#ifndef UTILS_VAD_INFERENCE_H
#define UTILS_VAD_INFERENCE_H

#include <memory>
#include <string>
#include <vector>

namespace Ort {
struct Env;
struct Session;
}  // namespace Ort

namespace libquranasr {

// Adapted from
// https://github.com/snakers4/silero-vad/blob/master/examples/cpp/silero-vad-onnx.cpp
class VADInference {
 public:
  VADInference(const std::string& model_path, int sample_rate, int context_ms,
               int min_silence_duration_ms, int speech_pad_ms, float threshold);
  ~VADInference();

  // Returns true if we can detect speech in the given audio samples.
  bool predict(const float* samples, int numframes);
  void reset();

 private:
  std::unique_ptr<Ort::Env> env_;
  std::unique_ptr<Ort::Session> session_;

  // Model config
  const int sample_rate_;
  const int sr_per_ms_;
  const float threshold_;
  const int64_t min_silence_samples_;
  const int64_t speech_pad_samples_;

  // Support 256 512 768 for 8k; 512 1024 1536 for 16k.
  const int64_t context_samples_;

  // Model states
  bool triggerd_ = false;
  int64_t speech_start_ = 0;
  int64_t speech_end_ = 0;
  int64_t temp_end_ = 0;
  int64_t current_sample_ = 0;

  // Hidden model states.
  std::vector<float> h_;
  std::vector<float> c_;

  VADInference(const VADInference&) = delete;             // no copy
  VADInference& operator=(const VADInference&) = delete;  // no copy
};

}  // namespace libquranasr

#endif  // UTILS_VAD_INFERENCE_H
