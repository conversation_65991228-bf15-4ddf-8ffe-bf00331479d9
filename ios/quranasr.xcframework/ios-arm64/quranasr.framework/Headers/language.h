#ifndef UTILS_LANGUAGE_H_
#define UTILS_LANGUAGE_H_

#include <string>
#include <string_view>
#include <vector>

namespace libquranasr {
/**
 * @brief Reduces phonetically identical characters or group of characters into
 * the same letter. If patterns in the map having matching starting characters
 * then the longest matching pattern is selected
 *
 * @param encoded_text
 * @return std::string
 */
std::string NormWithPhoneticMap(const std::string &encoded_text);

/**
 * @brief Converts an arabic text encoded by
 `//utils:language.encode_quran_text` back to arabic characters
 *
 * @param encoded_text Encoded arabic text in ascii
 * @return std::string The decoded arabic text
 */
std::string DecodeAscii2Arabic(std::string_view encoded_text);

/**
 * @brief Removed encoded tashkeel symbols from encoded text. Assumes that ۞ & ۩
 * are not in the text
 *
 * @param encoded_text Encoded arabic text in ascii
 * @return std::string The encoded text with tashkeel removed
 */
std::string RemoveDiacritics(std::string_view encoded_text);

// Standardizes the way quran text is split. the words in the corpus are
// expected to be separated by a `*` as some words that are split by space in
// imlaei text are treated as one word in uthmani.
std::vector<std::string_view> Tokenize(std::string_view ayah);

}  // namespace libquranasr
#endif  // UTILS_LANGUAGE_H_
