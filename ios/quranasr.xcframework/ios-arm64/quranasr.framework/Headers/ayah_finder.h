#ifndef UTILS_AYAH_FINDER_H_
#define UTILS_AYAH_FINDER_H_

#include <set>
#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include <climits>

#include "bk_tree.h"

#if defined(ANDROID) || defined(__ANDROID__)
#else
//#include "gtest/gtest_prod.h"
#endif

namespace libquranasr {
class QuranAyahFinder {
 public:
  static std::unique_ptr<QuranAyahFinder> createInstance(
      const std::vector<std::string> &encoded_verses);

  QuranAyahFinder() {}

  /**
   * Populates a BK tree with n-grams from the quran and builds a dict to map an
   * n-gram to ayah id.
   *
   * @param verses The encoded verses of the quran. The bk tree implemented does
   * not support unicode characters.
   * @param max_n The highest value for n in n-grams. Default includes all
   * n-grams.
   * @param min_n The lowest value for n in n-grams. n-grams smaller than min_n
   * can be added if they are the only map to a verse
   *
   * @return None
   */
  void BuildCorpus(const std::vector<std::string> &verses, int max_n = INT_MAX,
                   int min_n = 1);

  // Default search params
  static const size_t kTopResults = 3;
  static const int kNormThreshold = 3;
  static const int kNumChar = 10;

  /**
   * Searches the BK tree for matches to the sentence, retrieves the ayahs
   * of the matched n-grams in ascending order using levenshtein distance.
   * If multiple n-grams map to the same ayah, only the initial n-gram is
   * recorded with that ayah, so the size of the returned vector can be less
   * than `top_results`.
   *
   * @param sentence: a string to be matched. If sentence has more words than
   * the max n-grams the ayah finder was initialized with only the last max_n
   * words are used for the search
   * @param top_results: maximum number of best matches to return.
   * @param norm_threshold: the maximum number of operations (insertion,
   * deletion, and substitutions) per `num_char` characters to be applied to a
   * valid match to become `sentence`.
   * @param num_char: the number of characters `norm_threshold` is normalized
   * to. if zero, defaults to the length of the sentence.
   *
   * @return: a vector of pairs of the matched ayahs sorted by levenshtein
   *          distance in ascending order. The first element of the pair is
   *          ayah_id, the id of the matched ayah. the id is the verses. order
   *          in the quran (1-6236). The second element is the `matched_n_gram`:
   *          the part of the ayah matched to the input sentence.
   */
  std::vector<std::pair<int, std::string>> FindAyah(
      std::string_view sentence, size_t top_results = kTopResults,
      int norm_threshold = kNormThreshold, int num_char = kNumChar);

#if defined(ANDROID) || defined(__ANDROID__)
#else
  void TreeStatistics();
#endif

 private:
#if defined(ANDROID) || defined(__ANDROID__)
#else
//FRIEND_TEST(QuranAyahFinderTest, HandlesSeg2IdPopulation);
#endif

  size_t max_n_;
  // std::set -> ayah ids numerically ordered
  std::unordered_map<std::string, std::set<int>> ayah_seg2ayah_id_;
  BKTree bk_tree_;
  /**
   * Parses the verses and generates n-grams. The n-grams are stored in the
   * attribute `ayah_seg2ayah_id` where each is mapped to their respective
   * verse(s) id. The id is the verse's index + 1. The assumption is that the
   * verses in the vector are given in order as they appear in the quran.
   *
   * The tokenization method splits an verse by spaces, then any n-grams that
   * map to multiple ayahs are dropped unless that n-gram happens to be the only
   * way to map to that verse. In that case only the ids of verses that can only
   * be mapped to with the n-gram are kept.
   *
   * @param verses The encoded verses of teh quran. The bk tree implemented does
   * not support unicode characters.
   * @param max_n The highest value for n in n-grams. Default includes all
   * n-grams.
   * @param min_n The lowest value for n in n-grams. n-grams smaller than min_n
   * can be added if they are the only map to a verse.
   *
   * @return None
   */
  void PopulateAyahSeg2ayahId(const std::vector<std::string> &verses, int max_n,
                              int min_n);
  void AddSurahNamesIntoSearchIndex();
};

}  // namespace libquranasr
#endif  // UTILS_AYAH_FINDER_H_
