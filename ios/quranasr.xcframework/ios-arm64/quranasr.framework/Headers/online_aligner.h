#ifndef UTILS_ONLINE_ALIGNER_H_
#define UTILS_ONLINE_ALIGNER_H_

#include <map>
#include <string>
#include <vector>

#include "logging.h"
#include "bk_tree.h"
#include "ctc_beamsearch.h"

namespace libquranasr {

struct AyahPointer {
  int ayah_id;
  // within ayah id
  size_t word_idx;
  bool is_correct;
  bool is_complete;
  bool is_last_word;

  bool operator==(const AyahPointer& pointer2) const {
    return ayah_id == pointer2.ayah_id && word_idx == pointer2.word_idx &&
           is_correct == pointer2.is_correct &&
           is_complete == pointer2.is_complete;
  };
  bool operator>(const AyahPointer& pointer2) const {
    return ayah_id > pointer2.ayah_id ||
           (ayah_id == pointer2.ayah_id && word_idx > pointer2.word_idx);
  }
};

std::ostream& operator<<(std::ostream& os, const Ayah<PERSON>ointer& ptr);

class OnlineAligner {
 public:
  virtual ~OnlineAligner() = default;
  /**
   * @brief Ideally should be split into a latching and correction phases. In
   * the former the most likely word that is being read is identified. During
   * the second phase the aligner follows the reciter and catches incorrect
   * recitation.
   *
   * @param log_probs T x S vector, where T is the time stamps and S is num
   * vocab. generated by the streaming asr model. on subsequent iterations the
   * whole log_probs is assumed to be passed unless the model state is reset.
   * TODO: For optimization reasons, we may want to only pass the delta
   * log_probs to the aligner. log_probs array is relatively big.
   * @return AyahPointer
   */
  virtual AyahPointer Align(const std::string& transcription) = 0;

  /**
    * @brief Set the Cursor object.
    *
    * @param ayah_id
    * @param word_idx
    */
  virtual bool SetCursor(int ayah_id, size_t word_idx) = 0;

 protected:
  std::vector<std::string> verses_;
  float cer_th_;
};

/**
 * @brief Beams are used to be matched against the transcription to find the
 * most likely starting point and the current word the user is reciting.
 *
 */
struct Beam {
  int ayah_id;
  size_t word_idx;
  std::string string;
  // within verse[ayah_id-1][word_idx]
  size_t letter_idx;
  // Indicates if `string` contains the whole `verse[ayah_id-1][word_idx]`
  bool is_word_complete = false;
  bool is_correct = false;

  int init_ayah_id;
  size_t init_word_idx;
  // True if the beam starting from the first word of ayah and complete the last
  // word of the ayah
  bool is_ayah_complete = false;
  // Holds cer of each word of the beam once completed
  std::vector<float> cers;

  Beam(std::string string_, int ayah_id_, size_t word_idx_, size_t letter_idx_)
      : ayah_id(ayah_id_),
        word_idx(word_idx_),
        string(string_),
        letter_idx(letter_idx_),
        init_ayah_id(ayah_id_),
        init_word_idx(word_idx_) {}

  Beam(int ayah_id_, size_t word_idx_) : Beam("", ayah_id_, word_idx_, -1) {}

  void ResetLastWord();
  bool AddCER(float cer);
  float GetAvgCER();
};

/**
 * @brief Accumulates chars to candidate beams to keep track of possible matches
 * to current transcription. Assumes that the user will read the words in
 * sequence and that the model state will reset before any repetition or change
 * of starting point.
 *
 */
class AccumCharAligner : public OnlineAligner {
 public:
  /**
   * @brief Construct a new Accum Char Aligner object
   *
   * @param verses ayah strings in order, the referenced vector is not expected
   * to change
   * @param starting_ayah_id_range [start_ayah_id, end_ayah_id] inclusive
   * @param cer_th if the cer of the transcribed word is <= cer_th, the
   * pronunciation is considered correct. Candidate beams are ones with cer <=
   * cer_th + 0.2 against given transcription.
   */
  AccumCharAligner(const std::vector<std::string>& verses,
                   const std::pair<int, int>& starting_ayah_id_range,
                   float cer_th);

  /**
   * @brief When the method is called each `Beam` accumulates the next chars
   * following its starting word until its size matches the transcription then
   * the `Beam`s who's strings' cer with the transcription is less than
   * `cer_th_` + 0.2 are kept, this is the beam cer. the cer that is measured
   * against `cer_th_` is the cer between the last word of the beam (which can
   * be a substring of the word) and the last word of the transcription (split
   * by space). The `Beam` with the lowest cer is highlighted. if multiple beams
   * have the same cer the closest to the cursor is selected.
   *
   * If a beam completed its current word but the best beam the `cursor_` is
   * pointing to is still incorrect the beam is not accumulated on. This means
   * that the cursor will not move until the current word is correct.
   *
   * This method assumes that the user will read the words sequentially and any
   * repetition or switch will be preceded with a model state reset. During the
   * warm up stage no `Beam` is removed.
   *
   * @param log_probs T x S vector, where T is the time stamps and S is num
   * vocab. generated by the streaming asr model. on subsequent iterations the
   * whole log_probs is assumed to be passed unless the model state is reset.
   * TODO: For optimization reasons, we may want to only pass the delta
   * log_probs to the aligner. log_probs array is relatively big.
   * @return `AyahPointer` Must be correct to move to the next word
   */
  AyahPointer Align(const std::string& transcription) override;
  AyahPointer NextWord();

  void SetRecitationRange(const std::pair<int, int>& starting_ayah_id_range);

  /**
   * @brief Set the Cursor object.
   *
   * @param ayah_id
   * @param word_idx
   */
  bool SetCursor(int ayah_id, size_t word_idx) override;

  int Dist(int ayah_id1, size_t word_idx1, int ayah_id2, size_t word_idx2);
  // Depreciated and can be removed
  void IterateAndCall(const AyahPointer& pointer1, const AyahPointer& pointer2,
                      std::function<void(const AyahPointer& ptr)>);

 private:
  /**
   * @brief Accumulates letters to the beam's string while accounting for the
   * letter's position. If the letter is the last in the word move to the next
   * word and if the word is the last in the ayah move to the next ayah, etc.
   *
   * Adding space between two words is counted as 1 accumulation.
   *
   * @param beam Modified to accumulate num_chars chars (including space) and
   * represent the position of the last letter of the string
   * @param stop_on_completion If true the accumulation will stop when the
   * beam's current word is complete
   * @param num_chars The number of chars to be accumulated on beam (including
   * space)
   */
  void AccumOnBeam(int num_chars, bool stop_on_completion, Beam* beam);
  /**
   * @brief creates a `Beam` for each possible starting word from the starting
   * ayah till the `cursor_` if the `cursor_.is_correct == false` or until the
   * `cursor_` plus the next word if `cursor_.is_correct == true`. In other
   * words calling `ResetBeams()` means the user can start from any word till
   * the `cursor_` or they can also start from the word right after the
   * `cursor_` if `cursor_`'s word was recited correctly.
   *
   */
  void ResetBeams();
  /**
   * @brief Measures the absolute number of words between `cursor_` and `beam`
   *
   * @param beam
   * @return int
   */
  int DistToCursor(const Beam& beam);
  // TODO: doc
  std::string Decode(const std::vector<float>& log_probs);

  std::vector<Beam> beams_;
  float beam_th_;
  std::string prev_transcription_;
  std::vector<std::vector<float>> log_probs_;
  int starting_ayah_id_;
  int ending_ayah_id_;
  AyahPointer cursor_;
  std::vector<std::vector<std::string>> split_ayahs_;

  std::string rewritten_transcription_;
  std::string new_transcription_;
  size_t cur_char_idx_;

  CTCBeamSearchDecoder ctc_decoder_;
  std::vector<float> prev_log_probs;
};

}  // namespace libquranasr

#endif  // UTILS_ONLINE_ALIGNER_H_
