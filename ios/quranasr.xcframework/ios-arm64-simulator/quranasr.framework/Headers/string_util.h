#ifndef UTILS_STRING_UTIL_H
#define UTILS_STRING_UTIL_H

#include <string>
#include <string_view>
#include <vector>

namespace libquranasr {

std::vector<std::string_view> StrSplit(std::string_view input, char delimiter = ' ');

template<typename Iterator>
std::string StrJoin(Iterator begin, Iterator end, std::string_view delimiter) {
    if (begin == end) {
        return "";
    }

    // Reserve space for efficiency. Assumes 5 characters per element.
    std::string result;
    result.reserve(5 * std::distance(begin, end) * (delimiter.size() + 1));

    for (Iterator it = begin; it != end; ++it) {
        result.append(*it);
        if (std::next(it) != end) {
            result.append(delimiter);
        }
    }

    return result;
}

std::string StrJoin(const std::vector<std::string_view>& strings, std::string_view delimiter);

}  // namespace libquranasr

#endif  // UTILS_STRING_UTIL_H 
