#ifndef UTILS_AYAH_FINDER_SERVICE_H_
#define UTILS_AYAH_FINDER_SERVICE_H_

#include <functional>
#include <future>
#include <string>

#include "ayah_finder.h"

namespace libquranasr {

class QuranAyahFinderService {
 public:
  using OnMatchedVersesCallback = std::function<void(
      const std::vector<std::pair<int, std::string>>& matched_verses)>;

  QuranAyahFinderService(const std::vector<std::string>& encoded_verses)
      : encoded_verses_(encoded_verses), stop_thread_(false) {}

  void Start();
  void Stop();
  void FindAyahAsync(std::string sentence, OnMatchedVersesCallback cb);

 private:
  const std::vector<std::string>& encoded_verses_;
  std::unique_ptr<QuranAyahFinder> ayah_finder_;

  std::mutex mtx_;
  std::condition_variable cv_;
  std::unique_ptr<std::thread> ayah_finder_thread_;
  std::function<void()> ayah_finder_task_;
  std::atomic<bool> stop_thread_{false};
};

}  // namespace libquranasr
#endif  // UTILS_AYAH_FINDER_SERVICE_H_
