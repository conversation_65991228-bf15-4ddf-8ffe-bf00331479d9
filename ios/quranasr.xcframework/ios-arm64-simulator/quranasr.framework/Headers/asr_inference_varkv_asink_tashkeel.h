// TODO have the varkv asink model allow custom vocab

#ifndef UTILS_ASR_INFERENCE_VARKV_ASINK_TASHKEEL_H
#define UTILS_ASR_INFERENCE_VARKV_ASINK_TASHKEEL_H

#include <atomic>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

#ifdef BAZEL
#include "onnxruntime_c_api.h"
#include "onnxruntime_cxx_api.h"
#else
#include "onnxruntime/onnxruntime_c_api.h"
#include "onnxruntime/onnxruntime_cxx_api.h"
#endif
#include "asr_inference.h"
#include "cond_kv.h"

namespace libquranasr {

class ASRInferenceVarkvAsinkTashk : public ASRInferenceABC {
 public:
  ASRInferenceVarkvAsinkTashk(const std::string& model_path,
                              SpeechDetectorCallback speech_detector_callback,
                              OnInferenceResultCallback transcription_callback,
                              OnModelResetCallback reset_callback,
                              int64_t kv_layers, int64_t kv_head_dim,
                              int64_t kv_n_head, int64_t kv_sink_len,
                              int64_t kv_fifo_len, int64_t kv_zeros_len,
                              const std::string& cond_model_path = "");
  ~ASRInferenceVarkvAsinkTashk();

  // Blocking inference used for audio input from a file
  std::string infer(const float* features, size_t num_features) override;
  // Non blocking inference used for transcribing from a mic input
  void onAudioReady(const float* audio_data, size_t num_frames) override;

  // Rests KV cache, Causal Masking, and the running log probabilities of the
  // tokens. If a condition model is provided then the kvs will be conditioned
  // on the last passed conditioning text.
  void reset() override;

  // Not used..
  size_t getContextSamples() override { return 0; };
  size_t getStrideSamples() override { return 0; };

  bool condition(const std::string& text) override;

 private:
  void resetInternal();
  void trimKv(size_t ntokens);

  std::string getPredictedTranscription(const float* log_probs, size_t T,
                                        size_t S);

  std::unique_ptr<Ort::Env> env_;
  std::unique_ptr<Ort::MemoryInfo> memory_info_;
  std::unique_ptr<OrtAllocator> allocator_;
  std::unique_ptr<Ort::Session> session_;
  std::unique_ptr<Ort::IoBinding> io_binding_;

 protected:
  // KV cache
  size_t kv_zeros_len_;
  size_t kv_sink_len_;
  std::vector<int64_t> kvs_max_dim_;
  std::vector<int64_t> kvs_in_dim_;
  std::vector<int64_t> kvs_out_dim_;
  std::vector<float> kv_values_;

 private:
  std::string transcription_;
  std::vector<float> log_probs_;

  // The callback to run on each result after inference.
  OnInferenceResultCallback inference_callback_;

  // The callback to run when the model resets itself.
  OnModelResetCallback reset_callback_;

  // The speech detector that gates ASR inference.
  SpeechDetectorCallback speech_detector_;

  // The actual buffer that we use to run the transcription.
  std::vector<float> streaming_buffer_;
  std::vector<float> features_buffer_;

  // Indicates whether there is an ASR transcription thread that's already in
  // progress. We do not spawn a transcription thread if there's already one
  // running.
  std::atomic_bool asr_in_progress_{false};
  std::atomic_bool gate_on_speech_{true};

  // The last time the transcription changed.
  std::string last_transcription_;

  std::mutex mtx_;
  std::condition_variable cv_;
  std::function<void()> inference_task_;
  std::unique_ptr<std::thread> inference_thread_;
  std::atomic_bool stop_thread_{false};

  int silence_count_ = 0;

  ASRInferenceVarkvAsinkTashk(const ASRInferenceVarkvAsinkTashk&) =
      delete;  // no copy
  ASRInferenceVarkvAsinkTashk& operator=(const ASRInferenceVarkvAsinkTashk&) =
      delete;  // no copy

  std::unique_ptr<libquranasr::CondKv> cond_kv_;
  // Indicates weather the cond kv model was loaded or not
  bool conditioning_ = false;
  // If true the next infer call will be preceded by conditioning the model on
  // cond_text_ using cond_kv_. Can be set to true if conditioning_ &&
  // !cond_text_.empty()
  bool cond_on_text_ = false;

  std::string cond_text_;

  // TODO: hard coded feature dim 512
  int feature_dim_ = 512;

  // Used to safely clear all buffers
  std::atomic_bool clear_buffers_{false};
};

}  // namespace libquranasr

#endif  // UTILS_ASR_INFERENCE_VARKV_ASINK_TASHKEEL_H
