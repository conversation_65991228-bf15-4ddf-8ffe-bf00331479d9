#ifndef HB_VAD_H
#define HB_VAD_H

#ifdef BAZEL
#include "onnxruntime_c_api.h"
#include "onnxruntime_cxx_api.h"
#else
#include "onnxruntime/onnxruntime_c_api.h"
#include "onnxruntime/onnxruntime_cxx_api.h"
#endif

namespace libquranasr {
class HBVAD {
 public:
  HBVAD(const std::string& model_path, int64_t last_layer_stride);
  ~HBVAD();

  // Returns true if we can detect speech in the given audio samples, and the feature encoder output
  std::pair<bool, std::vector<float>> predict(const float* samples,
                                              int num_frames);
  void reset();

 private:
  std::unique_ptr<Ort::Env> env_;
  std::unique_ptr<Ort::MemoryInfo> memory_info_;
  std::unique_ptr<OrtAllocator> allocator_;
  std::unique_ptr<Ort::Session> session_;
  std::unique_ptr<Ort::IoBinding> io_binding_;

  int64_t last_layer_stride_;
  /**
   *
   * @brief Based on the convolutions of [(10, 5)] + [(3, 2)] * 4 + [(2, 2)] +
   * [(2, stride)] where (kernel, stride) The following formula was derived to
   * calculate the number of tokens outputted given the number of samples:
   * `num_tokens = (num_samples - 400)/(160*stride) + 1`
   *
   * Calculation:
   * https://drive.google.com/file/d/13v6s9tWnYepVtz1VVicv8zFkuOYEElK0/view?usp=drive_link
   *
   * @param num_frames the number of samples passed to the model
   * @return size_t the output will discard any fractions
   */
  size_t getNumOutputTokens(size_t num_frames);

  HBVAD(const HBVAD&) = delete;             // no copy
  HBVAD& operator=(const HBVAD&) = delete;  // no copy
};
}  // namespace libquranasr
#endif