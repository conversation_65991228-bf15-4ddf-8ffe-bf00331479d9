#ifndef UTILS_INFERENCE_PIPELINE_H_
#define UTILS_INFERENCE_PIPELINE_H_

#include <vector>

#include "asr_inference.h"
#include "ayah_finder_service.h"
#include "online_aligner.h"
#include "hb_vad.h"

namespace libquranasr {

class AudioInput;
class ASRInferenceABC;

class InferencePipeline {
 public:
  using ASRBuilder = std::function<std::unique_ptr<ASRInferenceABC>(
      SpeechDetectorCallback speech_detector_callback,
      OnInferenceResultCallback inference_callback,
      OnModelResetCallback reset_callback)>;

  using OnListeningCallback = std::function<void(bool is_listening)>;
  using OnSpeechCallback = std::function<void(bool is_speech)>;
  using OnTranscriptionResultCallback =
      std::function<void(const std::string& transcription)>;
  using OnMatchedVersesCallback =
      std::function<void(const std::vector<int>& matched_verses)>;
  using OnAlignmentResultCallback = std::function<void(
      int ayah_id, size_t word_idx, bool is_correct, bool is_complete)>;

  InferencePipeline(std::unique_ptr<libquranasr::AudioInput> audio_input,
                    const std::string vad_model_path, int lastLayerStride,
                    const std::vector<std::string>& encoded_verses,
                    ASRBuilder asr_builder);
  ~InferencePipeline();

  float TestCompatibility();

  bool Align(int start_ayah_id, int end_ayah_id,
             OnListeningCallback onlistening_cb, OnSpeechCallback speech_cb,
             OnTranscriptionResultCallback transcription_cb,
             OnAlignmentResultCallback alignment_cb);
  bool SetAlignerCursor(int ayah_id, size_t word_idx);

  bool RecognizeVerse(OnListeningCallback onlistening_cb,
                      OnSpeechCallback speech_cb,
                      OnTranscriptionResultCallback transcription_cb,
                      OnMatchedVersesCallback matched_verses_cb);
  bool Stop();
  bool Join();
  bool Reset();

  std::string condition(int ayah_id, size_t word_idx = 0);

 private:
  OnListeningCallback onlistening_cb_;
  OnSpeechCallback speech_cb_;
  OnTranscriptionResultCallback transcription_cb_;
  OnAlignmentResultCallback alignment_cb_;
  OnMatchedVersesCallback matched_verses_cb_;

  std::unique_ptr<libquranasr::AudioInput> audio_input_;
  std::unique_ptr<libquranasr::HBVAD> vad_;
  std::unique_ptr<libquranasr::ASRInferenceABC> asr_;
  std::unique_ptr<libquranasr::QuranAyahFinderService,
                  void (*)(libquranasr::QuranAyahFinderService*)>
      ayah_finder_ = {nullptr, [](auto a) {}};
  std::unique_ptr<libquranasr::AccumCharAligner> aligner_;

  const std::vector<std::string>& encoded_verses_;
  libquranasr::AyahPointer ayah_pointer_{-1};

  // If true, then we are in verse recognition mode, otherwise, in online
  // alignment mode.
  std::atomic<bool> vr_mode_{true};

  InferencePipeline(const InferencePipeline&) = delete;             // no copy
  InferencePipeline& operator=(const InferencePipeline&) = delete;  // no copy
};

}  // namespace libquranasr

#endif  // UTILS_INFERENCE_PIPELINE_H_
