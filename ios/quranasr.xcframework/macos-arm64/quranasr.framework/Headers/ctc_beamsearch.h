#ifndef UTILS_CTC_BEAMSEARCH_H_
#define UTILS_CTC_BEAMSEARCH_H_

#include <stdexcept>
#include <string>
#include <vector>
namespace libquranasr {

/**
 * @brief Holds information and logic of a beam instance
 *
 */
class BeamEntry {
 public:
  // which of those can be a reference
  /**
   * @brief Construct a new Beam Entry object
   *
   * @param collapsed_str the collapsed path of the beam
   * @param p_no_blank probability of getting the collapsed prefix from paths
        ending with a non-blank
   * @param p_blank probability of getting the collapsed prefix from paths
        ending with a blank
   */
  BeamEntry(std::string collapsed_str, float p_no_blank, float p_blank)
      : collapsed_str_(collapsed_str),
        p_no_blank_(p_no_blank),
        p_blank_(p_blank) {}

  void SetP(float p_no_blank, float p_blank) {
    p_no_blank_ = p_no_blank;
    p_blank_ = p_blank;
  }
  void Extend(char new_char, float p_no_blank, float p_blank) {
    collapsed_str_.append(1, new_char);
    SetP(p_no_blank, p_blank);
  }

  /**
   * @brief accumulates the probabilities of beam. result is the probability
   * of this or beam
   *
   * @param beam
   */
  void AccumP(const BeamEntry& beam);

  float GetPNoBlank() const { return p_no_blank_; }
  float GetPBlank() const { return p_blank_; }
  /**
   * @brief Returns the total probability of beam
   *
   */
  float GetTotalScore() const { return GetPNoBlank() + GetPBlank(); }

  const std::string& GetCollapsedStr() const { return collapsed_str_; }
  char Back() const { return collapsed_str_.back(); }
  bool IsStrMatch(const BeamEntry& beam) {
    return GetCollapsedStr() == beam.GetCollapsedStr();
  }

  bool operator>(const BeamEntry& beam) const {
    return GetTotalScore() > beam.GetTotalScore();
  }
  bool operator<(const BeamEntry& beam) const {
    return GetTotalScore() < beam.GetTotalScore();
  }

 private:
  std::string collapsed_str_;
  float p_no_blank_;
  float p_blank_;
};

std::ostream& operator<<(std::ostream& os, const BeamEntry& ptr);

/**
 * @brief Handles the logic needed to manage a list of beams
 *
 */
class BeamList {
 public:
  void Clear() { beams_.clear(); }
  /**
   * @brief Creates a beam instance and appends it if p_no_blank + p_blank != 0
   *
   */
  const BeamEntry AddBeamFromPrefix(const std::string& collapsed_str,
                                    float p_no_blank, float p_blank);
  /**
   * @brief Sorts beams in dsc order by score
   *
   */
  void SortBeams();
  /**
   * @brief Keeps beam_width beams with the highest score
   *
   */
  std::vector<BeamEntry> GetBestBeams(size_t beam_width);

  /**
   * @brief returns the `top_paths` top transcription is descending order, and
   * their total probability
   *
   * @param top_paths
   * @return std::tuple<std::vector<std::string>, std::vector<float>>
   */
  std::tuple<std::vector<std::string>, std::vector<float>>
  GetBestTranscriptions(size_t top_paths);
  /**
   * @brief Merges all beams of the same collapse and aggregates their
   * probabilities
   *
   */
  void MergeBeams();

  // TODO: we said that if we will modify an object then we should pass its
  // pointer. is this doable here?
  std::vector<BeamEntry>& GetVector() { return beams_; }

 private:
  std::vector<BeamEntry> beams_;
};

/**
 * @brief CTC Beamsearch Decoder with incremental decoding.
 *
 * the class is designed to apply CTC Beamsearch Decoding one time step at a
 * time using the `decode` method. The class can also decode multiple time steps
 * at once using the `decode_multiple_steps` method.
 */
class CTCBeamSearchDecoder {
 public:
  CTCBeamSearchDecoder(std::string vocab, int beam_width, size_t top_paths,
                       size_t blank_idx)
      : vocab_(vocab),
        beam_width_(beam_width),
        top_paths_(top_paths),
        blank_idx_(blank_idx) {
    Reset();
  };
  /**
   * @brief clears the beam list and initializes it with an empty string beam
   *
   */
  void Reset();
  /**
   * @brief Returns the top_paths most likely outputs with their probabilities
   * using ctc beam search for one time step.
   *
   * @param log_probs T x S vector, where T is the time stamps and S is num
   * vocab. Holds a sequence of probabilities for each character including the
   * blank token.
   * @return std::tuple<std::vector<std::string>, std::vector<float>> a list of
   * possible texts sorted by likelihood in descending order and their
   * probability
   */
  std::tuple<std::vector<std::string>, std::vector<float>> Decode(
      const std::vector<float>& log_probs);

 private:
  std::string vocab_;
  int beam_width_;
  size_t top_paths_;
  size_t blank_idx_;
  BeamList beam_list_;
};
}  // namespace libquranasr

#endif  // UTILS_CTC_BEAMSEARCH_H_
