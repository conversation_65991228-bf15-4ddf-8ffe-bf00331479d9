#ifndef UTILS_ASR_INFERENCE_H
#define UTILS_ASR_INFERENCE_H

#include <functional>
#include <string>

namespace libquranasr {

using SpeechDetectorCallback =
    std::function<std::pair<bool, std::vector<float>>(const float* buffer, int64_t numframes)>;
// TODO: Best to keep the interface between asr_inference and upstream tasks
// simpler. I'm thinking just pass log_probs and have the decoder by a separate
// block.
using OnInferenceResultCallback = std::function<void(
    const std::string& transcription, const std::vector<float> log_probs)>;

using OnModelResetCallback = std::function<void()>;

class ASRInferenceABC {
 public:
  virtual ~ASRInferenceABC() = default;

  // Blocking inference used for audio input from a file
  virtual std::string infer(const float* audio_data, size_t num_frames) = 0;
  // Non blocking inference used for transcribing from a mic input
  virtual void onAudioReady(const float* audio_data, size_t num_frames) = 0;

  virtual size_t getContextSamples() = 0;
  virtual size_t getStrideSamples() = 0;

  virtual bool condition(const std::string& text) = 0;

  // Reset the state of the model
  virtual void reset() = 0;
};

}  // namespace libquranasr

#endif  // UTILS_ASR_INFERENCE_H
