#ifndef UTILS_AUDIO_INPUT_H
#define UTILS_AUDIO_INPUT_H

#include <functional>
#include <vector>

namespace libquranasr {

// This callback may be called at interrupt level on some machines, so don't
// do anything that could mess up the system like calling malloc() or free().
using OnAudioReadyCallback =
    std::function<bool(const float* buffer, int numframes)>;

class AudioInput {
 public:
  virtual ~AudioInput() = default;

  virtual void SetDataCallback(OnAudioReadyCallback cb) = 0;

  // Returns false if the stream() is already opened.
  virtual bool Stream() = 0;
  virtual bool CloseStream() = 0;
  virtual bool IsStreaming() = 0;

  // Blocks until the stream is closed and all the audio thus far has been
  // processed.
  virtual bool Join() = 0;
};

}  // namespace libquranasr

#endif  // UTILS_AUDIO_INPUT_H
