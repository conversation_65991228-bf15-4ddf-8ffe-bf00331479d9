#ifndef UTILS_MINIAUDIO_INPUT_H
#define UTILS_MINIAUDIO_INPUT_H

#include <cstdint>
#include <functional>

#if defined(__APPLE__)
#include <miniaudio/miniaudio.h>
#elif BAZEL
#include "third_party/miniaudio/miniaudio.h"
#else
#include "miniaudio.h"
#endif
#include "audio_input.h"

namespace libquranasr {

class MiniAudioInput : public AudioInput {
 public:
  MiniAudioInput(int sample_rate, int frames_per_buffer);
  ~MiniAudioInput();

  void SetDataCallback(OnAudioReadyCallback cb) override { callback_.swap(cb); }

  bool Stream() override;
  bool CloseStream() override;
  bool IsStreaming() override;

  bool Join() override;

 private:
  void Callback(const void* input_buffer, uint32_t frame_count) {
    callback_(reinterpret_cast<const float*>(input_buffer), frame_count);
  }

  static void data_callback(ma_device* pDevice, void* pOutput,
                            const void* pInput, ma_uint32 frameCount) {
    reinterpret_cast<MiniAudioInput*>(pDevice->pUserData)
        ->Callback(pInput, frameCount);
  }

  int sample_rate_;
  int frames_per_buffer_;
  OnAudioReadyCallback callback_;

  ma_device device_;
  // TODO: Make it atomic.
  bool is_streaming_ = false;
};

}  // namespace libquranasr

#endif
