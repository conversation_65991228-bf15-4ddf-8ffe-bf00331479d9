#ifndef UTILS_BK_TREE_H_
#define UTILS_BK_TREE_H_

#include <map>
#include <memory>
#include <string>
#include <string_view>
#include <vector>

namespace libquranasr {
/**
 * Represents a node in the BK-tree.
 * Each node stores a word and a collection of children nodes.
 * The children are stored as a mapping of the distance from the word in the
 * node to the word in the child node.
 */
struct Node {
  std::string word;
  std::map<int, std::unique_ptr<Node>> dist2child;
  Node(const std::string &word) : word(word) {}
};

/**
 * @brief The BKTree class represents a BKTree data structure.
 *
 * A BKTree is a tree data structure that allows for efficient fuzzy searching
 * of strings.
 */
class BKTree {
 public:
  BKTree() : root_(nullptr) {}
  /**
   * @brief Build the BKTree by inserting a list of words into the tree.
   *
   * @param words The list of words to insert into the BKTree.
   */
  void BuildTree(const std::vector<std::string> &words);
  /**
   * @brief Insert a new word into the BKTree.
   *
   * @param word The word to insert into the BKTree.
   */
  void Insert(const std::string &word);

  /**
   * Search the BKTree for words within a certain maximum distance of the input
   * word.
   *
   * This method searches the BKTree for words that are within a certain maximum
   * distance of the input word. The method returns a vector of pairs
   * representing the words in the BKTree that are within the specified maximum
   * distance of the input word, sorted in ascending order by distance.
   *
   * @param query The word to search for in the BKTree.
   * @param top_results: the number of best matches to return.
   * @param threshold The maximum allowed distance between the search word and
   *                  the words in the BKTree.
   *
   * @return A vector of words from the BKTree of size top_results that are
   *         within the specified threshold of the input word and have the
   *         lowest distances, sorted in ascending order by distance.
   */
  std::vector<std::string> Search(std::string_view query, size_t top_results,
                                 int threshold) const;

  void TreeStatistics();

 private:

  std::unique_ptr<Node> root_;
};
}  // namespace libquranasr
#endif  // UTILS_BK_TREE_H_
