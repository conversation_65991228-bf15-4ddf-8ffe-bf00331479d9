#ifndef UTILS_COND_KV_H
#define UTILS_COND_KV_H

#include <string>
#include <vector>

#ifdef BAZEL
#include "onnxruntime_c_api.h"
#include "onnxruntime_cxx_api.h"
#else
#include "onnxruntime/onnxruntime_c_api.h"
#include "onnxruntime/onnxruntime_cxx_api.h"
#endif

namespace libquranasr {

class CondKv {
 public:
  CondKv(const std::string& model_path, const std::string& labels,
         int64_t kv_layers, int64_t kv_head_dim, int64_t kv_n_head);
  void infer(const std::string& text, std::vector<float>* kv);

 protected:
  std::vector<int64_t> kvs_out_dim_;
  int64_t kvs_out_size_;

 private:
  std::vector<int32_t> Text2Encoding(const std::string& text);

  std::unique_ptr<Ort::Env> env_;
  std::unique_ptr<Ort::MemoryInfo> memory_info_;
  std::unique_ptr<OrtAllocator> allocator_;
  std::unique_ptr<Ort::Session> session_;
  std::unique_ptr<Ort::IoBinding> io_binding_;

  std::string labels_;
};

}  // namespace libquranasr

#endif  // UTILS_COND_KV_H
