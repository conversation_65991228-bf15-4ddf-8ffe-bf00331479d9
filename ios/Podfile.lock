PODS:
  - asset_delivery (0.0.1):
    - Flutter
  - audio_service (0.0.1):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - cloud_firestore (5.6.4):
    - Firebase/Firestore (= 11.8.0)
    - firebase_core
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - disk_space_2 (0.0.1):
    - Flutter
  - facebook_app_events (0.20.0):
    - FBSDKCoreKit (~> 18.0)
    - Flutter
  - FBAEMKit (18.0.1):
    - FBSDKCoreKit_Basics (= 18.0.1)
  - FBSDKCoreKit (18.0.1):
    - FBAEMKit (= 18.0.1)
    - FBSDKCoreKit_Basics (= 18.0.1)
  - FBSDKCoreKit_Basics (18.0.1)
  - Firebase/Analytics (11.8.0):
    - Firebase/Core
  - Firebase/Auth (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.8.0)
  - Firebase/Core (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.8.0)
  - Firebase/CoreOnly (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - Firebase/Crashlytics (11.8.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.8.0)
  - Firebase/Firestore (11.8.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.8.0)
  - firebase_analytics (11.4.3):
    - Firebase/Analytics (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.5.0):
    - Firebase/Auth (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_core (3.12.0):
    - Firebase/CoreOnly (= 11.8.0)
    - Flutter
  - firebase_crashlytics (4.3.3):
    - Firebase/Crashlytics (= 11.8.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.8.0):
    - FirebaseAnalytics/AdIdSupport (= 11.8.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.8.1):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.8.0):
    - FirebaseCoreInternal (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseCoreInternal (11.8.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseFirestore (11.8.0):
    - FirebaseFirestoreBinary (= 11.8.0)
  - FirebaseFirestoreAbseilBinary (1.2024011602.0)
  - FirebaseFirestoreBinary (11.8.0):
    - FirebaseCore (= 11.8.0)
    - FirebaseCoreExtension (= 11.8.0)
    - FirebaseFirestoreInternalBinary (= 11.8.0)
    - FirebaseSharedSwift (= 11.8.0)
  - FirebaseFirestoreGRPCBoringSSLBinary (1.65.1)
  - FirebaseFirestoreGRPCCoreBinary (1.65.1):
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCBoringSSLBinary (= 1.65.1)
  - FirebaseFirestoreGRPCCPPBinary (1.65.1):
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCCoreBinary (= 1.65.1)
  - FirebaseFirestoreInternalBinary (11.8.0):
    - FirebaseCore (= 11.8.0)
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCCPPBinary (= 1.65.1)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.8.0)
  - Flutter (1.0.0)
  - flutter_isolate (0.0.1):
    - Flutter
  - GoogleAppMeasurement (11.8.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.8.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.8.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (4.5.0)
  - integration_test (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
  - leveldb-library (1.22.6)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RecaptchaInterop (100.0.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.47.2):
    - sqlite3/common (= 3.47.2)
  - sqlite3/common (3.47.2)
  - sqlite3/dbstatvtab (3.47.2):
    - sqlite3/common
  - sqlite3/fts5 (3.47.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.47.2):
    - sqlite3/common
  - sqlite3/rtree (3.47.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - sqlite3 (~> 3.47.0)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - Stripe (23.27.6):
    - StripeApplePay (= 23.27.6)
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripePaymentsUI (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 23.27.0)
    - StripeApplePay (~> 23.27.0)
    - StripeFinancialConnections (~> 23.27.0)
    - StripePayments (~> 23.27.0)
    - StripePaymentSheet (~> 23.27.0)
    - StripePaymentsUI (~> 23.27.0)
  - StripeApplePay (23.27.6):
    - StripeCore (= 23.27.6)
  - StripeCore (23.27.6)
  - StripeFinancialConnections (23.27.6):
    - StripeCore (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - StripePayments (23.27.6):
    - StripeCore (= 23.27.6)
    - StripePayments/Stripe3DS2 (= 23.27.6)
  - StripePayments/Stripe3DS2 (23.27.6):
    - StripeCore (= 23.27.6)
  - StripePaymentSheet (23.27.6):
    - StripeApplePay (= 23.27.6)
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripePaymentsUI (= 23.27.6)
  - StripePaymentsUI (23.27.6):
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - StripeUICore (23.27.6):
    - StripeCore (= 23.27.6)
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - asset_delivery (from `.symlinks/plugins/asset_delivery/ios`)
  - audio_service (from `.symlinks/plugins/audio_service/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - disk_space_2 (from `.symlinks/plugins/disk_space_2/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `11.8.0`)
  - Flutter (from `Flutter`)
  - flutter_isolate (from `.symlinks/plugins/flutter_isolate/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/ios`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseFirestoreAbseilBinary
    - FirebaseFirestoreBinary
    - FirebaseFirestoreGRPCBoringSSLBinary
    - FirebaseFirestoreGRPCCoreBinary
    - FirebaseFirestoreGRPCCPPBinary
    - FirebaseFirestoreInternalBinary
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - sqlite3
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore

EXTERNAL SOURCES:
  asset_delivery:
    :path: ".symlinks/plugins/asset_delivery/ios"
  audio_service:
    :path: ".symlinks/plugins/audio_service/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  disk_space_2:
    :path: ".symlinks/plugins/disk_space_2/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.8.0
  Flutter:
    :path: Flutter
  flutter_isolate:
    :path: ".symlinks/plugins/flutter_isolate/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/ios"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.8.0

SPEC CHECKSUMS:
  asset_delivery: 3eb62d59cb6621c7ca73c85c89b97f8535f34d38
  audio_service: 2023a4a1bdb2fd1443e7b00bdbdb1baa321525db
  audio_session: f08db0697111ac84ba46191b55488c0563bb29c6
  cloud_firestore: 7a6d7b8111fe44d13a0e49783daad5219ce798ff
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  disk_space_2: 2de4ee49f04eaf246d5bc123726977b55d972594
  facebook_app_events: cfa07e73912a052992b5f218f79a19a66a99bf1a
  FBAEMKit: b2ed182002dbcb65d5a60059c9693d322186cd00
  FBSDKCoreKit: 7f96852f2539bdb88ba8d47e5ab4ae70a6f8d691
  FBSDKCoreKit_Basics: 22d4c1a509ded4e45116f3bb167a14907550f62e
  Firebase: d80354ed7f6df5f9aca55e9eb47cc4b634735eaf
  firebase_analytics: d9a47ad8168f1da7bab57cd1400833d2bc48a043
  firebase_auth: c028e89b64d87f04708ed01f0e7691d997b3fa0d
  firebase_core: 6cbed78b4f298ed103a9fd034e6dbc846320480f
  firebase_crashlytics: eb5eb0ef5e6910395adfe177b9ca4a62e8a2f1aa
  FirebaseAnalytics: 4fd42def128146e24e480e89f310e3d8534ea42b
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: ad59a1a7b161e75f74c39f70179d2482d40e2737
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: 861681f012101000fba3c2a321ed00181d257591
  FirebaseCoreExtension: 3d3f2017a00d06e09ab4ebe065391b0bb642565e
  FirebaseCoreInternal: df24ce5af28864660ecbd13596fc8dd3a8c34629
  FirebaseCrashlytics: a1102c035f18d5dd94a5969ee439c526d0c9e313
  FirebaseFirestore: c3c9dfb2e2ab96bd74a42804bbefb3a7edd32c88
  FirebaseFirestoreAbseilBinary: fa2ebd2ed02cadef5382e4f7c93f1b265c812c85
  FirebaseFirestoreBinary: 072a7121be7fbe601733a039c5ed80936ffb1e9b
  FirebaseFirestoreGRPCBoringSSLBinary: d86ebbe2adc8d15d7ebf305fff7d6358385327f8
  FirebaseFirestoreGRPCCoreBinary: 472bd808e1886a5efb2fd03dd09b98d34641a335
  FirebaseFirestoreGRPCCPPBinary: db76d83d2b7517623f8426ed7f7a17bad2478084
  FirebaseFirestoreInternalBinary: 4695c807651ee9775867026ea85712b3442995f9
  FirebaseInstallations: 6c963bd2a86aca0481eef4f48f5a4df783ae5917
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: c4d40a97f88f9eaff2834d61b4fea0a522d62123
  FirebaseSharedSwift: 672954eac7b141d6954fab9a32d45d6b1d922df8
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_isolate: ca1a284bbb8dcfeb26c4764794e0206f58ffa984
  GoogleAppMeasurement: fc0817122bd4d4189164f85374e06773b9561896
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: fc75fc972958dceedee61cb662ae1da7a83a91cf
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  just_audio: 6c031bb61297cf218b4462be616638e81c058e97
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  share_plus: 011d6fb4f9d2576b83179a3a5c5e323202cdabcf
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 44bb54cc302bff1fbe5752293aba1820b157cf1c
  sqlite3: 7559e33dae4c78538df563795af3a86fc887ee71
  sqlite3_flutter_libs: 4dd5d78cb764bc095baa5af79dcee907d05a21a1
  Stripe: 9fec845645e39f371e6898926d096fd9c2feb5a5
  stripe_ios: 5accba1ff472f89300e1676c351166f29dc93ced
  StripeApplePay: 5f017e8dfe259fafbab70137776189deef754bb2
  StripeCore: 01ec57f0dddfe742054dc6a322f811426c25313d
  StripeFinancialConnections: 56698cb6274bf89fb8c76b934f6156f368e97765
  StripePayments: 6adf11faf1b7038e77aa97019410305c6adca79d
  StripePaymentSheet: 3eaf870c4388e44b0cc37e4c69d00b6957fd8bd7
  StripePaymentsUI: 59ccddeacad592b09fa67e8d641340820ddb4751
  StripeUICore: 879bbf5889265db13f52fac8aad7a176ba62481f
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  wakelock_plus: fd58c82b1388f4afe3fe8aa2c856503a262a5b03

PODFILE CHECKSUM: 2db0ac5ad493795e82aa3d3b3f2e138e2f7d2ecb

COCOAPODS: 1.16.2
