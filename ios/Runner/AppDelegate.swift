import UIKit
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {

    let systemChannelName = "my.huda.quranapp/systemChannel"

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        let nativeChannel = FlutterMethodChannel(name: systemChannelName, binaryMessenger: controller.binaryMessenger)
            .setMethodCallHandler({
                [weak self] (call: FlutterMethodCall, result: FlutterResult) -> Void in
                let isMobileMethodName = "isMobile"

                guard call.method == isMobileMethodName else {
                    result(FlutterMethodNotImplemented)
                    return
                }

                let isIPhone = UIDevice.current.userInterfaceIdiom == .phone
                result(isIPhone)
            })

        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
}
