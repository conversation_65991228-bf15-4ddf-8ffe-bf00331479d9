#!/bin/bash

set -x -e

# Set the path to the bundletool jar file
BUNDLETOOL_JAR=$(pwd)/bundletool-all-1.15.4.jar

# Get the list of devices
DEVICES=$(adb devices | sed "1d" | grep "device" | awk '{print $1;}')

# Print the list of devices
echo -e "Device list:\nDEVICES"

echo "Building development bundle..."
cd android
./gradlew bundleDevelopmentDebug
cd ..

echo "Generating apks file..."
java -jar "$BUNDLETOOL_JAR" build-apks --bundle="$(pwd)"/build/app/outputs/bundle/developmentDebug/app-development-debug.aab --output=quranapp.apks --local-testing --overwrite

for device in $DEVICES; do
  echo "Installing the apks for $device..."
  java -jar "$BUNDLETOOL_JAR" install-apks --apks=quranapp.apks --device-id="$device"

  echo "Running on $device..."
  adb -s "$device" shell am start -n my.huda.quranapp.dev/my.huda.quranapp.MainActivity
done

