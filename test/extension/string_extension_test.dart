import 'package:flutter_test/flutter_test.dart';
import 'package:mushafi/extension/string_extension.dart';

void main() {
  test('numerics string check, the result must be true', () {
    const string = '12345';
    expect(string.isNumericsOnly(), true);
  });

  test('negative numerics string check, the result must be true', () {
    const string = '-6789';
    expect(string.isNumericsOnly(), true);
  });

  test('numerics with dot string check, the result must be false', () {
    const string = '48912.52';
    expect(string.isNumericsOnly(), false);
  });

  test('text string check, the result must be false', () {
    const string = 'Lorem ipsum';
    expect(string.isNumericsOnly(), false);
  });

  test('empty string check, the result must be false', () {
    const string = '';
    expect(string.isNumericsOnly(), false);
  });
}
