import 'package:flutter_test/flutter_test.dart';
import 'package:mushafi/extension/juz_number_formatter.dart';
import 'package:mushafi/utils/constants.dart';

void main() {
  group('toJuzNumberCharCode test', () {
    test('toJuzNumberCharCode returns the correct character code', () {
      expect(1.toJuzNumberCharCode(), String.fromCharCode(juzCharCode + 1));
      expect(2.toJuzNumberCharCode(), String.fromCharCode(juzCharCode + 2));
      expect(3.toJuzNumberCharCode(), String.fromCharCode(juzCharCode + 3));
    });
  });
}
