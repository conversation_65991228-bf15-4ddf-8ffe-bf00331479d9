import 'package:flutter_test/flutter_test.dart';
import 'package:mushafi/extension/verse_key_parser.dart';

void main() {
  group('Test surah number parsing from verse key', () {
    test(
        'parsing from numeric string with colon in the middle, the result should be more than -1',
        () {
      const verseKey = '20:5';
      expect(verseKey.parseSurahNumber(), 20);
    });

    test(
        'parsing from numeric string without colon, the result should be more than -1',
        () {
      const verseKey = '8';
      expect(verseKey.parseSurahNumber(), 8);
    });

    test('parsing from text string, the result should be -1', () {
      const verseKey = 'Lorem ipsum';
      expect(verseKey.parseSurahNumber(), -1);
    });

    test('parsing from empty string, the result should be -1', () {
      const verseKey = '';
      expect(verseKey.parseSurahNumber(), -1);
    });
  });

  group('Test verse number parsing from verse key', () {
    test(
        'parsing from numeric string with colon in the middle, the result should be more than -1',
        () {
      const verseKey = '18:1';
      expect(verseKey.parseVerseNumber(), 1);
    });

    test('parsing from numeric string without colon, the result should be -1',
        () {
      const verseKey = '105';
      expect(verseKey.parseVerseNumber(), -1);
    });

    test('parsing from text string, the result should be -1', () {
      const verseKey = 'Lorem ipsum';
      expect(verseKey.parseVerseNumber(), -1);
    });

    test('parsing from empty string, the result should be -1', () {
      const verseKey = '';
      expect(verseKey.parseVerseNumber(), -1);
    });
  });
}
