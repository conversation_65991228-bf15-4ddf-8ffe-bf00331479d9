import 'package:flutter_test/flutter_test.dart';
import 'package:mushafi/extension/integer_extension.dart';

void main() {
  group('threeDigitsFormat test', () {
    test('threeDigitsFormat pads with zeros to a minimum of 3 digits', () {
      expect(1.threeDigitsFormat(), '001');
      expect(12.threeDigitsFormat(), '012');
      expect(123.threeDigitsFormat(), '123');
    });

    test('threeDigitsFormat does not truncate numbers with more than 3 digits',
        () {
      expect(1234.threeDigitsFormat(), '1234');
    });
  });
}
