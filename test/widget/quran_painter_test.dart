import 'package:flutter/cupertino.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_data.dart';
import 'package:mushafi/utils/quran_image_helper.dart';
import 'package:mushafi/widget/quran_painter.dart';

void main() {
  testGoldens('Quran painter shows correctly', (tester) async {
    final devices = [
      const Device(
        name: 'Pixel 7 Pro',
        size: Si<PERSON>(1440, 3120),
      ),
      Device.phone,
      Device.iphone11,
      Device.tabletPortrait
    ];

    final asset = await tester.runAsync(() {
      return QuranImageHelper().getQuranThemeAsset();
    });
    final canvasData = QuranCanvasData(asset!);

    final quranPaint = CustomPaint(
      size: const Size(
        double.infinity,
        double.infinity,
      ),
      painter: <PERSON><PERSON>ain<PERSON>(canvasData),
    );

    await tester.pumpWidgetBuilder(quranPaint);
    await multiScreenGolden(
      tester,
      'quran_painter_scenarios',
      devices: devices,
      deviceSetup: (device, tester) async {
        canvasData.calculateDimension(device.size.width, device.size.height);
      },
    );
  });
}
