# mushafi

## Prerequisites

* Flutter SDK ([Download link](https://docs.flutter.dev/release/archive))
* Android Studio ([Download link](https://developer.android.com/studio))
* Xcode ([Download link](https://apps.apple.com/us/app/xcode/id497799835))
* CocoaPods
  * For Intel-based Mac, use `sudo gem install cocoapods`
  * For Apple Silicon-based Mac, use `brew install cocoapods`

## Install the Flutter SDK

Create a directory at `~/development/` and extract the downloaded Flutter SDK to it

```shell
unzip ~/Downloads/flutter_macos_arm64_3.22.3-stable.zip \
       -d ~/development/
```

Open the Zsh environmental variable file `~/.zshenv` or create one if it does not exist, then copy the following line and paste it at the of the file:

```shell
export PATH=$HOME/development/flutter/bin:$PATH
```

## Configure Android Development

Verify that the following Android components have been installed by following these steps:
* Launch Android Studio and go to `Tools > SDK Manager`
* Click `SDK Platforms`
* Expand `Android 14.0`, then select `Android SDK Platform 34` and `Sources for Android 34`
* Click `SDK Tools`
* Verify that the following SDK Tools have been selected:
  * `Android SDK Command-line Tools`
  * `Android SDK Build-Tools`
  * `Android SDK Platform-Tools`
  * `Android Emulator`
* Click `Apply` and then `OK`

Agree to the licenses of the Android SDK platform

```shell
flutter doctor --android-licenses
```

## Configure iOS Development

Sign the Xcode license agreement

```shell
sudo xcodebuild -license
```

To install the iOS Simulator, run the following command:

```shell
xcodebuild -downloadPlatform iOS
```

Open the Zsh environmental variable file `~/.zshenv`, then copy the following line and paste it at the of the file:

```shell
export PATH=$HOME/.gem/bin:$PATH
```

## Run the Flutter Project

* Open Android Studio without opening the project
* For the newer version of Android Studio, select `Plugins` on the left side
* For the older version of Android Studio, select `Configure > Plugins`
* Select `Marketplace` and search for "Flutter", then click `Install`
* If a popup appears that will install the Dart plugin, click `Yes`
* Restart IDE
* Open mushafi folder with Android Studio
* From the top left, select `Android Studio > Settings...`
* Go to `Languages & Frameworks > Flutter` and make sure the Flutter SDK path is on the right path ($HOME/development/flutter)
* Still in `Languages & Frameworks` settings, select `Dart`
* Check `Enable Dart support for the project 'mushafi'`
* Fill in `Dart SDK path:` with $HOME/development/flutter/bin/cache/dart-sdk
* In the `Enable Dart support for the following modules:`, check `mushafi`
* Open terminal from the mushafi project folder and run `flutter pub get` to fetch dependencies
* From the `<no device selected>` dropdown at the top, select the device
* Click on the green play button on the right

## Build the Production bundle (iOS)

* Open the project directory (mushafi) using cmd/terminal
* Run `flutter build ios && flutter build ipa`
* Upload the `.ipa` file with Transporter

## Build the Production bundle (Android)

* Open the android directory (mushafi/android) using Android Studio
* At the Top Bar, choose `Build > Generate Signed App Bundle / APK...`
* Select `Android App Bundle` and then click `Next`
* Fill in the key store details and then click `Next`
* Choose `productionRelease` and then click `Create`
* Open Play Console, choose `Test and release > Testing > Internal testing`
* Click on `Create new release`
* Upload the `.aab` file and fill in the release notes, then click `Next`
* Click `Submit`