#ifdef WIN32
#define EXPORT __declspec(dllexport)
#else
#define EXPORT                                                                 \
  extern "C" __attribute__((visibility("default"))) __attribute__((used))
#endif

#include <list>
#include <string>

#ifdef __APPLE__

#include <quranasr/asr_inference.h>
#include <quranasr/asr_inference_varkv_asink_tashkeel.h>
#include <quranasr/audio_input.h>
#include <quranasr/file_utils.h>
#include <quranasr/inference_pipeline.h>
#include <quranasr/language.h>
#include <quranasr/logging.h>
#include <quranasr/miniaudio_input.h>

#else

#include "asr_inference.h"
#include "asr_inference_varkv_asink_tashkeel.h"
#include "audio_input.h"
#include "file_utils.h"
#include "inference_pipeline.h"
#include "language.h"
#include "logging.h"
#include "miniaudio_input.h"

#endif

extern "C" {
//#include "benchmark.h"
}

using libquranasr::InferencePipeline;

typedef struct {
  int32_t* data;
  int32_t size;
} Array;

typedef struct {
  int num_runs;
  double average_time;
  uint64_t min_time;
  uint64_t max_time;
} MatMulResult;

typedef void (*OnListeningCallback)(bool);

typedef void (*OnSpeechCallback)(bool);

typedef void (*OnTranscriptionCallback)(const char *);

typedef void (*OnAlignmentResultCallback)(int, int, bool, bool);

typedef void (*OnMatchedVersesCallback)(Array);

EXPORT
long newSelf(const char *vadModelPath, const char *asrModelPath,
             const char *encodedVersesPath, int lastLayerStride,
             const char *condModelPath) {
  try {
    LOG(INFO) << "Creating new InferencePipeline..";

    // Assumes that the encoded verses path str remains the same for all function
    // calls.
    static const std::vector<std::string> kVerses =
      libquranasr::ReadLines(encodedVersesPath);

    // TODO: Keep constants consistent with the ones defined in
    // inference_pipeline.cc
    const int kSampleRate = 16000;
    const int kSrPerMs = kSampleRate / 1000;
    const int kVadContextMs = 65;
    std::unique_ptr<libquranasr::AudioInput> audio_input =
      std::make_unique<libquranasr::MiniAudioInput>(kSampleRate,
                                                    kVadContextMs * kSrPerMs);

    auto asr_builder =
      [asrModelPath, condModelPath](
        libquranasr::SpeechDetectorCallback speech_callback,
        libquranasr::OnInferenceResultCallback transcription_callback,
        libquranasr::OnModelResetCallback reset_callback) {
        return std::make_unique<libquranasr::ASRInferenceVarkvAsinkTashk>(
          asrModelPath, speech_callback, transcription_callback,
          reset_callback, 9, 64, 12, 120, 120, 1,
          condModelPath);
      };

    InferencePipeline *self = new InferencePipeline(
      std::move(audio_input), vadModelPath, lastLayerStride, kVerses, asr_builder);
    return (long)self;
  } catch (const std::exception &e) {
    LOG(ERROR) << e.what();
    return 0;
  }
}

EXPORT
void deleteSelf(long selfAddr) {
  LOG(INFO) << "Deleting Inference pipeline...";
  if (selfAddr != 0) {
    InferencePipeline *self = reinterpret_cast<InferencePipeline *>(selfAddr);
    delete self;
  }
}

EXPORT
bool align(long self_addr, int start_ayah_id, int end_ayah_id,
           OnListeningCallback on_listening_callback,
           OnSpeechCallback speech_callback,
           OnTranscriptionCallback transcription_callback,
           OnAlignmentResultCallback alignment_result_callback) {
  if (self_addr != 0) {
    try {
      InferencePipeline *self = reinterpret_cast<InferencePipeline *>(self_addr);

      auto onlistening_cb = [on_listening_callback](bool is_listening) {
        on_listening_callback(is_listening);
      };

      auto speech_cb = [speech_callback](bool is_speech) {
        speech_callback(is_speech);
      };

      auto transcription_cb =
        [transcription_callback](const std::string &transcription) {
          auto decoded_transcription =
            libquranasr::DecodeAscii2Arabic(transcription);
          char* c_str = strdup(decoded_transcription.c_str());
          transcription_callback(c_str);
        };

      auto alignment_result_cb =
        [alignment_result_callback](int ayah_id, int word_idx, bool is_correct,
                                    bool is_complete) {
          alignment_result_callback(ayah_id, word_idx, is_correct, is_complete);
        };

      return self->Align(start_ayah_id, end_ayah_id, onlistening_cb, speech_cb,
                         transcription_cb, alignment_result_cb);
    } catch (const std::exception &e) {
      LOG(ERROR) << e.what();
      return false;
    }
  }

  return false;
}

EXPORT
bool setAlignerCursor(long selfAddr, int ayah_id, int word_idx) {
  if (selfAddr != 0) {
    InferencePipeline *self = reinterpret_cast<InferencePipeline *>(selfAddr);
    return self->SetAlignerCursor(ayah_id, word_idx);
  }
  return false;
}

EXPORT
bool recognizeVerse(long selfAddr, OnListeningCallback onListeningCallback,
                    OnSpeechCallback speechCallback,
                    OnTranscriptionCallback transcriptionCallback,
                    OnMatchedVersesCallback matchedVersesCallback) {
  if (selfAddr != 0) {
    try {
      InferencePipeline *self = reinterpret_cast<InferencePipeline *>(selfAddr);

      auto onlistening_cb = [onListeningCallback](bool is_listening) {
        onListeningCallback(is_listening);
      };

      auto speech_cb = [speechCallback](bool is_speech) {
        speechCallback(is_speech);
      };

      auto transcription_cb =
        [transcriptionCallback](const std::string &transcription) {
          auto decoded_transcription =
            libquranasr::DecodeAscii2Arabic(transcription);
          char* c_str = strdup(decoded_transcription.c_str());
          transcriptionCallback(c_str);
        };

      auto matched_verses_cb =
        [matchedVersesCallback](const std::vector<int> &matched_verses) {
          Array array;
          array.size = matched_verses.size();
          array.data = new int32_t[array.size];
          std::copy(matched_verses.begin(), matched_verses.end(), array.data);
          matchedVersesCallback(array);
        };

      return self->RecognizeVerse(onlistening_cb, speech_cb, transcription_cb,
                                  matched_verses_cb);
    } catch (const std::exception &e) {
      LOG(ERROR) << e.what();
      return false;
    }
  }
  return false;
}

EXPORT
bool stop(long self_addr) {
  if (self_addr != 0) {
    InferencePipeline *self = reinterpret_cast<InferencePipeline *>(self_addr);
    return self->Stop();
  }
  return false;
}

EXPORT
bool reset(long self_addr) {
  if (self_addr != 0) {
    InferencePipeline *self = reinterpret_cast<InferencePipeline *>(self_addr);
    return self->Reset();
  }
  return false;
}

EXPORT
float testCompatibility(long self_addr) {
  if (self_addr != 0) {
    InferencePipeline *self = reinterpret_cast<InferencePipeline *>(self_addr);
    return self->TestCompatibility();
  }
  return 0.0f;
}

#if 0
static void matmul(float *A, float *B, float *C, const int M, const int N,
                   const int K) {
  for (int i = 0; i < M; i++) {
    for (int j = 0; j < N; j++) {
      for (int p = 0; p < K; p++) {
        C[j * M + i] += A[p * M + i] * B[j * K + p];
      }
    }
  }
}

EXPORT
MatMulResult matMulTest() {
  static float a[2 * 512];
  static float b[512 * 768];
  static float c[2 * 768];

  // Microbenchmark variables
  const int num_runs = 100;
  uint64_t total_time = 0;
  uint64_t min_time = UINT64_MAX;
  uint64_t max_time = 0;

  // Run inference multiple times for benchmarking
  for (int i = 0; i < num_runs; i++) {
    uint64_t start_time = nanos();

    matmul(a, b, c, 2, 768, 512);

    uint64_t end_time = nanos();
    uint64_t elapsed_time = end_time - start_time;

    total_time += elapsed_time;
    if (elapsed_time < min_time)
      min_time = elapsed_time;
    if (elapsed_time > max_time)
      max_time = elapsed_time;
  }

  // Calculate and print benchmark results
  double average_time = (double)total_time / num_runs;
  printf("Inference Benchmark Results (over %d runs):\n", num_runs);
  printf("Average time: %.3f us\n", average_time / 1000.0);
  printf("Min time: %.3f us\n", (double)min_time / 1000.0);
  printf("Max time: %.3f us\n", (double)max_time / 1000.0);

  MatMulResult matMulResult;
  matMulResult.num_runs = num_runs;
  matMulResult.average_time = average_time / 1000.0;
  matMulResult.min_time = min_time / 1000.0;
  matMulResult.max_time = max_time / 1000.0;

  return matMulResult;
}

#include "onnxruntime/onnxruntime_c_api.h"

#define ORT_ABORT_ON_ERROR(g_api, expr)                                        \
  do {                                                                         \
    OrtStatus *onnx_status = (expr);                                           \
    if (onnx_status != NULL) {                                                 \
      const char *msg = (g_api)->GetErrorMessage(onnx_status);                 \
      fprintf(stderr, "%s\n", msg);                                            \
      (g_api)->ReleaseStatus(onnx_status);                                     \
      abort();                                                                 \
    }                                                                          \
  } while (0)

static void matmul_onnx(const OrtApi *api, OrtMemoryInfo *memory_info,
                        OrtSession *session, float *A, float *B, float *C,
                        const int M, const int N, const int K) {
  // Assume input shapes are fixed as per the model we created
  const int64_t input1_shape[] = {M, K};
  const int64_t input2_shape[] = {K, N};
  const int64_t output_shape[] = {M, N};

  size_t input1_size = M * K;
  size_t input2_size = K * N;
  size_t output_size = M * N;

  OrtValue *input1_tensor = NULL;
  ORT_ABORT_ON_ERROR(
      api, api->CreateTensorWithDataAsOrtValue(
               memory_info, A, input1_size * sizeof(float), input1_shape, 2,
               ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT, &input1_tensor));

  OrtValue *input2_tensor = NULL;
  ORT_ABORT_ON_ERROR(
      api, api->CreateTensorWithDataAsOrtValue(
               memory_info, B, input2_size * sizeof(float), input2_shape, 2,
               ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT, &input2_tensor));

  OrtValue *output_tensor = NULL;
  ORT_ABORT_ON_ERROR(
      api, api->CreateTensorWithDataAsOrtValue(
               memory_info, C, output_size * sizeof(float), output_shape, 2,
               ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT, &output_tensor));

  const char *input_names[] = {"input1", "input2"};
  const char *output_names[] = {"output"};
  const OrtValue *input_tensors[] = {input1_tensor, input2_tensor};
  OrtValue *output_tensors[] = {output_tensor};

  ORT_ABORT_ON_ERROR(api, api->Run(session, NULL, input_names, input_tensors, 2,
                                   output_names, 1, output_tensors));

  // Clean up
  api->ReleaseValue(input1_tensor);
  api->ReleaseValue(input2_tensor);
  api->ReleaseValue(output_tensor);
}

EXPORT
MatMulResult matMulOnnxTest() {
  const OrtApi *api = OrtGetApiBase()->GetApi(ORT_API_VERSION);

  OrtEnv *env;
  ORT_ABORT_ON_ERROR(api,
                     api->CreateEnv(ORT_LOGGING_LEVEL_WARNING, "test", &env));

  OrtSessionOptions *session_options;
  ORT_ABORT_ON_ERROR(api, api->CreateSessionOptions(&session_options));
  ORT_ABORT_ON_ERROR(api, api->SetIntraOpNumThreads(session_options, 1));

  OrtMemoryInfo *memory_info;
  ORT_ABORT_ON_ERROR(api,
                     api->CreateCpuMemoryInfo(OrtArenaAllocator,
                                              OrtMemTypeDefault, &memory_info));

  OrtSession *session;
  ORT_ABORT_ON_ERROR(
      api,
      api->CreateSession(env, "/home/<USER>/tmp/models/matmul_model.onnx",
                         session_options, &session));

  static float a[2 * 512];
  static float b[512 * 768];
  static float c[2 * 768];

  // Microbenchmark variables
  const int num_runs = 100;
  uint64_t total_time = 0;
  uint64_t min_time = UINT64_MAX;
  uint64_t max_time = 0;

  // Run inference multiple times for benchmarking
  for (int i = 0; i < num_runs; i++) {
    uint64_t start_time = nanos();

    matmul_onnx(api, memory_info, session, a, b, c, 2, 768, 512);

    uint64_t end_time = nanos();
    uint64_t elapsed_time = end_time - start_time;

    total_time += elapsed_time;
    if (elapsed_time < min_time)
      min_time = elapsed_time;
    if (elapsed_time > max_time)
      max_time = elapsed_time;
  }

  api->ReleaseMemoryInfo(memory_info);
  api->ReleaseSession(session);
  api->ReleaseSessionOptions(session_options);
  api->ReleaseEnv(env);

  // Calculate and print benchmark results
  double average_time = (double)total_time / num_runs;
  printf("Inference Benchmark Results (over %d runs):\n", num_runs);
  printf("Average time: %.3f us\n", average_time / 1000.0);
  printf("Min time: %.3f us\n", (double)min_time / 1000.0);
  printf("Max time: %.3f us\n", (double)max_time / 1000.0);

  MatMulResult matMulResult;
  matMulResult.num_runs = num_runs;
  matMulResult.average_time = average_time / 1000.0;
  matMulResult.min_time = min_time / 1000.0;
  matMulResult.max_time = max_time / 1000.0;

  return matMulResult;
}

EXPORT
MatMulResult benchmarkTest() {
  // TODO: Implement.
  // run_benchmarks();

  MatMulResult matMulResult;
  matMulResult.num_runs = 100;
  matMulResult.average_time = 100.0;
  matMulResult.min_time = 100.0;
  matMulResult.max_time = 100.0;

  return matMulResult;
}
#endif
