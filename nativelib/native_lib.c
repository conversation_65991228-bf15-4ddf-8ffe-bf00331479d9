#include <stdio.h>
#include <stdint.h>

// Function pointer type matching the Dart function
typedef void (*SumFunc)(int32_t, int32_t);

// Global variable to hold the function pointer
SumFunc callback = NULL;

// Function to set the callback
void setCallback(SumFunc func) {
    callback = func;
}

// Function to invoke the callback
void invokeCallback() {
    if (callback != NULL) {
        callback(5, 3);
    }
}