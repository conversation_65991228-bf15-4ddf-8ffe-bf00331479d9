PODS:
  - audio_service (0.14.1):
    - FlutterMacOS
  - audio_session (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - just_audio (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.47.0):
    - sqlite3/common (= 3.47.0)
  - sqlite3/common (3.47.0)
  - sqlite3/dbstatvtab (3.47.0):
    - sqlite3/common
  - sqlite3/fts5 (3.47.0):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.47.0):
    - sqlite3/common
  - sqlite3/rtree (3.47.0):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - FlutterMacOS
    - sqlite3 (~> 3.47.0)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - audio_service (from `Flutter/ephemeral/.symlinks/plugins/audio_service/macos`)
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - just_audio (from `Flutter/ephemeral/.symlinks/plugins/just_audio/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - sqlite3_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - sqlite3

EXTERNAL SOURCES:
  audio_service:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_service/macos
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  just_audio:
    :path: Flutter/ephemeral/.symlinks/plugins/just_audio/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  sqlite3_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  audio_service: b88ff778e0e3915efd4cd1a5ad6f0beef0c950a9
  audio_session: dea1f41890dbf1718f04a56f1d6150fd50039b72
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  just_audio: 9b67ca7b97c61cfc9784ea23cd8cc55eb226d489
  package_info_plus: f5790acc797bf17c3e959e9d6cf162cc68ff7523
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  share_plus: 36537c04ce0c3e3f5bd297ce4318b6d5ee5fd6cf
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: a553b1fd6fe66f53bbb0fe5b4f5bab93f08d7a13
  sqlite3: 0aa20658a9b238a3b1ff7175eb7bdd863b0ab4fd
  sqlite3_flutter_libs: f0b7a85544d8bac7b8bac12eac7d05bcfdd786d0
  url_launcher_macos: c82c93949963e55b228a30115bd219499a6fe404

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.15.2
