import 'dart:io';

import 'package:adaptive_theme/adaptive_theme.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:get_storage/get_storage.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:just_audio_media_kit/just_audio_media_kit.dart';
import 'package:mushafi/credentials.dart';
import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/data/source/minio_data_manager.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/firebase_options.dart';
import 'package:mushafi/presentation/splash/splash_screen.dart';
import 'package:mushafi/utils/app_lifecycle_manager.dart';
import 'package:mushafi/utils/method_channel_helper.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:toastification/toastification.dart';

final talker = TalkerFlutter.init();

const supportedLocales = [
  Locale('en'),
  Locale('ar'),
  Locale('id'),
  Locale('ms'),
  // Locale('tr'),
];

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..findProxy = (uri) {
        return 'DIRECT';
      }
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        FirebaseCrashlytics.instance.recordError(
          Exception('Bad certificate for $host:$port'),
          StackTrace.current,
          reason: 'Bad certificate',
        );

        return host == MinioDataManager.endPoint;
      };
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  JustAudioMediaKit.ensureInitialized();
  await QuranDatabase.applyExistingDatabase();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // ios does not have build flavor, so don't record crashlytics in ios debug mode
  if (!Platform.isIOS || kReleaseMode) {
    // Use recordFlutterError instead of recordFlutterFatalError for better categorization
    // Most Flutter errors are recoverable and shouldn't be marked as fatal
    FlutterError.onError = (errorDetails) {
      // Analyze error severity for better categorization
      final isCritical = _isCriticalFlutterError(errorDetails);

      if (isCritical) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      } else {
        FirebaseCrashlytics.instance.recordFlutterError(errorDetails);
      }
    };

    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: false);
      return true;
    };
  }

  if (kDebugMode || await MethodChannelHelper.isRunningFromFirebaseTestLab()) {
    // Disable Firebase Analytics in debug mode
    // await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(false);
    // await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(false);
  }

  await GetStorage.init();
  await PreferenceStorage.migrateToGetStorage();

  await JustAudioBackground.init(
    androidNotificationChannelId: 'recitation_channel',
    androidNotificationChannelName: 'Recitation Playback',
    androidStopForegroundOnPause: false,
  );

  AppLifecycleManager().initialize();

  HttpOverrides.global = MyHttpOverrides();
  await MinioDataManager.resolveDNS();

  Stripe.publishableKey = stripePublishableKey;

  runApp(
    EasyLocalization(
      supportedLocales: supportedLocales,
      path: 'assets/translations',
      fallbackLocale: const Locale('en'),
      child: const ProviderScope(child: MyApp()),
    ),
  );
}

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return AdaptiveTheme(
      light: ThemeData.light(useMaterial3: true),
      dark: ThemeData.dark(useMaterial3: true),
      initial: AdaptiveThemeMode.light,
      builder: (theme, darkTheme) {
        return ToastificationWrapper(
          child: MaterialApp(
            debugShowCheckedModeBanner: false,
            navigatorKey: navigatorKey,
            title: 'Mushafi',
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            theme: theme.copyWith(
              textTheme: TextTheme(
                displayLarge: Theme.of(context)
                    .textTheme
                    .displayLarge
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                displayMedium: Theme.of(context)
                    .textTheme
                    .displayMedium
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                displaySmall: Theme.of(context)
                    .textTheme
                    .displaySmall
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                headlineLarge: Theme.of(context)
                    .textTheme
                    .headlineLarge
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                headlineMedium: Theme.of(context)
                    .textTheme
                    .headlineMedium
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                headlineSmall: Theme.of(context)
                    .textTheme
                    .headlineSmall
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                titleLarge: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                titleMedium: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                titleSmall: Theme.of(context)
                    .textTheme
                    .titleSmall
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                bodyLarge: Theme.of(context)
                    .textTheme
                    .bodyLarge
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                bodyMedium: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                bodySmall: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                labelLarge: Theme.of(context)
                    .textTheme
                    .labelLarge
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                labelMedium: Theme.of(context)
                    .textTheme
                    .labelMedium
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
                labelSmall: Theme.of(context)
                    .textTheme
                    .labelSmall
                    ?.copyWith(letterSpacing: 0.1, fontFamily: 'ProductSans'),
              ),
            ),
            home: const SplashScreen(),
          ),
        );
      },
    );
  }
}

/// Helper function to determine if a Flutter error should be considered critical/fatal
///
/// Returns true for errors that indicate serious app instability or data corruption
bool _isCriticalFlutterError(FlutterErrorDetails errorDetails) {
  final errorString = errorDetails.toString().toLowerCase();

  // Critical errors that should be marked as fatal
  return errorString.contains('renderflex overflowed') ||
      errorString.contains('null check operator') ||
      errorString.contains('type \'null\' is not a subtype') ||
      errorString.contains('format exception') ||
      errorString.contains('out of memory') ||
      errorString.contains('stack overflow') ||
      errorString.contains('assertion failed') ||
      errorString.contains('flutter/runtime/dart_vm_initializer.cc');
}
