import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/presentation/model/home/<USER>';
import 'package:uuid/uuid.dart';

class RecentPageMapper {
  RecentPageMapper._();

  static RecentPage mapFromEntity(RecentPageEntityData entity) {
    return RecentPage(
      id: entity.id,
      pageNumber: entity.pageNumber,
      time: entity.time,
    );
  }

  static RecentPageEntityCompanion mapToInsertEntity(RecentPage page) {
    return RecentPageEntityCompanion.insert(
      id: (page.id.isNotEmpty) ? page.id : const Uuid().v1(),
      pageNumber: page.pageNumber,
      time: page.time,
    );
  }
}
