import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/presentation/model/quran/alignment_type.dart';
import 'package:mushafi/presentation/model/quran/alignment_word_result.dart';
import 'package:uuid/uuid.dart';

class AlignmentWordResultMapper {
  AlignmentWordResultMapper._();

  static AlignmentWordResult mapFromEntity(
      AlignmentWordResultEntityData entity) {
    return AlignmentWordResult(
      id: entity.id,
      trackId: entity.trackId,
      verseId: entity.verseId,
      verseKey: entity.verseKey,
      wordId: entity.wordId,
      wordIndex: entity.wordIndex,
      pageNumber: entity.pageNumber,
      verseCodeV1: entity.verseCodeV1,
      codeV1: entity.codeV1,
      type: AlignmentType.values.byName(entity.type),
      isCorrect: entity.isCorrect,
      time: entity.time,
      isHidden: false,
    );
  }

  static AlignmentWordResultEntityCompanion mapToInsertEntity(
      AlignmentWordResult result) {
    return AlignmentWordResultEntityCompanion.insert(
      id: (result.id.isNotEmpty) ? result.id : const Uuid().v1(),
      trackId: result.trackId,
      verseId: result.verseId,
      verseKey: result.verseKey,
      wordId: result.wordId,
      wordIndex: result.wordIndex,
      pageNumber: result.pageNumber,
      verseCodeV1: result.verseCodeV1,
      codeV1: result.codeV1,
      type: result.type.name,
      isCorrect: result.isCorrect,
      time: result.time,
    );
  }
}
