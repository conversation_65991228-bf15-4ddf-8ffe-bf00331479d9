import 'package:collection/collection.dart';
import 'package:drift/drift.dart';
import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/presentation/model/quran/mushaf_code.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/model/track/track_range.dart';
import 'package:mushafi/presentation/model/track/track_schedule.dart';

class TrackMapper {
  TrackMapper._();

  static Track mapFromEntity(TrackEntityData entity, bool isDarkMode) {
    final range = TrackRange(
      startJuzNumber: entity.startJuzNumber,
      startPageNumber: entity.startPageNumber,
      startSurahNumber: entity.startSurahNumber,
      startVerseNumber: entity.startVerseNumber,
      endJuzNumber: entity.endJuzNumber,
      endPageNumber: entity.endPageNumber,
      endSurahNumber: entity.endSurahNumber,
      endVerseNumber: entity.endVerseNumber,
    );
    final design = MushafDesign.getList(isDarkMode, includeDefault: true)
        .firstWhereOrNull((design) => design.code == entity.designCode);
    final schedule =
        (entity.scheduleDayNumber != null && entity.scheduleTime != null)
            ? TrackSchedule(
                scheduleDayNumber: entity.scheduleDayNumber!,
                scheduleTime: entity.scheduleTime!)
            : null;
    final zoomState = ZoomState.fromName(entity.zoomState);

    return Track(
      id: entity.id,
      range: range,
      reciterKey: entity.reciter,
      reciteVerseCount: entity.reciteVerseCount,
      isLoop: entity.isLoop,
      enableEnglishRecitation: entity.enableEnglishRecitation,
      bluetoothAddress: entity.bluetoothAddress,
      type: entity.type,
      design: design,
      lastHighlightedVerseKey: entity.lastHighlightedVerseKey,
      lastRecitedWord: entity.lastRecitedWord,
      name: entity.name,
      rangePortion: entity.rangePortion,
      schedule: schedule,
      enableRecitationChecker: entity.enableRecitationChecker,
      zoomState: zoomState ?? ZoomState.defaultZoom,
      reminderDayNumber: entity.reminderDayNumber,
      reminderTime: entity.reminderTime,
      openingTimestamp: entity.openingTimestamp,
      enableDownloadAudio: entity.enableDownloadAudio,
      audioSize: entity.audioSize,
      isAudioDownloaded: entity.isAudioDownloaded,
    );
  }

  static TrackEntityCompanion mapToInsertEntity(Track track,
      {required bool withId}) {
    final designCode = track.design?.code ?? MushafCode.defaultCode;
    return TrackEntityCompanion.insert(
      id: (withId) ? Value(track.id) : const Value.absent(),
      startJuzNumber: track.range.startJuzNumber,
      startPageNumber: track.range.startPageNumber,
      startSurahNumber: track.range.startSurahNumber,
      startVerseNumber: track.range.startVerseNumber,
      endJuzNumber: track.range.endJuzNumber,
      endPageNumber: track.range.endPageNumber,
      endSurahNumber: track.range.endSurahNumber,
      endVerseNumber: track.range.endVerseNumber,
      reciter: Value(track.reciter?.key),
      reciteVerseCount: track.reciteVerseCount,
      isLoop: track.isLoop,
      enableEnglishRecitation: track.enableEnglishRecitation,
      bluetoothAddress: Value(track.bluetoothAddress),
      type: Value(track.type),
      designCode: Value(designCode),
      lastHighlightedVerseKey: Value(track.lastHighlightedVerseKey),
      lastRecitedWord: Value(track.lastRecitedWord),
      name: Value(track.name),
      rangePortion: track.rangePortion,
      scheduleDayNumber: Value(track.schedule?.scheduleDayNumber),
      scheduleTime: Value(track.schedule?.scheduleTime),
      enableRecitationChecker: track.enableRecitationChecker,
      zoomState: track.zoomState.name,
      reminderDayNumber: Value(track.reminderDayNumber),
      reminderTime: Value(track.reminderTime),
      openingTimestamp: Value(track.openingTimestamp),
      enableDownloadAudio: track.enableDownloadAudio,
      audioSize: track.audioSize,
      isAudioDownloaded: track.isAudioDownloaded,
    );
  }
}
