import 'package:collection/collection.dart';
import 'package:mushafi/data/source/quran_data_manager.dart';
import 'package:mushafi/extension/string_extension.dart';
import 'package:mushafi/presentation/model/quran/quran_audio_segment.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';

class QuranVerseMapper {
  QuranVerseMapper._();

  static QuranVerse? mapFromCompleteVerse(CompleteVerse completeVerse) {
    final verse = completeVerse.verse;

    final words = completeVerse.verse.words;
    if (words == null) return null;

    final quranWords = words.mapIndexed((index, word) {
      bool isVerseNumber = (word.codeV1?.isNotEmpty == true)
          ? index == words.length - 1
          : word.textUthmani?.isArabicNumericsOnly() == true;

      return QuranWord(
        id: word.wordId,
        verseKey: verse.verseKey ?? '',
        lineNumber: word.lineNumber,
        codeV1: word.codeV1 ?? '',
        textUthmani: word.textUthmani ?? '',
        textImlaei: word.textImlaei ?? '',
        isVerseNumber: isVerseNumber,
      );
    }).toList();

    final audioSegments = completeVerse.audioSegments;
    final quranAudioSegments = audioSegments.map((segment) {
      return QuranAudioSegment(
        index: segment.index,
        startTime: segment.startTime,
        endTime: segment.endTime,
      );
    }).toList();

    return QuranVerse(
      id: verse.verseId,
      surahId: verse.surahId,
      pageId: verse.pageId,
      verseKey: verse.verseKey ?? '',
      words: quranWords,
      audioSegments: quranAudioSegments,
      translationOrTafseer: completeVerse.translation,
    );
  }
}
