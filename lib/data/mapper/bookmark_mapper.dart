import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/presentation/model/home/<USER>';

class BookmarkMapper {
  BookmarkMapper._();

  static Bookmark mapFromEntity(BookmarkEntityData entity) {
    return Bookmark(
      verseKey: entity.verseKey,
      pageNumber: entity.pageNumber,
      time: entity.time,
    );
  }

  static BookmarkEntityCompanion mapToInsertEntity(Bookmark bookmark) {
    return BookmarkEntityCompanion.insert(
      verseKey: bookmark.verseKey,
      pageNumber: bookmark.pageNumber,
      time: bookmark.time,
    );
  }
}
