import 'dart:io';

import 'package:collection/collection.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:mushafi/extension/verse_key_extension.dart';
import 'package:mushafi/generated/flatbuffer_flatbuffer_generated.dart';
import 'package:mushafi/main.dart';

typedef CompleteVerse = ({
  Verse verse,
  String translation,
  List<AudioSegment> audioSegments
});

class QuranDataManager {
  File? _currentQuranFlatBufferFile;
  Quran? _quran;

  File? _currentTranslationFlatBufferFile;
  Quran? _translation;

  File? _currentAudioSegmentFlatBufferFile;
  Quran? _audioSegment;

  Future<void> initQuran(File flatBufferFile) async {
    if (flatBufferFile == _currentQuranFlatBufferFile) {
      return;
    }

    final byteData = await flatBufferFile.readAsBytes();
    _quran = Quran(byteData);
    _currentQuranFlatBufferFile = flatBufferFile;
  }

  Future<void> initTranslation(File flatBufferFile) async {
    if (flatBufferFile == _currentTranslationFlatBufferFile) {
      return;
    }

    final byteData = await flatBufferFile.readAsBytes();
    _translation = Quran(byteData);
    _currentTranslationFlatBufferFile = flatBufferFile;
  }

  Future<void> initAudioSegment(File flatBufferFile) async {
    if (flatBufferFile == _currentAudioSegmentFlatBufferFile) {
      return;
    }

    final byteData = await flatBufferFile.readAsBytes();
    _audioSegment = Quran(byteData);
    _currentAudioSegmentFlatBufferFile = flatBufferFile;
  }

  Future<String?> getCurrentMushafFileName() async {
    final file = _currentQuranFlatBufferFile;
    if (file == null) return null;

    return file.path.split('/').last;
  }

  Future<Page?> getPageById(int pageId) async {
    final quran = _quran;
    if (quran == null) return null;

    // Assuming pages are stored in order of page_id and start from page 1
    int index = pageId - 1;

    // Get the list of pages
    List<Page?>? pages = quran.pages;

    if (pages == null || index < 0 || index >= pages.length) {
      return null; // Invalid page_id or pages not available
    }

    Page? page = pages[index];

    // Verify that the retrieved page has the correct page_id
    if (page != null && page.pageId == pageId) {
      return page;
    }

    return null; // Page not found or mismatch in page_id
  }

  Future<List<int>?> getGlobalFont() async {
    final quran = _quran;
    if (quran == null) return null;

    return quran.globalFont;
  }

  Future<List<Verse>?> getVersesByPage(Page page) async {
    final verses = _quran?.verses;
    if (verses == null) return null;

    final firstVerseId = page.firstVerseId;
    final lastVerseId = page.lastVerseId;

    // Collect verses for this page using direct indexing
    List<Verse> pageVerses = [];
    for (int verseId = firstVerseId; verseId <= lastVerseId; verseId++) {
      final verseIndex = verseId - 1; // Assuming verse IDs start from 1
      final verse = verses[verseIndex];
      pageVerses.add(verse);
    }

    return pageVerses;
  }

  Future<List<CompleteVerse>?> getCompleteVersesByPage(Page page) async {
    final verses = _quran?.verses;
    if (verses == null) return null;

    List<CompleteVerse> completeVerseList = [];

    final firstVerseId = page.firstVerseId;
    final lastVerseId = page.lastVerseId;

    try {
      final translationVerses = _translation?.verses;
      final audioSegmentVerses = _audioSegment?.verses;

      for (int verseId = firstVerseId; verseId <= lastVerseId; verseId++) {
        final verseIndex = verseId - 1; // Assuming verse IDs start from 1
        final verse = verses[verseIndex];

        final verseKey = verse.verseKey;
        if (verseKey == null) continue;

        final translationVerse = translationVerses?.elementAtOrNull(verseIndex);
        final translation = translationVerse?.translationOrTafseer ?? '';

        final audioSegmentVerse =
            audioSegmentVerses?.elementAtOrNull(verseIndex);
        final audioSegments = audioSegmentVerse?.audioSegments ?? [];

        final completeVerse = (
          verse: verse,
          translation: translation,
          audioSegments: audioSegments
        );
        completeVerseList.add(completeVerse);
      }
    } catch (e, st) {
      talker.handle(e, st);
      FirebaseCrashlytics.instance.recordError(e, st);

      for (int verseId = firstVerseId; verseId <= lastVerseId; verseId++) {
        final verseIndex = verseId - 1; // Assuming verse IDs start from 1
        final verse = verses[verseIndex];

        final verseKey = verse.verseKey;
        if (verseKey == null) continue;

        final completeVerse =
            (verse: verse, translation: '', audioSegments: <AudioSegment>[]);
        completeVerseList.add(completeVerse);
      }
    }

    return completeVerseList;
  }

  Future<Verse?> getFirstVerse() async {
    final verses = _quran?.verses;
    if (verses == null || verses.isEmpty) return null;

    return verses.first;
  }

  Future<Verse?> getPreviousVerse(Verse currentVerse) async {
    final verses = _quran?.verses;
    if (verses == null) return null;

    final currentVerseIndex = currentVerse.verseId - 1;
    final previousVerseIndex = currentVerseIndex - 1;
    if (previousVerseIndex < 0) return null;

    return verses[previousVerseIndex];
  }

  Future<Verse?> getNextVerse(Verse currentVerse) async {
    final verses = _quran?.verses;
    if (verses == null) return null;

    final currentVerseIndex = currentVerse.verseId - 1;
    final nextVerseIndex = currentVerseIndex + 1;
    if (nextVerseIndex < 0) return null;

    return verses.elementAtOrNull(nextVerseIndex);
  }

  Future<bool> isGlobalFont() async {
    return _quran?.globalFont != null;
  }

  Future<Juz?> getJuzById(int juzId) async {
    final quran = _quran;
    if (quran == null) return null;

    int index = juzId - 1;
    List<Juz?>? juzs = quran.juzs;

    if (juzs == null || index < 0 || index >= juzs.length) {
      return null;
    }

    Juz? juz = juzs[index];

    // Verify that the retrieved page has the correct page_id
    if (juz != null && juz.juzId == juzId) {
      return juz;
    }

    return null; // Page not found or mismatch in page_id
  }

  Future<List<Page>?> getPages() async {
    final pages = _quran?.pages;
    if (pages == null) return null;

    return pages;
  }

  Future<List<Juz>?> getJuzs() async {
    final juzs = _quran?.juzs;
    if (juzs == null) return null;

    return juzs;
  }

  Future<CompleteVerse?> getCompleteVerseById(int id) async {
    final verses = _quran?.verses;
    if (verses == null) return null;

    final verseIndex = id - 1;
    if (verseIndex < 0) return null;

    final verse = verses[verseIndex];

    try {
      final translationVerses = _translation?.verses;
      final audioSegmentVerses = _audioSegment?.verses;

      final translationVerse = translationVerses?.elementAtOrNull(verseIndex);
      final translation = translationVerse?.translationOrTafseer ?? '';

      final audioSegmentVerse = audioSegmentVerses?.elementAtOrNull(verseIndex);
      final audioSegments = audioSegmentVerse?.audioSegments ?? [];

      return (
        verse: verse,
        translation: translation,
        audioSegments: audioSegments
      );
    } catch (e, st) {
      talker.handle(e, st);
      FirebaseCrashlytics.instance.recordError(e, st);

      return (verse: verse, translation: '', audioSegments: <AudioSegment>[]);
    }
  }

  Future<List<CompleteVerse>?> getCompleteVerseByIds(List<int> ids) async {
    final verses = _quran?.verses;
    if (verses == null) return null;

    final List<CompleteVerse> completeVerseList = [];

    try {
      final translationVerses = _translation?.verses;
      final audioSegmentVerses = _audioSegment?.verses;

      for (final id in ids) {
        final verseIndex = id - 1;
        if (verseIndex < 0) continue;

        final verse = verses[verseIndex];

        final translationVerse = translationVerses?.elementAtOrNull(verseIndex);
        final translation = translationVerse?.translationOrTafseer ?? '';

        final audioSegmentVerse =
            audioSegmentVerses?.elementAtOrNull(verseIndex);
        final audioSegments = audioSegmentVerse?.audioSegments ?? [];

        completeVerseList.add((
          verse: verse,
          translation: translation,
          audioSegments: audioSegments
        ));
      }
    } catch (e, st) {
      talker.handle(e, st);
      FirebaseCrashlytics.instance.recordError(e, st);

      for (final id in ids) {
        final verseIndex = id - 1;
        if (verseIndex < 0) continue;

        final verse = verses[verseIndex];
        completeVerseList.add(
            (verse: verse, translation: '', audioSegments: <AudioSegment>[]));
      }
    }

    return completeVerseList;
  }

  Future<CompleteVerse?> getCompleteVerseByVerseKey(String verseKey) async {
    final verses = _quran?.verses;
    if (verses == null) return null;

    CompleteVerse? completeVerse;

    try {
      final translationVerses = _translation?.verses ?? [];
      final audioSegmentVerses = _audioSegment?.verses ?? [];

      for (final verse
          in IterableZip([verses, translationVerses, audioSegmentVerses])) {
        if (verse[0].verseKey == verseKey) {
          completeVerse = (
            verse: verse[0],
            translation: verse.elementAtOrNull(1)?.translationOrTafseer ?? '',
            audioSegments: verse.elementAtOrNull(2)?.audioSegments ?? [],
          );
          break;
        }
      }
    } catch (e, st) {
      talker.handle(e, st);
      FirebaseCrashlytics.instance.recordError(e, st);

      for (final verse in verses) {
        if (verse.verseKey == verseKey) {
          completeVerse = (
            verse: verse,
            translation: '',
            audioSegments: <AudioSegment>[],
          );
          break;
        }
      }
    }

    return completeVerse;
  }

  Future<List<CompleteVerse>?> getCompleteVerseList(
    String startVerseKey,
    String endVerseKey,
  ) async {
    final verses = _quran?.verses;
    if (verses == null) return null;

    List<CompleteVerse> completeVerseList = [];

    try {
      final translationVerses = _translation?.verses ?? [];
      final audioSegmentVerses = _audioSegment?.verses ?? [];

      for (final verse
          in IterableZip([verses, translationVerses, audioSegmentVerses])) {
        final verseKey = verse[0].verseKey;
        if (verseKey == null) return null;

        if (verseKey.verseKeyInt >= startVerseKey.verseKeyInt &&
            verseKey.verseKeyInt <= endVerseKey.verseKeyInt) {
          final completeVerse = (
            verse: verse[0],
            translation: verse.elementAtOrNull(1)?.translationOrTafseer ?? '',
            audioSegments: verse.elementAtOrNull(2)?.audioSegments ?? [],
          );
          completeVerseList.add(completeVerse);
        }

        if (verseKey == endVerseKey) {
          break;
        }
      }
    } catch (e, st) {
      talker.handle(e, st);
      FirebaseCrashlytics.instance.recordError(e, st);

      for (final verse in verses) {
        final verseKey = verse.verseKey;
        if (verseKey == null) return null;

        if (verseKey.verseKeyInt >= startVerseKey.verseKeyInt &&
            verseKey.verseKeyInt <= endVerseKey.verseKeyInt) {
          final completeVerse = (
            verse: verse,
            translation: '',
            audioSegments: <AudioSegment>[],
          );
          completeVerseList.add(completeVerse);
        }

        if (verseKey == endVerseKey) {
          break;
        }
      }
    }

    return completeVerseList;
  }
}
