import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';

class FirestoreDataManager {
  final db =
      FirebaseFirestore.instanceFor(app: Firebase.app(), databaseId: 'bmdb');

  Future<double?> getRTF(String modelVersion) async {
    final latestTestedModelVersion =
        PreferenceStorage.getLatestTestedModelVersion();
    if (latestTestedModelVersion != modelVersion) return null;

    final deviceInfoPlugin = DeviceInfoPlugin();

    String? deviceModel;
    if (Platform.isAndroid) {
      final info = await deviceInfoPlugin.androidInfo;
      deviceModel = info.model;
    } else if (Platform.isIOS) {
      final info = await deviceInfoPlugin.iosInfo;
      // machine ids: https://gist.github.com/adamawolf/3048717
      deviceModel = info.utsname.machine;
    }

    if (deviceModel == null) return null;

    // all fields
    final snapshot = await db.collection("rtf_data").doc(deviceModel).get();
    final data = snapshot.data();
    if (data == null) return null;

    // rtf data by app version
    final packageInfo = await PackageInfo.fromPlatform();
    final appVersion = packageInfo.version.split("-").first;
    if (!data.containsKey(appVersion)) return null;
    final rtfData = data[appVersion] as Map<String, dynamic>;

    // rtf list by model version
    if (!rtfData.containsKey(modelVersion)) return null;
    final rtfList = List<double>.from(rtfData[modelVersion]);

    final medianRtf = CommonUtils.calculateMedian(rtfList);
    return medianRtf;
  }

  Future<void> setRTF(double rtf, String modelVersion) async {
    final deviceInfoPlugin = DeviceInfoPlugin();

    String? deviceModel;
    if (Platform.isAndroid) {
      final info = await deviceInfoPlugin.androidInfo;
      deviceModel = info.model;
    } else if (Platform.isIOS) {
      final info = await deviceInfoPlugin.iosInfo;
      deviceModel = info.utsname.machine;
    }

    if (deviceModel == null) return;

    final packageInfo = await PackageInfo.fromPlatform();
    final appVersion = packageInfo.version.split("-").first;
    final data = {
      appVersion: {
        modelVersion: FieldValue.arrayUnion([rtf]),
      }
    };

    db
        .collection("rtf_data")
        .doc(deviceModel)
        .set(data, SetOptions(merge: true));

    PreferenceStorage.saveLatestTestedModelVersion(modelVersion);
  }
}
