import 'package:drift/drift.dart';

class AlignmentWordResultEntity extends Table {
  TextColumn get id => text().named('id')();

  IntColumn get trackId => integer().named('trackId')();

  IntColumn get verseId => integer().named('verseId')();

  TextColumn get verseKey => text().named('verseKey')();

  IntColumn get wordId => integer().named('wordId')();

  IntColumn get wordIndex => integer().named('wordIndex')();

  IntColumn get pageNumber => integer().named('pageNumber')();

  TextColumn get verseCodeV1 => text().named('verseCodeV1')(); // split by comma

  TextColumn get codeV1 => text().named('codeV1')();

  TextColumn get type => text().named('type')();

  BoolColumn get isCorrect => boolean().named('isCorrect')();

  IntColumn get time => integer().named('time')();

  @override
  Set<Column<Object>>? get primaryKey => {id};

  @override
  String? get tableName => 'alignment_word_results';
}
