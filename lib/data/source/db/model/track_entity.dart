import 'package:drift/drift.dart';
import 'package:mushafi/data/source/db/converter.dart';
import 'package:mushafi/presentation/model/quran/mushaf_code.dart';
import 'package:mushafi/presentation/model/track/track_range_portion.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';

class TrackEntity extends Table {
  IntColumn get id => integer().autoIncrement().named('id')();

  IntColumn get startJuzNumber => integer().named('startJuzNumber')();

  IntColumn get startPageNumber => integer().named('startPageNumber')();

  IntColumn get startSurahNumber => integer().named('startSurahNumber')();

  IntColumn get startVerseNumber => integer().named('startVerseNumber')();

  IntColumn get endJuzNumber => integer().named('endJuzNumber')();

  IntColumn get endPageNumber => integer().named('endPageNumber')();

  IntColumn get endSurahNumber => integer().named('endSurahNumber')();

  IntColumn get endVerseNumber => integer().named('endVerseNumber')();

  TextColumn get reciter => text().named('reciter').nullable()();

  IntColumn get reciteVerseCount => integer().named('reciteVerseCount')();

  BoolColumn get isLoop => boolean().named('isLoop')();

  BoolColumn get enableEnglishRecitation =>
      boolean().named('enableEnglishRecitation')();

  TextColumn get bluetoothAddress =>
      text().named('bluetoothAddress').nullable()();

  TextColumn get type => textEnum<TrackType>().named('type').nullable()();

  TextColumn get designCode =>
      textEnum<MushafCode>().named('designCode').nullable()();

  TextColumn get lastHighlightedVerseKey =>
      text().named('lastHighlightedVerseKey').nullable()();

  TextColumn get lastRecitedWord => text()
      .named('lastRecitedWord')
      .map(Converter.lastRecitedWordConverter)
      .nullable()();

  TextColumn get name => text().named('name').nullable()();

  TextColumn get rangePortion =>
      textEnum<TrackRangePortion>().named('rangePortion')();

  IntColumn get scheduleDayNumber =>
      integer().named('scheduleDayNumber').nullable()();

  TextColumn get scheduleTime => text().named('scheduleTime').nullable()();

  BoolColumn get enableRecitationChecker =>
      boolean().named('enableRecitationChecker')();

  TextColumn get zoomState => text().named('zoomState')();

  IntColumn get reminderDayNumber =>
      integer().named('reminderDayNumber').nullable()();

  TextColumn get reminderTime => text().named('reminderTime').nullable()();

  IntColumn get openingTimestamp =>
      integer().named('openingTimestamp').nullable()();

  BoolColumn get enableDownloadAudio =>
      boolean().named('enableDownloadAudio')();

  IntColumn get audioSize => integer().named('audioSize')();

  BoolColumn get isAudioDownloaded => boolean().named('isAudioDownloaded')();

  @override
  String? get tableName => 'tracks';
}
