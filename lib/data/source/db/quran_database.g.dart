// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_database.dart';

// ignore_for_file: type=lint
class $TrackEntityTable extends TrackEntity
    with TableInfo<$TrackEntityTable, TrackEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TrackEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _startJuzNumberMeta =
      const VerificationMeta('startJuzNumber');
  @override
  late final GeneratedColumn<int> startJuzNumber = GeneratedColumn<int>(
      'startJuzNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _startPageNumberMeta =
      const VerificationMeta('startPageNumber');
  @override
  late final GeneratedColumn<int> startPageNumber = GeneratedColumn<int>(
      'startPageNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _startSurahNumberMeta =
      const VerificationMeta('startSurahNumber');
  @override
  late final GeneratedColumn<int> startSurahNumber = GeneratedColumn<int>(
      'startSurahNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _startVerseNumberMeta =
      const VerificationMeta('startVerseNumber');
  @override
  late final GeneratedColumn<int> startVerseNumber = GeneratedColumn<int>(
      'startVerseNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _endJuzNumberMeta =
      const VerificationMeta('endJuzNumber');
  @override
  late final GeneratedColumn<int> endJuzNumber = GeneratedColumn<int>(
      'endJuzNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _endPageNumberMeta =
      const VerificationMeta('endPageNumber');
  @override
  late final GeneratedColumn<int> endPageNumber = GeneratedColumn<int>(
      'endPageNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _endSurahNumberMeta =
      const VerificationMeta('endSurahNumber');
  @override
  late final GeneratedColumn<int> endSurahNumber = GeneratedColumn<int>(
      'endSurahNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _endVerseNumberMeta =
      const VerificationMeta('endVerseNumber');
  @override
  late final GeneratedColumn<int> endVerseNumber = GeneratedColumn<int>(
      'endVerseNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _reciterMeta =
      const VerificationMeta('reciter');
  @override
  late final GeneratedColumn<String> reciter = GeneratedColumn<String>(
      'reciter', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _reciteVerseCountMeta =
      const VerificationMeta('reciteVerseCount');
  @override
  late final GeneratedColumn<int> reciteVerseCount = GeneratedColumn<int>(
      'reciteVerseCount', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _isLoopMeta = const VerificationMeta('isLoop');
  @override
  late final GeneratedColumn<bool> isLoop = GeneratedColumn<bool>(
      'isLoop', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("isLoop" IN (0, 1))'));
  static const VerificationMeta _enableEnglishRecitationMeta =
      const VerificationMeta('enableEnglishRecitation');
  @override
  late final GeneratedColumn<bool> enableEnglishRecitation =
      GeneratedColumn<bool>('enableEnglishRecitation', aliasedName, false,
          type: DriftSqlType.bool,
          requiredDuringInsert: true,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("enableEnglishRecitation" IN (0, 1))'));
  static const VerificationMeta _bluetoothAddressMeta =
      const VerificationMeta('bluetoothAddress');
  @override
  late final GeneratedColumn<String> bluetoothAddress = GeneratedColumn<String>(
      'bluetoothAddress', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumnWithTypeConverter<TrackType?, String> type =
      GeneratedColumn<String>('type', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<TrackType?>($TrackEntityTable.$convertertypen);
  static const VerificationMeta _designCodeMeta =
      const VerificationMeta('designCode');
  @override
  late final GeneratedColumnWithTypeConverter<MushafCode?, String> designCode =
      GeneratedColumn<String>('designCode', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<MushafCode?>($TrackEntityTable.$converterdesignCoden);
  static const VerificationMeta _lastHighlightedVerseKeyMeta =
      const VerificationMeta('lastHighlightedVerseKey');
  @override
  late final GeneratedColumn<String> lastHighlightedVerseKey =
      GeneratedColumn<String>('lastHighlightedVerseKey', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _lastRecitedWordMeta =
      const VerificationMeta('lastRecitedWord');
  @override
  late final GeneratedColumnWithTypeConverter<LastRecitedWord?, String>
      lastRecitedWord = GeneratedColumn<String>(
              'lastRecitedWord', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<LastRecitedWord?>(
              $TrackEntityTable.$converterlastRecitedWordn);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _rangePortionMeta =
      const VerificationMeta('rangePortion');
  @override
  late final GeneratedColumnWithTypeConverter<TrackRangePortion, String>
      rangePortion = GeneratedColumn<String>('rangePortion', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<TrackRangePortion>(
              $TrackEntityTable.$converterrangePortion);
  static const VerificationMeta _scheduleDayNumberMeta =
      const VerificationMeta('scheduleDayNumber');
  @override
  late final GeneratedColumn<int> scheduleDayNumber = GeneratedColumn<int>(
      'scheduleDayNumber', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _scheduleTimeMeta =
      const VerificationMeta('scheduleTime');
  @override
  late final GeneratedColumn<String> scheduleTime = GeneratedColumn<String>(
      'scheduleTime', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _enableRecitationCheckerMeta =
      const VerificationMeta('enableRecitationChecker');
  @override
  late final GeneratedColumn<bool> enableRecitationChecker =
      GeneratedColumn<bool>('enableRecitationChecker', aliasedName, false,
          type: DriftSqlType.bool,
          requiredDuringInsert: true,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("enableRecitationChecker" IN (0, 1))'));
  static const VerificationMeta _zoomStateMeta =
      const VerificationMeta('zoomState');
  @override
  late final GeneratedColumn<String> zoomState = GeneratedColumn<String>(
      'zoomState', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _reminderDayNumberMeta =
      const VerificationMeta('reminderDayNumber');
  @override
  late final GeneratedColumn<int> reminderDayNumber = GeneratedColumn<int>(
      'reminderDayNumber', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _reminderTimeMeta =
      const VerificationMeta('reminderTime');
  @override
  late final GeneratedColumn<String> reminderTime = GeneratedColumn<String>(
      'reminderTime', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _openingTimestampMeta =
      const VerificationMeta('openingTimestamp');
  @override
  late final GeneratedColumn<int> openingTimestamp = GeneratedColumn<int>(
      'openingTimestamp', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _enableDownloadAudioMeta =
      const VerificationMeta('enableDownloadAudio');
  @override
  late final GeneratedColumn<bool> enableDownloadAudio = GeneratedColumn<bool>(
      'enableDownloadAudio', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("enableDownloadAudio" IN (0, 1))'));
  static const VerificationMeta _audioSizeMeta =
      const VerificationMeta('audioSize');
  @override
  late final GeneratedColumn<int> audioSize = GeneratedColumn<int>(
      'audioSize', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _isAudioDownloadedMeta =
      const VerificationMeta('isAudioDownloaded');
  @override
  late final GeneratedColumn<bool> isAudioDownloaded = GeneratedColumn<bool>(
      'isAudioDownloaded', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("isAudioDownloaded" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        startJuzNumber,
        startPageNumber,
        startSurahNumber,
        startVerseNumber,
        endJuzNumber,
        endPageNumber,
        endSurahNumber,
        endVerseNumber,
        reciter,
        reciteVerseCount,
        isLoop,
        enableEnglishRecitation,
        bluetoothAddress,
        type,
        designCode,
        lastHighlightedVerseKey,
        lastRecitedWord,
        name,
        rangePortion,
        scheduleDayNumber,
        scheduleTime,
        enableRecitationChecker,
        zoomState,
        reminderDayNumber,
        reminderTime,
        openingTimestamp,
        enableDownloadAudio,
        audioSize,
        isAudioDownloaded
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'tracks';
  @override
  VerificationContext validateIntegrity(Insertable<TrackEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('startJuzNumber')) {
      context.handle(
          _startJuzNumberMeta,
          startJuzNumber.isAcceptableOrUnknown(
              data['startJuzNumber']!, _startJuzNumberMeta));
    } else if (isInserting) {
      context.missing(_startJuzNumberMeta);
    }
    if (data.containsKey('startPageNumber')) {
      context.handle(
          _startPageNumberMeta,
          startPageNumber.isAcceptableOrUnknown(
              data['startPageNumber']!, _startPageNumberMeta));
    } else if (isInserting) {
      context.missing(_startPageNumberMeta);
    }
    if (data.containsKey('startSurahNumber')) {
      context.handle(
          _startSurahNumberMeta,
          startSurahNumber.isAcceptableOrUnknown(
              data['startSurahNumber']!, _startSurahNumberMeta));
    } else if (isInserting) {
      context.missing(_startSurahNumberMeta);
    }
    if (data.containsKey('startVerseNumber')) {
      context.handle(
          _startVerseNumberMeta,
          startVerseNumber.isAcceptableOrUnknown(
              data['startVerseNumber']!, _startVerseNumberMeta));
    } else if (isInserting) {
      context.missing(_startVerseNumberMeta);
    }
    if (data.containsKey('endJuzNumber')) {
      context.handle(
          _endJuzNumberMeta,
          endJuzNumber.isAcceptableOrUnknown(
              data['endJuzNumber']!, _endJuzNumberMeta));
    } else if (isInserting) {
      context.missing(_endJuzNumberMeta);
    }
    if (data.containsKey('endPageNumber')) {
      context.handle(
          _endPageNumberMeta,
          endPageNumber.isAcceptableOrUnknown(
              data['endPageNumber']!, _endPageNumberMeta));
    } else if (isInserting) {
      context.missing(_endPageNumberMeta);
    }
    if (data.containsKey('endSurahNumber')) {
      context.handle(
          _endSurahNumberMeta,
          endSurahNumber.isAcceptableOrUnknown(
              data['endSurahNumber']!, _endSurahNumberMeta));
    } else if (isInserting) {
      context.missing(_endSurahNumberMeta);
    }
    if (data.containsKey('endVerseNumber')) {
      context.handle(
          _endVerseNumberMeta,
          endVerseNumber.isAcceptableOrUnknown(
              data['endVerseNumber']!, _endVerseNumberMeta));
    } else if (isInserting) {
      context.missing(_endVerseNumberMeta);
    }
    if (data.containsKey('reciter')) {
      context.handle(_reciterMeta,
          reciter.isAcceptableOrUnknown(data['reciter']!, _reciterMeta));
    }
    if (data.containsKey('reciteVerseCount')) {
      context.handle(
          _reciteVerseCountMeta,
          reciteVerseCount.isAcceptableOrUnknown(
              data['reciteVerseCount']!, _reciteVerseCountMeta));
    } else if (isInserting) {
      context.missing(_reciteVerseCountMeta);
    }
    if (data.containsKey('isLoop')) {
      context.handle(_isLoopMeta,
          isLoop.isAcceptableOrUnknown(data['isLoop']!, _isLoopMeta));
    } else if (isInserting) {
      context.missing(_isLoopMeta);
    }
    if (data.containsKey('enableEnglishRecitation')) {
      context.handle(
          _enableEnglishRecitationMeta,
          enableEnglishRecitation.isAcceptableOrUnknown(
              data['enableEnglishRecitation']!, _enableEnglishRecitationMeta));
    } else if (isInserting) {
      context.missing(_enableEnglishRecitationMeta);
    }
    if (data.containsKey('bluetoothAddress')) {
      context.handle(
          _bluetoothAddressMeta,
          bluetoothAddress.isAcceptableOrUnknown(
              data['bluetoothAddress']!, _bluetoothAddressMeta));
    }
    context.handle(_typeMeta, const VerificationResult.success());
    context.handle(_designCodeMeta, const VerificationResult.success());
    if (data.containsKey('lastHighlightedVerseKey')) {
      context.handle(
          _lastHighlightedVerseKeyMeta,
          lastHighlightedVerseKey.isAcceptableOrUnknown(
              data['lastHighlightedVerseKey']!, _lastHighlightedVerseKeyMeta));
    }
    context.handle(_lastRecitedWordMeta, const VerificationResult.success());
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    }
    context.handle(_rangePortionMeta, const VerificationResult.success());
    if (data.containsKey('scheduleDayNumber')) {
      context.handle(
          _scheduleDayNumberMeta,
          scheduleDayNumber.isAcceptableOrUnknown(
              data['scheduleDayNumber']!, _scheduleDayNumberMeta));
    }
    if (data.containsKey('scheduleTime')) {
      context.handle(
          _scheduleTimeMeta,
          scheduleTime.isAcceptableOrUnknown(
              data['scheduleTime']!, _scheduleTimeMeta));
    }
    if (data.containsKey('enableRecitationChecker')) {
      context.handle(
          _enableRecitationCheckerMeta,
          enableRecitationChecker.isAcceptableOrUnknown(
              data['enableRecitationChecker']!, _enableRecitationCheckerMeta));
    } else if (isInserting) {
      context.missing(_enableRecitationCheckerMeta);
    }
    if (data.containsKey('zoomState')) {
      context.handle(_zoomStateMeta,
          zoomState.isAcceptableOrUnknown(data['zoomState']!, _zoomStateMeta));
    } else if (isInserting) {
      context.missing(_zoomStateMeta);
    }
    if (data.containsKey('reminderDayNumber')) {
      context.handle(
          _reminderDayNumberMeta,
          reminderDayNumber.isAcceptableOrUnknown(
              data['reminderDayNumber']!, _reminderDayNumberMeta));
    }
    if (data.containsKey('reminderTime')) {
      context.handle(
          _reminderTimeMeta,
          reminderTime.isAcceptableOrUnknown(
              data['reminderTime']!, _reminderTimeMeta));
    }
    if (data.containsKey('openingTimestamp')) {
      context.handle(
          _openingTimestampMeta,
          openingTimestamp.isAcceptableOrUnknown(
              data['openingTimestamp']!, _openingTimestampMeta));
    }
    if (data.containsKey('enableDownloadAudio')) {
      context.handle(
          _enableDownloadAudioMeta,
          enableDownloadAudio.isAcceptableOrUnknown(
              data['enableDownloadAudio']!, _enableDownloadAudioMeta));
    } else if (isInserting) {
      context.missing(_enableDownloadAudioMeta);
    }
    if (data.containsKey('audioSize')) {
      context.handle(_audioSizeMeta,
          audioSize.isAcceptableOrUnknown(data['audioSize']!, _audioSizeMeta));
    } else if (isInserting) {
      context.missing(_audioSizeMeta);
    }
    if (data.containsKey('isAudioDownloaded')) {
      context.handle(
          _isAudioDownloadedMeta,
          isAudioDownloaded.isAcceptableOrUnknown(
              data['isAudioDownloaded']!, _isAudioDownloadedMeta));
    } else if (isInserting) {
      context.missing(_isAudioDownloadedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  TrackEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TrackEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      startJuzNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}startJuzNumber'])!,
      startPageNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}startPageNumber'])!,
      startSurahNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}startSurahNumber'])!,
      startVerseNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}startVerseNumber'])!,
      endJuzNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}endJuzNumber'])!,
      endPageNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}endPageNumber'])!,
      endSurahNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}endSurahNumber'])!,
      endVerseNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}endVerseNumber'])!,
      reciter: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}reciter']),
      reciteVerseCount: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}reciteVerseCount'])!,
      isLoop: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isLoop'])!,
      enableEnglishRecitation: attachedDatabase.typeMapping.read(
          DriftSqlType.bool,
          data['${effectivePrefix}enableEnglishRecitation'])!,
      bluetoothAddress: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}bluetoothAddress']),
      type: $TrackEntityTable.$convertertypen.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])),
      designCode: $TrackEntityTable.$converterdesignCoden.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}designCode'])),
      lastHighlightedVerseKey: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}lastHighlightedVerseKey']),
      lastRecitedWord: $TrackEntityTable.$converterlastRecitedWordn.fromSql(
          attachedDatabase.typeMapping.read(
              DriftSqlType.string, data['${effectivePrefix}lastRecitedWord'])),
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name']),
      rangePortion: $TrackEntityTable.$converterrangePortion.fromSql(
          attachedDatabase.typeMapping.read(
              DriftSqlType.string, data['${effectivePrefix}rangePortion'])!),
      scheduleDayNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}scheduleDayNumber']),
      scheduleTime: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}scheduleTime']),
      enableRecitationChecker: attachedDatabase.typeMapping.read(
          DriftSqlType.bool,
          data['${effectivePrefix}enableRecitationChecker'])!,
      zoomState: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}zoomState'])!,
      reminderDayNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}reminderDayNumber']),
      reminderTime: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}reminderTime']),
      openingTimestamp: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}openingTimestamp']),
      enableDownloadAudio: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}enableDownloadAudio'])!,
      audioSize: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}audioSize'])!,
      isAudioDownloaded: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}isAudioDownloaded'])!,
    );
  }

  @override
  $TrackEntityTable createAlias(String alias) {
    return $TrackEntityTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<TrackType, String, String> $convertertype =
      const EnumNameConverter<TrackType>(TrackType.values);
  static JsonTypeConverter2<TrackType?, String?, String?> $convertertypen =
      JsonTypeConverter2.asNullable($convertertype);
  static JsonTypeConverter2<MushafCode, String, String> $converterdesignCode =
      const EnumNameConverter<MushafCode>(MushafCode.values);
  static JsonTypeConverter2<MushafCode?, String?, String?>
      $converterdesignCoden =
      JsonTypeConverter2.asNullable($converterdesignCode);
  static JsonTypeConverter2<LastRecitedWord, String, String>
      $converterlastRecitedWord = Converter.lastRecitedWordConverter;
  static JsonTypeConverter2<LastRecitedWord?, String?, String?>
      $converterlastRecitedWordn =
      JsonTypeConverter2.asNullable($converterlastRecitedWord);
  static JsonTypeConverter2<TrackRangePortion, String, String>
      $converterrangePortion =
      const EnumNameConverter<TrackRangePortion>(TrackRangePortion.values);
}

class TrackEntityData extends DataClass implements Insertable<TrackEntityData> {
  final int id;
  final int startJuzNumber;
  final int startPageNumber;
  final int startSurahNumber;
  final int startVerseNumber;
  final int endJuzNumber;
  final int endPageNumber;
  final int endSurahNumber;
  final int endVerseNumber;
  final String? reciter;
  final int reciteVerseCount;
  final bool isLoop;
  final bool enableEnglishRecitation;
  final String? bluetoothAddress;
  final TrackType? type;
  final MushafCode? designCode;
  final String? lastHighlightedVerseKey;
  final LastRecitedWord? lastRecitedWord;
  final String? name;
  final TrackRangePortion rangePortion;
  final int? scheduleDayNumber;
  final String? scheduleTime;
  final bool enableRecitationChecker;
  final String zoomState;
  final int? reminderDayNumber;
  final String? reminderTime;
  final int? openingTimestamp;
  final bool enableDownloadAudio;
  final int audioSize;
  final bool isAudioDownloaded;
  const TrackEntityData(
      {required this.id,
      required this.startJuzNumber,
      required this.startPageNumber,
      required this.startSurahNumber,
      required this.startVerseNumber,
      required this.endJuzNumber,
      required this.endPageNumber,
      required this.endSurahNumber,
      required this.endVerseNumber,
      this.reciter,
      required this.reciteVerseCount,
      required this.isLoop,
      required this.enableEnglishRecitation,
      this.bluetoothAddress,
      this.type,
      this.designCode,
      this.lastHighlightedVerseKey,
      this.lastRecitedWord,
      this.name,
      required this.rangePortion,
      this.scheduleDayNumber,
      this.scheduleTime,
      required this.enableRecitationChecker,
      required this.zoomState,
      this.reminderDayNumber,
      this.reminderTime,
      this.openingTimestamp,
      required this.enableDownloadAudio,
      required this.audioSize,
      required this.isAudioDownloaded});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['startJuzNumber'] = Variable<int>(startJuzNumber);
    map['startPageNumber'] = Variable<int>(startPageNumber);
    map['startSurahNumber'] = Variable<int>(startSurahNumber);
    map['startVerseNumber'] = Variable<int>(startVerseNumber);
    map['endJuzNumber'] = Variable<int>(endJuzNumber);
    map['endPageNumber'] = Variable<int>(endPageNumber);
    map['endSurahNumber'] = Variable<int>(endSurahNumber);
    map['endVerseNumber'] = Variable<int>(endVerseNumber);
    if (!nullToAbsent || reciter != null) {
      map['reciter'] = Variable<String>(reciter);
    }
    map['reciteVerseCount'] = Variable<int>(reciteVerseCount);
    map['isLoop'] = Variable<bool>(isLoop);
    map['enableEnglishRecitation'] = Variable<bool>(enableEnglishRecitation);
    if (!nullToAbsent || bluetoothAddress != null) {
      map['bluetoothAddress'] = Variable<String>(bluetoothAddress);
    }
    if (!nullToAbsent || type != null) {
      map['type'] =
          Variable<String>($TrackEntityTable.$convertertypen.toSql(type));
    }
    if (!nullToAbsent || designCode != null) {
      map['designCode'] = Variable<String>(
          $TrackEntityTable.$converterdesignCoden.toSql(designCode));
    }
    if (!nullToAbsent || lastHighlightedVerseKey != null) {
      map['lastHighlightedVerseKey'] =
          Variable<String>(lastHighlightedVerseKey);
    }
    if (!nullToAbsent || lastRecitedWord != null) {
      map['lastRecitedWord'] = Variable<String>(
          $TrackEntityTable.$converterlastRecitedWordn.toSql(lastRecitedWord));
    }
    if (!nullToAbsent || name != null) {
      map['name'] = Variable<String>(name);
    }
    {
      map['rangePortion'] = Variable<String>(
          $TrackEntityTable.$converterrangePortion.toSql(rangePortion));
    }
    if (!nullToAbsent || scheduleDayNumber != null) {
      map['scheduleDayNumber'] = Variable<int>(scheduleDayNumber);
    }
    if (!nullToAbsent || scheduleTime != null) {
      map['scheduleTime'] = Variable<String>(scheduleTime);
    }
    map['enableRecitationChecker'] = Variable<bool>(enableRecitationChecker);
    map['zoomState'] = Variable<String>(zoomState);
    if (!nullToAbsent || reminderDayNumber != null) {
      map['reminderDayNumber'] = Variable<int>(reminderDayNumber);
    }
    if (!nullToAbsent || reminderTime != null) {
      map['reminderTime'] = Variable<String>(reminderTime);
    }
    if (!nullToAbsent || openingTimestamp != null) {
      map['openingTimestamp'] = Variable<int>(openingTimestamp);
    }
    map['enableDownloadAudio'] = Variable<bool>(enableDownloadAudio);
    map['audioSize'] = Variable<int>(audioSize);
    map['isAudioDownloaded'] = Variable<bool>(isAudioDownloaded);
    return map;
  }

  TrackEntityCompanion toCompanion(bool nullToAbsent) {
    return TrackEntityCompanion(
      id: Value(id),
      startJuzNumber: Value(startJuzNumber),
      startPageNumber: Value(startPageNumber),
      startSurahNumber: Value(startSurahNumber),
      startVerseNumber: Value(startVerseNumber),
      endJuzNumber: Value(endJuzNumber),
      endPageNumber: Value(endPageNumber),
      endSurahNumber: Value(endSurahNumber),
      endVerseNumber: Value(endVerseNumber),
      reciter: reciter == null && nullToAbsent
          ? const Value.absent()
          : Value(reciter),
      reciteVerseCount: Value(reciteVerseCount),
      isLoop: Value(isLoop),
      enableEnglishRecitation: Value(enableEnglishRecitation),
      bluetoothAddress: bluetoothAddress == null && nullToAbsent
          ? const Value.absent()
          : Value(bluetoothAddress),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
      designCode: designCode == null && nullToAbsent
          ? const Value.absent()
          : Value(designCode),
      lastHighlightedVerseKey: lastHighlightedVerseKey == null && nullToAbsent
          ? const Value.absent()
          : Value(lastHighlightedVerseKey),
      lastRecitedWord: lastRecitedWord == null && nullToAbsent
          ? const Value.absent()
          : Value(lastRecitedWord),
      name: name == null && nullToAbsent ? const Value.absent() : Value(name),
      rangePortion: Value(rangePortion),
      scheduleDayNumber: scheduleDayNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(scheduleDayNumber),
      scheduleTime: scheduleTime == null && nullToAbsent
          ? const Value.absent()
          : Value(scheduleTime),
      enableRecitationChecker: Value(enableRecitationChecker),
      zoomState: Value(zoomState),
      reminderDayNumber: reminderDayNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(reminderDayNumber),
      reminderTime: reminderTime == null && nullToAbsent
          ? const Value.absent()
          : Value(reminderTime),
      openingTimestamp: openingTimestamp == null && nullToAbsent
          ? const Value.absent()
          : Value(openingTimestamp),
      enableDownloadAudio: Value(enableDownloadAudio),
      audioSize: Value(audioSize),
      isAudioDownloaded: Value(isAudioDownloaded),
    );
  }

  factory TrackEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TrackEntityData(
      id: serializer.fromJson<int>(json['id']),
      startJuzNumber: serializer.fromJson<int>(json['startJuzNumber']),
      startPageNumber: serializer.fromJson<int>(json['startPageNumber']),
      startSurahNumber: serializer.fromJson<int>(json['startSurahNumber']),
      startVerseNumber: serializer.fromJson<int>(json['startVerseNumber']),
      endJuzNumber: serializer.fromJson<int>(json['endJuzNumber']),
      endPageNumber: serializer.fromJson<int>(json['endPageNumber']),
      endSurahNumber: serializer.fromJson<int>(json['endSurahNumber']),
      endVerseNumber: serializer.fromJson<int>(json['endVerseNumber']),
      reciter: serializer.fromJson<String?>(json['reciter']),
      reciteVerseCount: serializer.fromJson<int>(json['reciteVerseCount']),
      isLoop: serializer.fromJson<bool>(json['isLoop']),
      enableEnglishRecitation:
          serializer.fromJson<bool>(json['enableEnglishRecitation']),
      bluetoothAddress: serializer.fromJson<String?>(json['bluetoothAddress']),
      type: $TrackEntityTable.$convertertypen
          .fromJson(serializer.fromJson<String?>(json['type'])),
      designCode: $TrackEntityTable.$converterdesignCoden
          .fromJson(serializer.fromJson<String?>(json['designCode'])),
      lastHighlightedVerseKey:
          serializer.fromJson<String?>(json['lastHighlightedVerseKey']),
      lastRecitedWord: $TrackEntityTable.$converterlastRecitedWordn
          .fromJson(serializer.fromJson<String?>(json['lastRecitedWord'])),
      name: serializer.fromJson<String?>(json['name']),
      rangePortion: $TrackEntityTable.$converterrangePortion
          .fromJson(serializer.fromJson<String>(json['rangePortion'])),
      scheduleDayNumber: serializer.fromJson<int?>(json['scheduleDayNumber']),
      scheduleTime: serializer.fromJson<String?>(json['scheduleTime']),
      enableRecitationChecker:
          serializer.fromJson<bool>(json['enableRecitationChecker']),
      zoomState: serializer.fromJson<String>(json['zoomState']),
      reminderDayNumber: serializer.fromJson<int?>(json['reminderDayNumber']),
      reminderTime: serializer.fromJson<String?>(json['reminderTime']),
      openingTimestamp: serializer.fromJson<int?>(json['openingTimestamp']),
      enableDownloadAudio:
          serializer.fromJson<bool>(json['enableDownloadAudio']),
      audioSize: serializer.fromJson<int>(json['audioSize']),
      isAudioDownloaded: serializer.fromJson<bool>(json['isAudioDownloaded']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'startJuzNumber': serializer.toJson<int>(startJuzNumber),
      'startPageNumber': serializer.toJson<int>(startPageNumber),
      'startSurahNumber': serializer.toJson<int>(startSurahNumber),
      'startVerseNumber': serializer.toJson<int>(startVerseNumber),
      'endJuzNumber': serializer.toJson<int>(endJuzNumber),
      'endPageNumber': serializer.toJson<int>(endPageNumber),
      'endSurahNumber': serializer.toJson<int>(endSurahNumber),
      'endVerseNumber': serializer.toJson<int>(endVerseNumber),
      'reciter': serializer.toJson<String?>(reciter),
      'reciteVerseCount': serializer.toJson<int>(reciteVerseCount),
      'isLoop': serializer.toJson<bool>(isLoop),
      'enableEnglishRecitation':
          serializer.toJson<bool>(enableEnglishRecitation),
      'bluetoothAddress': serializer.toJson<String?>(bluetoothAddress),
      'type': serializer
          .toJson<String?>($TrackEntityTable.$convertertypen.toJson(type)),
      'designCode': serializer.toJson<String?>(
          $TrackEntityTable.$converterdesignCoden.toJson(designCode)),
      'lastHighlightedVerseKey':
          serializer.toJson<String?>(lastHighlightedVerseKey),
      'lastRecitedWord': serializer.toJson<String?>(
          $TrackEntityTable.$converterlastRecitedWordn.toJson(lastRecitedWord)),
      'name': serializer.toJson<String?>(name),
      'rangePortion': serializer.toJson<String>(
          $TrackEntityTable.$converterrangePortion.toJson(rangePortion)),
      'scheduleDayNumber': serializer.toJson<int?>(scheduleDayNumber),
      'scheduleTime': serializer.toJson<String?>(scheduleTime),
      'enableRecitationChecker':
          serializer.toJson<bool>(enableRecitationChecker),
      'zoomState': serializer.toJson<String>(zoomState),
      'reminderDayNumber': serializer.toJson<int?>(reminderDayNumber),
      'reminderTime': serializer.toJson<String?>(reminderTime),
      'openingTimestamp': serializer.toJson<int?>(openingTimestamp),
      'enableDownloadAudio': serializer.toJson<bool>(enableDownloadAudio),
      'audioSize': serializer.toJson<int>(audioSize),
      'isAudioDownloaded': serializer.toJson<bool>(isAudioDownloaded),
    };
  }

  TrackEntityData copyWith(
          {int? id,
          int? startJuzNumber,
          int? startPageNumber,
          int? startSurahNumber,
          int? startVerseNumber,
          int? endJuzNumber,
          int? endPageNumber,
          int? endSurahNumber,
          int? endVerseNumber,
          Value<String?> reciter = const Value.absent(),
          int? reciteVerseCount,
          bool? isLoop,
          bool? enableEnglishRecitation,
          Value<String?> bluetoothAddress = const Value.absent(),
          Value<TrackType?> type = const Value.absent(),
          Value<MushafCode?> designCode = const Value.absent(),
          Value<String?> lastHighlightedVerseKey = const Value.absent(),
          Value<LastRecitedWord?> lastRecitedWord = const Value.absent(),
          Value<String?> name = const Value.absent(),
          TrackRangePortion? rangePortion,
          Value<int?> scheduleDayNumber = const Value.absent(),
          Value<String?> scheduleTime = const Value.absent(),
          bool? enableRecitationChecker,
          String? zoomState,
          Value<int?> reminderDayNumber = const Value.absent(),
          Value<String?> reminderTime = const Value.absent(),
          Value<int?> openingTimestamp = const Value.absent(),
          bool? enableDownloadAudio,
          int? audioSize,
          bool? isAudioDownloaded}) =>
      TrackEntityData(
        id: id ?? this.id,
        startJuzNumber: startJuzNumber ?? this.startJuzNumber,
        startPageNumber: startPageNumber ?? this.startPageNumber,
        startSurahNumber: startSurahNumber ?? this.startSurahNumber,
        startVerseNumber: startVerseNumber ?? this.startVerseNumber,
        endJuzNumber: endJuzNumber ?? this.endJuzNumber,
        endPageNumber: endPageNumber ?? this.endPageNumber,
        endSurahNumber: endSurahNumber ?? this.endSurahNumber,
        endVerseNumber: endVerseNumber ?? this.endVerseNumber,
        reciter: reciter.present ? reciter.value : this.reciter,
        reciteVerseCount: reciteVerseCount ?? this.reciteVerseCount,
        isLoop: isLoop ?? this.isLoop,
        enableEnglishRecitation:
            enableEnglishRecitation ?? this.enableEnglishRecitation,
        bluetoothAddress: bluetoothAddress.present
            ? bluetoothAddress.value
            : this.bluetoothAddress,
        type: type.present ? type.value : this.type,
        designCode: designCode.present ? designCode.value : this.designCode,
        lastHighlightedVerseKey: lastHighlightedVerseKey.present
            ? lastHighlightedVerseKey.value
            : this.lastHighlightedVerseKey,
        lastRecitedWord: lastRecitedWord.present
            ? lastRecitedWord.value
            : this.lastRecitedWord,
        name: name.present ? name.value : this.name,
        rangePortion: rangePortion ?? this.rangePortion,
        scheduleDayNumber: scheduleDayNumber.present
            ? scheduleDayNumber.value
            : this.scheduleDayNumber,
        scheduleTime:
            scheduleTime.present ? scheduleTime.value : this.scheduleTime,
        enableRecitationChecker:
            enableRecitationChecker ?? this.enableRecitationChecker,
        zoomState: zoomState ?? this.zoomState,
        reminderDayNumber: reminderDayNumber.present
            ? reminderDayNumber.value
            : this.reminderDayNumber,
        reminderTime:
            reminderTime.present ? reminderTime.value : this.reminderTime,
        openingTimestamp: openingTimestamp.present
            ? openingTimestamp.value
            : this.openingTimestamp,
        enableDownloadAudio: enableDownloadAudio ?? this.enableDownloadAudio,
        audioSize: audioSize ?? this.audioSize,
        isAudioDownloaded: isAudioDownloaded ?? this.isAudioDownloaded,
      );
  TrackEntityData copyWithCompanion(TrackEntityCompanion data) {
    return TrackEntityData(
      id: data.id.present ? data.id.value : this.id,
      startJuzNumber: data.startJuzNumber.present
          ? data.startJuzNumber.value
          : this.startJuzNumber,
      startPageNumber: data.startPageNumber.present
          ? data.startPageNumber.value
          : this.startPageNumber,
      startSurahNumber: data.startSurahNumber.present
          ? data.startSurahNumber.value
          : this.startSurahNumber,
      startVerseNumber: data.startVerseNumber.present
          ? data.startVerseNumber.value
          : this.startVerseNumber,
      endJuzNumber: data.endJuzNumber.present
          ? data.endJuzNumber.value
          : this.endJuzNumber,
      endPageNumber: data.endPageNumber.present
          ? data.endPageNumber.value
          : this.endPageNumber,
      endSurahNumber: data.endSurahNumber.present
          ? data.endSurahNumber.value
          : this.endSurahNumber,
      endVerseNumber: data.endVerseNumber.present
          ? data.endVerseNumber.value
          : this.endVerseNumber,
      reciter: data.reciter.present ? data.reciter.value : this.reciter,
      reciteVerseCount: data.reciteVerseCount.present
          ? data.reciteVerseCount.value
          : this.reciteVerseCount,
      isLoop: data.isLoop.present ? data.isLoop.value : this.isLoop,
      enableEnglishRecitation: data.enableEnglishRecitation.present
          ? data.enableEnglishRecitation.value
          : this.enableEnglishRecitation,
      bluetoothAddress: data.bluetoothAddress.present
          ? data.bluetoothAddress.value
          : this.bluetoothAddress,
      type: data.type.present ? data.type.value : this.type,
      designCode:
          data.designCode.present ? data.designCode.value : this.designCode,
      lastHighlightedVerseKey: data.lastHighlightedVerseKey.present
          ? data.lastHighlightedVerseKey.value
          : this.lastHighlightedVerseKey,
      lastRecitedWord: data.lastRecitedWord.present
          ? data.lastRecitedWord.value
          : this.lastRecitedWord,
      name: data.name.present ? data.name.value : this.name,
      rangePortion: data.rangePortion.present
          ? data.rangePortion.value
          : this.rangePortion,
      scheduleDayNumber: data.scheduleDayNumber.present
          ? data.scheduleDayNumber.value
          : this.scheduleDayNumber,
      scheduleTime: data.scheduleTime.present
          ? data.scheduleTime.value
          : this.scheduleTime,
      enableRecitationChecker: data.enableRecitationChecker.present
          ? data.enableRecitationChecker.value
          : this.enableRecitationChecker,
      zoomState: data.zoomState.present ? data.zoomState.value : this.zoomState,
      reminderDayNumber: data.reminderDayNumber.present
          ? data.reminderDayNumber.value
          : this.reminderDayNumber,
      reminderTime: data.reminderTime.present
          ? data.reminderTime.value
          : this.reminderTime,
      openingTimestamp: data.openingTimestamp.present
          ? data.openingTimestamp.value
          : this.openingTimestamp,
      enableDownloadAudio: data.enableDownloadAudio.present
          ? data.enableDownloadAudio.value
          : this.enableDownloadAudio,
      audioSize: data.audioSize.present ? data.audioSize.value : this.audioSize,
      isAudioDownloaded: data.isAudioDownloaded.present
          ? data.isAudioDownloaded.value
          : this.isAudioDownloaded,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TrackEntityData(')
          ..write('id: $id, ')
          ..write('startJuzNumber: $startJuzNumber, ')
          ..write('startPageNumber: $startPageNumber, ')
          ..write('startSurahNumber: $startSurahNumber, ')
          ..write('startVerseNumber: $startVerseNumber, ')
          ..write('endJuzNumber: $endJuzNumber, ')
          ..write('endPageNumber: $endPageNumber, ')
          ..write('endSurahNumber: $endSurahNumber, ')
          ..write('endVerseNumber: $endVerseNumber, ')
          ..write('reciter: $reciter, ')
          ..write('reciteVerseCount: $reciteVerseCount, ')
          ..write('isLoop: $isLoop, ')
          ..write('enableEnglishRecitation: $enableEnglishRecitation, ')
          ..write('bluetoothAddress: $bluetoothAddress, ')
          ..write('type: $type, ')
          ..write('designCode: $designCode, ')
          ..write('lastHighlightedVerseKey: $lastHighlightedVerseKey, ')
          ..write('lastRecitedWord: $lastRecitedWord, ')
          ..write('name: $name, ')
          ..write('rangePortion: $rangePortion, ')
          ..write('scheduleDayNumber: $scheduleDayNumber, ')
          ..write('scheduleTime: $scheduleTime, ')
          ..write('enableRecitationChecker: $enableRecitationChecker, ')
          ..write('zoomState: $zoomState, ')
          ..write('reminderDayNumber: $reminderDayNumber, ')
          ..write('reminderTime: $reminderTime, ')
          ..write('openingTimestamp: $openingTimestamp, ')
          ..write('enableDownloadAudio: $enableDownloadAudio, ')
          ..write('audioSize: $audioSize, ')
          ..write('isAudioDownloaded: $isAudioDownloaded')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        startJuzNumber,
        startPageNumber,
        startSurahNumber,
        startVerseNumber,
        endJuzNumber,
        endPageNumber,
        endSurahNumber,
        endVerseNumber,
        reciter,
        reciteVerseCount,
        isLoop,
        enableEnglishRecitation,
        bluetoothAddress,
        type,
        designCode,
        lastHighlightedVerseKey,
        lastRecitedWord,
        name,
        rangePortion,
        scheduleDayNumber,
        scheduleTime,
        enableRecitationChecker,
        zoomState,
        reminderDayNumber,
        reminderTime,
        openingTimestamp,
        enableDownloadAudio,
        audioSize,
        isAudioDownloaded
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TrackEntityData &&
          other.id == this.id &&
          other.startJuzNumber == this.startJuzNumber &&
          other.startPageNumber == this.startPageNumber &&
          other.startSurahNumber == this.startSurahNumber &&
          other.startVerseNumber == this.startVerseNumber &&
          other.endJuzNumber == this.endJuzNumber &&
          other.endPageNumber == this.endPageNumber &&
          other.endSurahNumber == this.endSurahNumber &&
          other.endVerseNumber == this.endVerseNumber &&
          other.reciter == this.reciter &&
          other.reciteVerseCount == this.reciteVerseCount &&
          other.isLoop == this.isLoop &&
          other.enableEnglishRecitation == this.enableEnglishRecitation &&
          other.bluetoothAddress == this.bluetoothAddress &&
          other.type == this.type &&
          other.designCode == this.designCode &&
          other.lastHighlightedVerseKey == this.lastHighlightedVerseKey &&
          other.lastRecitedWord == this.lastRecitedWord &&
          other.name == this.name &&
          other.rangePortion == this.rangePortion &&
          other.scheduleDayNumber == this.scheduleDayNumber &&
          other.scheduleTime == this.scheduleTime &&
          other.enableRecitationChecker == this.enableRecitationChecker &&
          other.zoomState == this.zoomState &&
          other.reminderDayNumber == this.reminderDayNumber &&
          other.reminderTime == this.reminderTime &&
          other.openingTimestamp == this.openingTimestamp &&
          other.enableDownloadAudio == this.enableDownloadAudio &&
          other.audioSize == this.audioSize &&
          other.isAudioDownloaded == this.isAudioDownloaded);
}

class TrackEntityCompanion extends UpdateCompanion<TrackEntityData> {
  final Value<int> id;
  final Value<int> startJuzNumber;
  final Value<int> startPageNumber;
  final Value<int> startSurahNumber;
  final Value<int> startVerseNumber;
  final Value<int> endJuzNumber;
  final Value<int> endPageNumber;
  final Value<int> endSurahNumber;
  final Value<int> endVerseNumber;
  final Value<String?> reciter;
  final Value<int> reciteVerseCount;
  final Value<bool> isLoop;
  final Value<bool> enableEnglishRecitation;
  final Value<String?> bluetoothAddress;
  final Value<TrackType?> type;
  final Value<MushafCode?> designCode;
  final Value<String?> lastHighlightedVerseKey;
  final Value<LastRecitedWord?> lastRecitedWord;
  final Value<String?> name;
  final Value<TrackRangePortion> rangePortion;
  final Value<int?> scheduleDayNumber;
  final Value<String?> scheduleTime;
  final Value<bool> enableRecitationChecker;
  final Value<String> zoomState;
  final Value<int?> reminderDayNumber;
  final Value<String?> reminderTime;
  final Value<int?> openingTimestamp;
  final Value<bool> enableDownloadAudio;
  final Value<int> audioSize;
  final Value<bool> isAudioDownloaded;
  const TrackEntityCompanion({
    this.id = const Value.absent(),
    this.startJuzNumber = const Value.absent(),
    this.startPageNumber = const Value.absent(),
    this.startSurahNumber = const Value.absent(),
    this.startVerseNumber = const Value.absent(),
    this.endJuzNumber = const Value.absent(),
    this.endPageNumber = const Value.absent(),
    this.endSurahNumber = const Value.absent(),
    this.endVerseNumber = const Value.absent(),
    this.reciter = const Value.absent(),
    this.reciteVerseCount = const Value.absent(),
    this.isLoop = const Value.absent(),
    this.enableEnglishRecitation = const Value.absent(),
    this.bluetoothAddress = const Value.absent(),
    this.type = const Value.absent(),
    this.designCode = const Value.absent(),
    this.lastHighlightedVerseKey = const Value.absent(),
    this.lastRecitedWord = const Value.absent(),
    this.name = const Value.absent(),
    this.rangePortion = const Value.absent(),
    this.scheduleDayNumber = const Value.absent(),
    this.scheduleTime = const Value.absent(),
    this.enableRecitationChecker = const Value.absent(),
    this.zoomState = const Value.absent(),
    this.reminderDayNumber = const Value.absent(),
    this.reminderTime = const Value.absent(),
    this.openingTimestamp = const Value.absent(),
    this.enableDownloadAudio = const Value.absent(),
    this.audioSize = const Value.absent(),
    this.isAudioDownloaded = const Value.absent(),
  });
  TrackEntityCompanion.insert({
    this.id = const Value.absent(),
    required int startJuzNumber,
    required int startPageNumber,
    required int startSurahNumber,
    required int startVerseNumber,
    required int endJuzNumber,
    required int endPageNumber,
    required int endSurahNumber,
    required int endVerseNumber,
    this.reciter = const Value.absent(),
    required int reciteVerseCount,
    required bool isLoop,
    required bool enableEnglishRecitation,
    this.bluetoothAddress = const Value.absent(),
    this.type = const Value.absent(),
    this.designCode = const Value.absent(),
    this.lastHighlightedVerseKey = const Value.absent(),
    this.lastRecitedWord = const Value.absent(),
    this.name = const Value.absent(),
    required TrackRangePortion rangePortion,
    this.scheduleDayNumber = const Value.absent(),
    this.scheduleTime = const Value.absent(),
    required bool enableRecitationChecker,
    required String zoomState,
    this.reminderDayNumber = const Value.absent(),
    this.reminderTime = const Value.absent(),
    this.openingTimestamp = const Value.absent(),
    required bool enableDownloadAudio,
    required int audioSize,
    required bool isAudioDownloaded,
  })  : startJuzNumber = Value(startJuzNumber),
        startPageNumber = Value(startPageNumber),
        startSurahNumber = Value(startSurahNumber),
        startVerseNumber = Value(startVerseNumber),
        endJuzNumber = Value(endJuzNumber),
        endPageNumber = Value(endPageNumber),
        endSurahNumber = Value(endSurahNumber),
        endVerseNumber = Value(endVerseNumber),
        reciteVerseCount = Value(reciteVerseCount),
        isLoop = Value(isLoop),
        enableEnglishRecitation = Value(enableEnglishRecitation),
        rangePortion = Value(rangePortion),
        enableRecitationChecker = Value(enableRecitationChecker),
        zoomState = Value(zoomState),
        enableDownloadAudio = Value(enableDownloadAudio),
        audioSize = Value(audioSize),
        isAudioDownloaded = Value(isAudioDownloaded);
  static Insertable<TrackEntityData> custom({
    Expression<int>? id,
    Expression<int>? startJuzNumber,
    Expression<int>? startPageNumber,
    Expression<int>? startSurahNumber,
    Expression<int>? startVerseNumber,
    Expression<int>? endJuzNumber,
    Expression<int>? endPageNumber,
    Expression<int>? endSurahNumber,
    Expression<int>? endVerseNumber,
    Expression<String>? reciter,
    Expression<int>? reciteVerseCount,
    Expression<bool>? isLoop,
    Expression<bool>? enableEnglishRecitation,
    Expression<String>? bluetoothAddress,
    Expression<String>? type,
    Expression<String>? designCode,
    Expression<String>? lastHighlightedVerseKey,
    Expression<String>? lastRecitedWord,
    Expression<String>? name,
    Expression<String>? rangePortion,
    Expression<int>? scheduleDayNumber,
    Expression<String>? scheduleTime,
    Expression<bool>? enableRecitationChecker,
    Expression<String>? zoomState,
    Expression<int>? reminderDayNumber,
    Expression<String>? reminderTime,
    Expression<int>? openingTimestamp,
    Expression<bool>? enableDownloadAudio,
    Expression<int>? audioSize,
    Expression<bool>? isAudioDownloaded,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (startJuzNumber != null) 'startJuzNumber': startJuzNumber,
      if (startPageNumber != null) 'startPageNumber': startPageNumber,
      if (startSurahNumber != null) 'startSurahNumber': startSurahNumber,
      if (startVerseNumber != null) 'startVerseNumber': startVerseNumber,
      if (endJuzNumber != null) 'endJuzNumber': endJuzNumber,
      if (endPageNumber != null) 'endPageNumber': endPageNumber,
      if (endSurahNumber != null) 'endSurahNumber': endSurahNumber,
      if (endVerseNumber != null) 'endVerseNumber': endVerseNumber,
      if (reciter != null) 'reciter': reciter,
      if (reciteVerseCount != null) 'reciteVerseCount': reciteVerseCount,
      if (isLoop != null) 'isLoop': isLoop,
      if (enableEnglishRecitation != null)
        'enableEnglishRecitation': enableEnglishRecitation,
      if (bluetoothAddress != null) 'bluetoothAddress': bluetoothAddress,
      if (type != null) 'type': type,
      if (designCode != null) 'designCode': designCode,
      if (lastHighlightedVerseKey != null)
        'lastHighlightedVerseKey': lastHighlightedVerseKey,
      if (lastRecitedWord != null) 'lastRecitedWord': lastRecitedWord,
      if (name != null) 'name': name,
      if (rangePortion != null) 'rangePortion': rangePortion,
      if (scheduleDayNumber != null) 'scheduleDayNumber': scheduleDayNumber,
      if (scheduleTime != null) 'scheduleTime': scheduleTime,
      if (enableRecitationChecker != null)
        'enableRecitationChecker': enableRecitationChecker,
      if (zoomState != null) 'zoomState': zoomState,
      if (reminderDayNumber != null) 'reminderDayNumber': reminderDayNumber,
      if (reminderTime != null) 'reminderTime': reminderTime,
      if (openingTimestamp != null) 'openingTimestamp': openingTimestamp,
      if (enableDownloadAudio != null)
        'enableDownloadAudio': enableDownloadAudio,
      if (audioSize != null) 'audioSize': audioSize,
      if (isAudioDownloaded != null) 'isAudioDownloaded': isAudioDownloaded,
    });
  }

  TrackEntityCompanion copyWith(
      {Value<int>? id,
      Value<int>? startJuzNumber,
      Value<int>? startPageNumber,
      Value<int>? startSurahNumber,
      Value<int>? startVerseNumber,
      Value<int>? endJuzNumber,
      Value<int>? endPageNumber,
      Value<int>? endSurahNumber,
      Value<int>? endVerseNumber,
      Value<String?>? reciter,
      Value<int>? reciteVerseCount,
      Value<bool>? isLoop,
      Value<bool>? enableEnglishRecitation,
      Value<String?>? bluetoothAddress,
      Value<TrackType?>? type,
      Value<MushafCode?>? designCode,
      Value<String?>? lastHighlightedVerseKey,
      Value<LastRecitedWord?>? lastRecitedWord,
      Value<String?>? name,
      Value<TrackRangePortion>? rangePortion,
      Value<int?>? scheduleDayNumber,
      Value<String?>? scheduleTime,
      Value<bool>? enableRecitationChecker,
      Value<String>? zoomState,
      Value<int?>? reminderDayNumber,
      Value<String?>? reminderTime,
      Value<int?>? openingTimestamp,
      Value<bool>? enableDownloadAudio,
      Value<int>? audioSize,
      Value<bool>? isAudioDownloaded}) {
    return TrackEntityCompanion(
      id: id ?? this.id,
      startJuzNumber: startJuzNumber ?? this.startJuzNumber,
      startPageNumber: startPageNumber ?? this.startPageNumber,
      startSurahNumber: startSurahNumber ?? this.startSurahNumber,
      startVerseNumber: startVerseNumber ?? this.startVerseNumber,
      endJuzNumber: endJuzNumber ?? this.endJuzNumber,
      endPageNumber: endPageNumber ?? this.endPageNumber,
      endSurahNumber: endSurahNumber ?? this.endSurahNumber,
      endVerseNumber: endVerseNumber ?? this.endVerseNumber,
      reciter: reciter ?? this.reciter,
      reciteVerseCount: reciteVerseCount ?? this.reciteVerseCount,
      isLoop: isLoop ?? this.isLoop,
      enableEnglishRecitation:
          enableEnglishRecitation ?? this.enableEnglishRecitation,
      bluetoothAddress: bluetoothAddress ?? this.bluetoothAddress,
      type: type ?? this.type,
      designCode: designCode ?? this.designCode,
      lastHighlightedVerseKey:
          lastHighlightedVerseKey ?? this.lastHighlightedVerseKey,
      lastRecitedWord: lastRecitedWord ?? this.lastRecitedWord,
      name: name ?? this.name,
      rangePortion: rangePortion ?? this.rangePortion,
      scheduleDayNumber: scheduleDayNumber ?? this.scheduleDayNumber,
      scheduleTime: scheduleTime ?? this.scheduleTime,
      enableRecitationChecker:
          enableRecitationChecker ?? this.enableRecitationChecker,
      zoomState: zoomState ?? this.zoomState,
      reminderDayNumber: reminderDayNumber ?? this.reminderDayNumber,
      reminderTime: reminderTime ?? this.reminderTime,
      openingTimestamp: openingTimestamp ?? this.openingTimestamp,
      enableDownloadAudio: enableDownloadAudio ?? this.enableDownloadAudio,
      audioSize: audioSize ?? this.audioSize,
      isAudioDownloaded: isAudioDownloaded ?? this.isAudioDownloaded,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (startJuzNumber.present) {
      map['startJuzNumber'] = Variable<int>(startJuzNumber.value);
    }
    if (startPageNumber.present) {
      map['startPageNumber'] = Variable<int>(startPageNumber.value);
    }
    if (startSurahNumber.present) {
      map['startSurahNumber'] = Variable<int>(startSurahNumber.value);
    }
    if (startVerseNumber.present) {
      map['startVerseNumber'] = Variable<int>(startVerseNumber.value);
    }
    if (endJuzNumber.present) {
      map['endJuzNumber'] = Variable<int>(endJuzNumber.value);
    }
    if (endPageNumber.present) {
      map['endPageNumber'] = Variable<int>(endPageNumber.value);
    }
    if (endSurahNumber.present) {
      map['endSurahNumber'] = Variable<int>(endSurahNumber.value);
    }
    if (endVerseNumber.present) {
      map['endVerseNumber'] = Variable<int>(endVerseNumber.value);
    }
    if (reciter.present) {
      map['reciter'] = Variable<String>(reciter.value);
    }
    if (reciteVerseCount.present) {
      map['reciteVerseCount'] = Variable<int>(reciteVerseCount.value);
    }
    if (isLoop.present) {
      map['isLoop'] = Variable<bool>(isLoop.value);
    }
    if (enableEnglishRecitation.present) {
      map['enableEnglishRecitation'] =
          Variable<bool>(enableEnglishRecitation.value);
    }
    if (bluetoothAddress.present) {
      map['bluetoothAddress'] = Variable<String>(bluetoothAddress.value);
    }
    if (type.present) {
      map['type'] =
          Variable<String>($TrackEntityTable.$convertertypen.toSql(type.value));
    }
    if (designCode.present) {
      map['designCode'] = Variable<String>(
          $TrackEntityTable.$converterdesignCoden.toSql(designCode.value));
    }
    if (lastHighlightedVerseKey.present) {
      map['lastHighlightedVerseKey'] =
          Variable<String>(lastHighlightedVerseKey.value);
    }
    if (lastRecitedWord.present) {
      map['lastRecitedWord'] = Variable<String>($TrackEntityTable
          .$converterlastRecitedWordn
          .toSql(lastRecitedWord.value));
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (rangePortion.present) {
      map['rangePortion'] = Variable<String>(
          $TrackEntityTable.$converterrangePortion.toSql(rangePortion.value));
    }
    if (scheduleDayNumber.present) {
      map['scheduleDayNumber'] = Variable<int>(scheduleDayNumber.value);
    }
    if (scheduleTime.present) {
      map['scheduleTime'] = Variable<String>(scheduleTime.value);
    }
    if (enableRecitationChecker.present) {
      map['enableRecitationChecker'] =
          Variable<bool>(enableRecitationChecker.value);
    }
    if (zoomState.present) {
      map['zoomState'] = Variable<String>(zoomState.value);
    }
    if (reminderDayNumber.present) {
      map['reminderDayNumber'] = Variable<int>(reminderDayNumber.value);
    }
    if (reminderTime.present) {
      map['reminderTime'] = Variable<String>(reminderTime.value);
    }
    if (openingTimestamp.present) {
      map['openingTimestamp'] = Variable<int>(openingTimestamp.value);
    }
    if (enableDownloadAudio.present) {
      map['enableDownloadAudio'] = Variable<bool>(enableDownloadAudio.value);
    }
    if (audioSize.present) {
      map['audioSize'] = Variable<int>(audioSize.value);
    }
    if (isAudioDownloaded.present) {
      map['isAudioDownloaded'] = Variable<bool>(isAudioDownloaded.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TrackEntityCompanion(')
          ..write('id: $id, ')
          ..write('startJuzNumber: $startJuzNumber, ')
          ..write('startPageNumber: $startPageNumber, ')
          ..write('startSurahNumber: $startSurahNumber, ')
          ..write('startVerseNumber: $startVerseNumber, ')
          ..write('endJuzNumber: $endJuzNumber, ')
          ..write('endPageNumber: $endPageNumber, ')
          ..write('endSurahNumber: $endSurahNumber, ')
          ..write('endVerseNumber: $endVerseNumber, ')
          ..write('reciter: $reciter, ')
          ..write('reciteVerseCount: $reciteVerseCount, ')
          ..write('isLoop: $isLoop, ')
          ..write('enableEnglishRecitation: $enableEnglishRecitation, ')
          ..write('bluetoothAddress: $bluetoothAddress, ')
          ..write('type: $type, ')
          ..write('designCode: $designCode, ')
          ..write('lastHighlightedVerseKey: $lastHighlightedVerseKey, ')
          ..write('lastRecitedWord: $lastRecitedWord, ')
          ..write('name: $name, ')
          ..write('rangePortion: $rangePortion, ')
          ..write('scheduleDayNumber: $scheduleDayNumber, ')
          ..write('scheduleTime: $scheduleTime, ')
          ..write('enableRecitationChecker: $enableRecitationChecker, ')
          ..write('zoomState: $zoomState, ')
          ..write('reminderDayNumber: $reminderDayNumber, ')
          ..write('reminderTime: $reminderTime, ')
          ..write('openingTimestamp: $openingTimestamp, ')
          ..write('enableDownloadAudio: $enableDownloadAudio, ')
          ..write('audioSize: $audioSize, ')
          ..write('isAudioDownloaded: $isAudioDownloaded')
          ..write(')'))
        .toString();
  }
}

class $AlignmentWordResultEntityTable extends AlignmentWordResultEntity
    with
        TableInfo<$AlignmentWordResultEntityTable,
            AlignmentWordResultEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AlignmentWordResultEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _trackIdMeta =
      const VerificationMeta('trackId');
  @override
  late final GeneratedColumn<int> trackId = GeneratedColumn<int>(
      'trackId', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _verseIdMeta =
      const VerificationMeta('verseId');
  @override
  late final GeneratedColumn<int> verseId = GeneratedColumn<int>(
      'verseId', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _verseKeyMeta =
      const VerificationMeta('verseKey');
  @override
  late final GeneratedColumn<String> verseKey = GeneratedColumn<String>(
      'verseKey', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _wordIdMeta = const VerificationMeta('wordId');
  @override
  late final GeneratedColumn<int> wordId = GeneratedColumn<int>(
      'wordId', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _wordIndexMeta =
      const VerificationMeta('wordIndex');
  @override
  late final GeneratedColumn<int> wordIndex = GeneratedColumn<int>(
      'wordIndex', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _pageNumberMeta =
      const VerificationMeta('pageNumber');
  @override
  late final GeneratedColumn<int> pageNumber = GeneratedColumn<int>(
      'pageNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _verseCodeV1Meta =
      const VerificationMeta('verseCodeV1');
  @override
  late final GeneratedColumn<String> verseCodeV1 = GeneratedColumn<String>(
      'verseCodeV1', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _codeV1Meta = const VerificationMeta('codeV1');
  @override
  late final GeneratedColumn<String> codeV1 = GeneratedColumn<String>(
      'codeV1', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _isCorrectMeta =
      const VerificationMeta('isCorrect');
  @override
  late final GeneratedColumn<bool> isCorrect = GeneratedColumn<bool>(
      'isCorrect', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("isCorrect" IN (0, 1))'));
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  @override
  late final GeneratedColumn<int> time = GeneratedColumn<int>(
      'time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        trackId,
        verseId,
        verseKey,
        wordId,
        wordIndex,
        pageNumber,
        verseCodeV1,
        codeV1,
        type,
        isCorrect,
        time
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'alignment_word_results';
  @override
  VerificationContext validateIntegrity(
      Insertable<AlignmentWordResultEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('trackId')) {
      context.handle(_trackIdMeta,
          trackId.isAcceptableOrUnknown(data['trackId']!, _trackIdMeta));
    } else if (isInserting) {
      context.missing(_trackIdMeta);
    }
    if (data.containsKey('verseId')) {
      context.handle(_verseIdMeta,
          verseId.isAcceptableOrUnknown(data['verseId']!, _verseIdMeta));
    } else if (isInserting) {
      context.missing(_verseIdMeta);
    }
    if (data.containsKey('verseKey')) {
      context.handle(_verseKeyMeta,
          verseKey.isAcceptableOrUnknown(data['verseKey']!, _verseKeyMeta));
    } else if (isInserting) {
      context.missing(_verseKeyMeta);
    }
    if (data.containsKey('wordId')) {
      context.handle(_wordIdMeta,
          wordId.isAcceptableOrUnknown(data['wordId']!, _wordIdMeta));
    } else if (isInserting) {
      context.missing(_wordIdMeta);
    }
    if (data.containsKey('wordIndex')) {
      context.handle(_wordIndexMeta,
          wordIndex.isAcceptableOrUnknown(data['wordIndex']!, _wordIndexMeta));
    } else if (isInserting) {
      context.missing(_wordIndexMeta);
    }
    if (data.containsKey('pageNumber')) {
      context.handle(
          _pageNumberMeta,
          pageNumber.isAcceptableOrUnknown(
              data['pageNumber']!, _pageNumberMeta));
    } else if (isInserting) {
      context.missing(_pageNumberMeta);
    }
    if (data.containsKey('verseCodeV1')) {
      context.handle(
          _verseCodeV1Meta,
          verseCodeV1.isAcceptableOrUnknown(
              data['verseCodeV1']!, _verseCodeV1Meta));
    } else if (isInserting) {
      context.missing(_verseCodeV1Meta);
    }
    if (data.containsKey('codeV1')) {
      context.handle(_codeV1Meta,
          codeV1.isAcceptableOrUnknown(data['codeV1']!, _codeV1Meta));
    } else if (isInserting) {
      context.missing(_codeV1Meta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('isCorrect')) {
      context.handle(_isCorrectMeta,
          isCorrect.isAcceptableOrUnknown(data['isCorrect']!, _isCorrectMeta));
    } else if (isInserting) {
      context.missing(_isCorrectMeta);
    }
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    } else if (isInserting) {
      context.missing(_timeMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AlignmentWordResultEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AlignmentWordResultEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      trackId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}trackId'])!,
      verseId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}verseId'])!,
      verseKey: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}verseKey'])!,
      wordId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}wordId'])!,
      wordIndex: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}wordIndex'])!,
      pageNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}pageNumber'])!,
      verseCodeV1: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}verseCodeV1'])!,
      codeV1: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}codeV1'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      isCorrect: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isCorrect'])!,
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}time'])!,
    );
  }

  @override
  $AlignmentWordResultEntityTable createAlias(String alias) {
    return $AlignmentWordResultEntityTable(attachedDatabase, alias);
  }
}

class AlignmentWordResultEntityData extends DataClass
    implements Insertable<AlignmentWordResultEntityData> {
  final String id;
  final int trackId;
  final int verseId;
  final String verseKey;
  final int wordId;
  final int wordIndex;
  final int pageNumber;
  final String verseCodeV1;
  final String codeV1;
  final String type;
  final bool isCorrect;
  final int time;
  const AlignmentWordResultEntityData(
      {required this.id,
      required this.trackId,
      required this.verseId,
      required this.verseKey,
      required this.wordId,
      required this.wordIndex,
      required this.pageNumber,
      required this.verseCodeV1,
      required this.codeV1,
      required this.type,
      required this.isCorrect,
      required this.time});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['trackId'] = Variable<int>(trackId);
    map['verseId'] = Variable<int>(verseId);
    map['verseKey'] = Variable<String>(verseKey);
    map['wordId'] = Variable<int>(wordId);
    map['wordIndex'] = Variable<int>(wordIndex);
    map['pageNumber'] = Variable<int>(pageNumber);
    map['verseCodeV1'] = Variable<String>(verseCodeV1);
    map['codeV1'] = Variable<String>(codeV1);
    map['type'] = Variable<String>(type);
    map['isCorrect'] = Variable<bool>(isCorrect);
    map['time'] = Variable<int>(time);
    return map;
  }

  AlignmentWordResultEntityCompanion toCompanion(bool nullToAbsent) {
    return AlignmentWordResultEntityCompanion(
      id: Value(id),
      trackId: Value(trackId),
      verseId: Value(verseId),
      verseKey: Value(verseKey),
      wordId: Value(wordId),
      wordIndex: Value(wordIndex),
      pageNumber: Value(pageNumber),
      verseCodeV1: Value(verseCodeV1),
      codeV1: Value(codeV1),
      type: Value(type),
      isCorrect: Value(isCorrect),
      time: Value(time),
    );
  }

  factory AlignmentWordResultEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AlignmentWordResultEntityData(
      id: serializer.fromJson<String>(json['id']),
      trackId: serializer.fromJson<int>(json['trackId']),
      verseId: serializer.fromJson<int>(json['verseId']),
      verseKey: serializer.fromJson<String>(json['verseKey']),
      wordId: serializer.fromJson<int>(json['wordId']),
      wordIndex: serializer.fromJson<int>(json['wordIndex']),
      pageNumber: serializer.fromJson<int>(json['pageNumber']),
      verseCodeV1: serializer.fromJson<String>(json['verseCodeV1']),
      codeV1: serializer.fromJson<String>(json['codeV1']),
      type: serializer.fromJson<String>(json['type']),
      isCorrect: serializer.fromJson<bool>(json['isCorrect']),
      time: serializer.fromJson<int>(json['time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'trackId': serializer.toJson<int>(trackId),
      'verseId': serializer.toJson<int>(verseId),
      'verseKey': serializer.toJson<String>(verseKey),
      'wordId': serializer.toJson<int>(wordId),
      'wordIndex': serializer.toJson<int>(wordIndex),
      'pageNumber': serializer.toJson<int>(pageNumber),
      'verseCodeV1': serializer.toJson<String>(verseCodeV1),
      'codeV1': serializer.toJson<String>(codeV1),
      'type': serializer.toJson<String>(type),
      'isCorrect': serializer.toJson<bool>(isCorrect),
      'time': serializer.toJson<int>(time),
    };
  }

  AlignmentWordResultEntityData copyWith(
          {String? id,
          int? trackId,
          int? verseId,
          String? verseKey,
          int? wordId,
          int? wordIndex,
          int? pageNumber,
          String? verseCodeV1,
          String? codeV1,
          String? type,
          bool? isCorrect,
          int? time}) =>
      AlignmentWordResultEntityData(
        id: id ?? this.id,
        trackId: trackId ?? this.trackId,
        verseId: verseId ?? this.verseId,
        verseKey: verseKey ?? this.verseKey,
        wordId: wordId ?? this.wordId,
        wordIndex: wordIndex ?? this.wordIndex,
        pageNumber: pageNumber ?? this.pageNumber,
        verseCodeV1: verseCodeV1 ?? this.verseCodeV1,
        codeV1: codeV1 ?? this.codeV1,
        type: type ?? this.type,
        isCorrect: isCorrect ?? this.isCorrect,
        time: time ?? this.time,
      );
  AlignmentWordResultEntityData copyWithCompanion(
      AlignmentWordResultEntityCompanion data) {
    return AlignmentWordResultEntityData(
      id: data.id.present ? data.id.value : this.id,
      trackId: data.trackId.present ? data.trackId.value : this.trackId,
      verseId: data.verseId.present ? data.verseId.value : this.verseId,
      verseKey: data.verseKey.present ? data.verseKey.value : this.verseKey,
      wordId: data.wordId.present ? data.wordId.value : this.wordId,
      wordIndex: data.wordIndex.present ? data.wordIndex.value : this.wordIndex,
      pageNumber:
          data.pageNumber.present ? data.pageNumber.value : this.pageNumber,
      verseCodeV1:
          data.verseCodeV1.present ? data.verseCodeV1.value : this.verseCodeV1,
      codeV1: data.codeV1.present ? data.codeV1.value : this.codeV1,
      type: data.type.present ? data.type.value : this.type,
      isCorrect: data.isCorrect.present ? data.isCorrect.value : this.isCorrect,
      time: data.time.present ? data.time.value : this.time,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AlignmentWordResultEntityData(')
          ..write('id: $id, ')
          ..write('trackId: $trackId, ')
          ..write('verseId: $verseId, ')
          ..write('verseKey: $verseKey, ')
          ..write('wordId: $wordId, ')
          ..write('wordIndex: $wordIndex, ')
          ..write('pageNumber: $pageNumber, ')
          ..write('verseCodeV1: $verseCodeV1, ')
          ..write('codeV1: $codeV1, ')
          ..write('type: $type, ')
          ..write('isCorrect: $isCorrect, ')
          ..write('time: $time')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, trackId, verseId, verseKey, wordId,
      wordIndex, pageNumber, verseCodeV1, codeV1, type, isCorrect, time);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AlignmentWordResultEntityData &&
          other.id == this.id &&
          other.trackId == this.trackId &&
          other.verseId == this.verseId &&
          other.verseKey == this.verseKey &&
          other.wordId == this.wordId &&
          other.wordIndex == this.wordIndex &&
          other.pageNumber == this.pageNumber &&
          other.verseCodeV1 == this.verseCodeV1 &&
          other.codeV1 == this.codeV1 &&
          other.type == this.type &&
          other.isCorrect == this.isCorrect &&
          other.time == this.time);
}

class AlignmentWordResultEntityCompanion
    extends UpdateCompanion<AlignmentWordResultEntityData> {
  final Value<String> id;
  final Value<int> trackId;
  final Value<int> verseId;
  final Value<String> verseKey;
  final Value<int> wordId;
  final Value<int> wordIndex;
  final Value<int> pageNumber;
  final Value<String> verseCodeV1;
  final Value<String> codeV1;
  final Value<String> type;
  final Value<bool> isCorrect;
  final Value<int> time;
  final Value<int> rowid;
  const AlignmentWordResultEntityCompanion({
    this.id = const Value.absent(),
    this.trackId = const Value.absent(),
    this.verseId = const Value.absent(),
    this.verseKey = const Value.absent(),
    this.wordId = const Value.absent(),
    this.wordIndex = const Value.absent(),
    this.pageNumber = const Value.absent(),
    this.verseCodeV1 = const Value.absent(),
    this.codeV1 = const Value.absent(),
    this.type = const Value.absent(),
    this.isCorrect = const Value.absent(),
    this.time = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AlignmentWordResultEntityCompanion.insert({
    required String id,
    required int trackId,
    required int verseId,
    required String verseKey,
    required int wordId,
    required int wordIndex,
    required int pageNumber,
    required String verseCodeV1,
    required String codeV1,
    required String type,
    required bool isCorrect,
    required int time,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        trackId = Value(trackId),
        verseId = Value(verseId),
        verseKey = Value(verseKey),
        wordId = Value(wordId),
        wordIndex = Value(wordIndex),
        pageNumber = Value(pageNumber),
        verseCodeV1 = Value(verseCodeV1),
        codeV1 = Value(codeV1),
        type = Value(type),
        isCorrect = Value(isCorrect),
        time = Value(time);
  static Insertable<AlignmentWordResultEntityData> custom({
    Expression<String>? id,
    Expression<int>? trackId,
    Expression<int>? verseId,
    Expression<String>? verseKey,
    Expression<int>? wordId,
    Expression<int>? wordIndex,
    Expression<int>? pageNumber,
    Expression<String>? verseCodeV1,
    Expression<String>? codeV1,
    Expression<String>? type,
    Expression<bool>? isCorrect,
    Expression<int>? time,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (trackId != null) 'trackId': trackId,
      if (verseId != null) 'verseId': verseId,
      if (verseKey != null) 'verseKey': verseKey,
      if (wordId != null) 'wordId': wordId,
      if (wordIndex != null) 'wordIndex': wordIndex,
      if (pageNumber != null) 'pageNumber': pageNumber,
      if (verseCodeV1 != null) 'verseCodeV1': verseCodeV1,
      if (codeV1 != null) 'codeV1': codeV1,
      if (type != null) 'type': type,
      if (isCorrect != null) 'isCorrect': isCorrect,
      if (time != null) 'time': time,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AlignmentWordResultEntityCompanion copyWith(
      {Value<String>? id,
      Value<int>? trackId,
      Value<int>? verseId,
      Value<String>? verseKey,
      Value<int>? wordId,
      Value<int>? wordIndex,
      Value<int>? pageNumber,
      Value<String>? verseCodeV1,
      Value<String>? codeV1,
      Value<String>? type,
      Value<bool>? isCorrect,
      Value<int>? time,
      Value<int>? rowid}) {
    return AlignmentWordResultEntityCompanion(
      id: id ?? this.id,
      trackId: trackId ?? this.trackId,
      verseId: verseId ?? this.verseId,
      verseKey: verseKey ?? this.verseKey,
      wordId: wordId ?? this.wordId,
      wordIndex: wordIndex ?? this.wordIndex,
      pageNumber: pageNumber ?? this.pageNumber,
      verseCodeV1: verseCodeV1 ?? this.verseCodeV1,
      codeV1: codeV1 ?? this.codeV1,
      type: type ?? this.type,
      isCorrect: isCorrect ?? this.isCorrect,
      time: time ?? this.time,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (trackId.present) {
      map['trackId'] = Variable<int>(trackId.value);
    }
    if (verseId.present) {
      map['verseId'] = Variable<int>(verseId.value);
    }
    if (verseKey.present) {
      map['verseKey'] = Variable<String>(verseKey.value);
    }
    if (wordId.present) {
      map['wordId'] = Variable<int>(wordId.value);
    }
    if (wordIndex.present) {
      map['wordIndex'] = Variable<int>(wordIndex.value);
    }
    if (pageNumber.present) {
      map['pageNumber'] = Variable<int>(pageNumber.value);
    }
    if (verseCodeV1.present) {
      map['verseCodeV1'] = Variable<String>(verseCodeV1.value);
    }
    if (codeV1.present) {
      map['codeV1'] = Variable<String>(codeV1.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (isCorrect.present) {
      map['isCorrect'] = Variable<bool>(isCorrect.value);
    }
    if (time.present) {
      map['time'] = Variable<int>(time.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AlignmentWordResultEntityCompanion(')
          ..write('id: $id, ')
          ..write('trackId: $trackId, ')
          ..write('verseId: $verseId, ')
          ..write('verseKey: $verseKey, ')
          ..write('wordId: $wordId, ')
          ..write('wordIndex: $wordIndex, ')
          ..write('pageNumber: $pageNumber, ')
          ..write('verseCodeV1: $verseCodeV1, ')
          ..write('codeV1: $codeV1, ')
          ..write('type: $type, ')
          ..write('isCorrect: $isCorrect, ')
          ..write('time: $time, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $FileSizeCacheEntityTable extends FileSizeCacheEntity
    with TableInfo<$FileSizeCacheEntityTable, FileSizeCacheEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $FileSizeCacheEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _fileNameMeta =
      const VerificationMeta('fileName');
  @override
  late final GeneratedColumn<String> fileName = GeneratedColumn<String>(
      'fileName', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _sizeMeta = const VerificationMeta('size');
  @override
  late final GeneratedColumn<int> size = GeneratedColumn<int>(
      'size', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _lastModifiedMeta =
      const VerificationMeta('lastModified');
  @override
  late final GeneratedColumn<DateTime> lastModified = GeneratedColumn<DateTime>(
      'lastModified', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [fileName, size, lastModified];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'fileSizeCaches';
  @override
  VerificationContext validateIntegrity(
      Insertable<FileSizeCacheEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('fileName')) {
      context.handle(_fileNameMeta,
          fileName.isAcceptableOrUnknown(data['fileName']!, _fileNameMeta));
    } else if (isInserting) {
      context.missing(_fileNameMeta);
    }
    if (data.containsKey('size')) {
      context.handle(
          _sizeMeta, size.isAcceptableOrUnknown(data['size']!, _sizeMeta));
    } else if (isInserting) {
      context.missing(_sizeMeta);
    }
    if (data.containsKey('lastModified')) {
      context.handle(
          _lastModifiedMeta,
          lastModified.isAcceptableOrUnknown(
              data['lastModified']!, _lastModifiedMeta));
    } else if (isInserting) {
      context.missing(_lastModifiedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {fileName};
  @override
  FileSizeCacheEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return FileSizeCacheEntityData(
      fileName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}fileName'])!,
      size: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}size'])!,
      lastModified: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}lastModified'])!,
    );
  }

  @override
  $FileSizeCacheEntityTable createAlias(String alias) {
    return $FileSizeCacheEntityTable(attachedDatabase, alias);
  }
}

class FileSizeCacheEntityData extends DataClass
    implements Insertable<FileSizeCacheEntityData> {
  final String fileName;
  final int size;
  final DateTime lastModified;
  const FileSizeCacheEntityData(
      {required this.fileName, required this.size, required this.lastModified});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['fileName'] = Variable<String>(fileName);
    map['size'] = Variable<int>(size);
    map['lastModified'] = Variable<DateTime>(lastModified);
    return map;
  }

  FileSizeCacheEntityCompanion toCompanion(bool nullToAbsent) {
    return FileSizeCacheEntityCompanion(
      fileName: Value(fileName),
      size: Value(size),
      lastModified: Value(lastModified),
    );
  }

  factory FileSizeCacheEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return FileSizeCacheEntityData(
      fileName: serializer.fromJson<String>(json['fileName']),
      size: serializer.fromJson<int>(json['size']),
      lastModified: serializer.fromJson<DateTime>(json['lastModified']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'fileName': serializer.toJson<String>(fileName),
      'size': serializer.toJson<int>(size),
      'lastModified': serializer.toJson<DateTime>(lastModified),
    };
  }

  FileSizeCacheEntityData copyWith(
          {String? fileName, int? size, DateTime? lastModified}) =>
      FileSizeCacheEntityData(
        fileName: fileName ?? this.fileName,
        size: size ?? this.size,
        lastModified: lastModified ?? this.lastModified,
      );
  FileSizeCacheEntityData copyWithCompanion(FileSizeCacheEntityCompanion data) {
    return FileSizeCacheEntityData(
      fileName: data.fileName.present ? data.fileName.value : this.fileName,
      size: data.size.present ? data.size.value : this.size,
      lastModified: data.lastModified.present
          ? data.lastModified.value
          : this.lastModified,
    );
  }

  @override
  String toString() {
    return (StringBuffer('FileSizeCacheEntityData(')
          ..write('fileName: $fileName, ')
          ..write('size: $size, ')
          ..write('lastModified: $lastModified')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(fileName, size, lastModified);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is FileSizeCacheEntityData &&
          other.fileName == this.fileName &&
          other.size == this.size &&
          other.lastModified == this.lastModified);
}

class FileSizeCacheEntityCompanion
    extends UpdateCompanion<FileSizeCacheEntityData> {
  final Value<String> fileName;
  final Value<int> size;
  final Value<DateTime> lastModified;
  final Value<int> rowid;
  const FileSizeCacheEntityCompanion({
    this.fileName = const Value.absent(),
    this.size = const Value.absent(),
    this.lastModified = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  FileSizeCacheEntityCompanion.insert({
    required String fileName,
    required int size,
    required DateTime lastModified,
    this.rowid = const Value.absent(),
  })  : fileName = Value(fileName),
        size = Value(size),
        lastModified = Value(lastModified);
  static Insertable<FileSizeCacheEntityData> custom({
    Expression<String>? fileName,
    Expression<int>? size,
    Expression<DateTime>? lastModified,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (fileName != null) 'fileName': fileName,
      if (size != null) 'size': size,
      if (lastModified != null) 'lastModified': lastModified,
      if (rowid != null) 'rowid': rowid,
    });
  }

  FileSizeCacheEntityCompanion copyWith(
      {Value<String>? fileName,
      Value<int>? size,
      Value<DateTime>? lastModified,
      Value<int>? rowid}) {
    return FileSizeCacheEntityCompanion(
      fileName: fileName ?? this.fileName,
      size: size ?? this.size,
      lastModified: lastModified ?? this.lastModified,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (fileName.present) {
      map['fileName'] = Variable<String>(fileName.value);
    }
    if (size.present) {
      map['size'] = Variable<int>(size.value);
    }
    if (lastModified.present) {
      map['lastModified'] = Variable<DateTime>(lastModified.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('FileSizeCacheEntityCompanion(')
          ..write('fileName: $fileName, ')
          ..write('size: $size, ')
          ..write('lastModified: $lastModified, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $RecentPageEntityTable extends RecentPageEntity
    with TableInfo<$RecentPageEntityTable, RecentPageEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $RecentPageEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _pageNumberMeta =
      const VerificationMeta('pageNumber');
  @override
  late final GeneratedColumn<int> pageNumber = GeneratedColumn<int>(
      'pageNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  @override
  late final GeneratedColumn<int> time = GeneratedColumn<int>(
      'time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [id, pageNumber, time];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'recent_pages';
  @override
  VerificationContext validateIntegrity(
      Insertable<RecentPageEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('pageNumber')) {
      context.handle(
          _pageNumberMeta,
          pageNumber.isAcceptableOrUnknown(
              data['pageNumber']!, _pageNumberMeta));
    } else if (isInserting) {
      context.missing(_pageNumberMeta);
    }
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    } else if (isInserting) {
      context.missing(_timeMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  RecentPageEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return RecentPageEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      pageNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}pageNumber'])!,
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}time'])!,
    );
  }

  @override
  $RecentPageEntityTable createAlias(String alias) {
    return $RecentPageEntityTable(attachedDatabase, alias);
  }
}

class RecentPageEntityData extends DataClass
    implements Insertable<RecentPageEntityData> {
  final String id;
  final int pageNumber;
  final int time;
  const RecentPageEntityData(
      {required this.id, required this.pageNumber, required this.time});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['pageNumber'] = Variable<int>(pageNumber);
    map['time'] = Variable<int>(time);
    return map;
  }

  RecentPageEntityCompanion toCompanion(bool nullToAbsent) {
    return RecentPageEntityCompanion(
      id: Value(id),
      pageNumber: Value(pageNumber),
      time: Value(time),
    );
  }

  factory RecentPageEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return RecentPageEntityData(
      id: serializer.fromJson<String>(json['id']),
      pageNumber: serializer.fromJson<int>(json['pageNumber']),
      time: serializer.fromJson<int>(json['time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'pageNumber': serializer.toJson<int>(pageNumber),
      'time': serializer.toJson<int>(time),
    };
  }

  RecentPageEntityData copyWith({String? id, int? pageNumber, int? time}) =>
      RecentPageEntityData(
        id: id ?? this.id,
        pageNumber: pageNumber ?? this.pageNumber,
        time: time ?? this.time,
      );
  RecentPageEntityData copyWithCompanion(RecentPageEntityCompanion data) {
    return RecentPageEntityData(
      id: data.id.present ? data.id.value : this.id,
      pageNumber:
          data.pageNumber.present ? data.pageNumber.value : this.pageNumber,
      time: data.time.present ? data.time.value : this.time,
    );
  }

  @override
  String toString() {
    return (StringBuffer('RecentPageEntityData(')
          ..write('id: $id, ')
          ..write('pageNumber: $pageNumber, ')
          ..write('time: $time')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, pageNumber, time);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is RecentPageEntityData &&
          other.id == this.id &&
          other.pageNumber == this.pageNumber &&
          other.time == this.time);
}

class RecentPageEntityCompanion extends UpdateCompanion<RecentPageEntityData> {
  final Value<String> id;
  final Value<int> pageNumber;
  final Value<int> time;
  final Value<int> rowid;
  const RecentPageEntityCompanion({
    this.id = const Value.absent(),
    this.pageNumber = const Value.absent(),
    this.time = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  RecentPageEntityCompanion.insert({
    required String id,
    required int pageNumber,
    required int time,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        pageNumber = Value(pageNumber),
        time = Value(time);
  static Insertable<RecentPageEntityData> custom({
    Expression<String>? id,
    Expression<int>? pageNumber,
    Expression<int>? time,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (pageNumber != null) 'pageNumber': pageNumber,
      if (time != null) 'time': time,
      if (rowid != null) 'rowid': rowid,
    });
  }

  RecentPageEntityCompanion copyWith(
      {Value<String>? id,
      Value<int>? pageNumber,
      Value<int>? time,
      Value<int>? rowid}) {
    return RecentPageEntityCompanion(
      id: id ?? this.id,
      pageNumber: pageNumber ?? this.pageNumber,
      time: time ?? this.time,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (pageNumber.present) {
      map['pageNumber'] = Variable<int>(pageNumber.value);
    }
    if (time.present) {
      map['time'] = Variable<int>(time.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('RecentPageEntityCompanion(')
          ..write('id: $id, ')
          ..write('pageNumber: $pageNumber, ')
          ..write('time: $time, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $BookmarkEntityTable extends BookmarkEntity
    with TableInfo<$BookmarkEntityTable, BookmarkEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $BookmarkEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _verseKeyMeta =
      const VerificationMeta('verseKey');
  @override
  late final GeneratedColumn<String> verseKey = GeneratedColumn<String>(
      'verseKey', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _pageNumberMeta =
      const VerificationMeta('pageNumber');
  @override
  late final GeneratedColumn<int> pageNumber = GeneratedColumn<int>(
      'pageNumber', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  @override
  late final GeneratedColumn<int> time = GeneratedColumn<int>(
      'time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [verseKey, pageNumber, time];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'bookmarks';
  @override
  VerificationContext validateIntegrity(Insertable<BookmarkEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('verseKey')) {
      context.handle(_verseKeyMeta,
          verseKey.isAcceptableOrUnknown(data['verseKey']!, _verseKeyMeta));
    } else if (isInserting) {
      context.missing(_verseKeyMeta);
    }
    if (data.containsKey('pageNumber')) {
      context.handle(
          _pageNumberMeta,
          pageNumber.isAcceptableOrUnknown(
              data['pageNumber']!, _pageNumberMeta));
    } else if (isInserting) {
      context.missing(_pageNumberMeta);
    }
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    } else if (isInserting) {
      context.missing(_timeMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {verseKey};
  @override
  BookmarkEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return BookmarkEntityData(
      verseKey: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}verseKey'])!,
      pageNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}pageNumber'])!,
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}time'])!,
    );
  }

  @override
  $BookmarkEntityTable createAlias(String alias) {
    return $BookmarkEntityTable(attachedDatabase, alias);
  }
}

class BookmarkEntityData extends DataClass
    implements Insertable<BookmarkEntityData> {
  final String verseKey;
  final int pageNumber;
  final int time;
  const BookmarkEntityData(
      {required this.verseKey, required this.pageNumber, required this.time});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['verseKey'] = Variable<String>(verseKey);
    map['pageNumber'] = Variable<int>(pageNumber);
    map['time'] = Variable<int>(time);
    return map;
  }

  BookmarkEntityCompanion toCompanion(bool nullToAbsent) {
    return BookmarkEntityCompanion(
      verseKey: Value(verseKey),
      pageNumber: Value(pageNumber),
      time: Value(time),
    );
  }

  factory BookmarkEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return BookmarkEntityData(
      verseKey: serializer.fromJson<String>(json['verseKey']),
      pageNumber: serializer.fromJson<int>(json['pageNumber']),
      time: serializer.fromJson<int>(json['time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'verseKey': serializer.toJson<String>(verseKey),
      'pageNumber': serializer.toJson<int>(pageNumber),
      'time': serializer.toJson<int>(time),
    };
  }

  BookmarkEntityData copyWith({String? verseKey, int? pageNumber, int? time}) =>
      BookmarkEntityData(
        verseKey: verseKey ?? this.verseKey,
        pageNumber: pageNumber ?? this.pageNumber,
        time: time ?? this.time,
      );
  BookmarkEntityData copyWithCompanion(BookmarkEntityCompanion data) {
    return BookmarkEntityData(
      verseKey: data.verseKey.present ? data.verseKey.value : this.verseKey,
      pageNumber:
          data.pageNumber.present ? data.pageNumber.value : this.pageNumber,
      time: data.time.present ? data.time.value : this.time,
    );
  }

  @override
  String toString() {
    return (StringBuffer('BookmarkEntityData(')
          ..write('verseKey: $verseKey, ')
          ..write('pageNumber: $pageNumber, ')
          ..write('time: $time')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(verseKey, pageNumber, time);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is BookmarkEntityData &&
          other.verseKey == this.verseKey &&
          other.pageNumber == this.pageNumber &&
          other.time == this.time);
}

class BookmarkEntityCompanion extends UpdateCompanion<BookmarkEntityData> {
  final Value<String> verseKey;
  final Value<int> pageNumber;
  final Value<int> time;
  final Value<int> rowid;
  const BookmarkEntityCompanion({
    this.verseKey = const Value.absent(),
    this.pageNumber = const Value.absent(),
    this.time = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  BookmarkEntityCompanion.insert({
    required String verseKey,
    required int pageNumber,
    required int time,
    this.rowid = const Value.absent(),
  })  : verseKey = Value(verseKey),
        pageNumber = Value(pageNumber),
        time = Value(time);
  static Insertable<BookmarkEntityData> custom({
    Expression<String>? verseKey,
    Expression<int>? pageNumber,
    Expression<int>? time,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (verseKey != null) 'verseKey': verseKey,
      if (pageNumber != null) 'pageNumber': pageNumber,
      if (time != null) 'time': time,
      if (rowid != null) 'rowid': rowid,
    });
  }

  BookmarkEntityCompanion copyWith(
      {Value<String>? verseKey,
      Value<int>? pageNumber,
      Value<int>? time,
      Value<int>? rowid}) {
    return BookmarkEntityCompanion(
      verseKey: verseKey ?? this.verseKey,
      pageNumber: pageNumber ?? this.pageNumber,
      time: time ?? this.time,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (verseKey.present) {
      map['verseKey'] = Variable<String>(verseKey.value);
    }
    if (pageNumber.present) {
      map['pageNumber'] = Variable<int>(pageNumber.value);
    }
    if (time.present) {
      map['time'] = Variable<int>(time.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('BookmarkEntityCompanion(')
          ..write('verseKey: $verseKey, ')
          ..write('pageNumber: $pageNumber, ')
          ..write('time: $time, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$QuranDatabase extends GeneratedDatabase {
  _$QuranDatabase(QueryExecutor e) : super(e);
  $QuranDatabaseManager get managers => $QuranDatabaseManager(this);
  late final $TrackEntityTable trackEntity = $TrackEntityTable(this);
  late final $AlignmentWordResultEntityTable alignmentWordResultEntity =
      $AlignmentWordResultEntityTable(this);
  late final $FileSizeCacheEntityTable fileSizeCacheEntity =
      $FileSizeCacheEntityTable(this);
  late final $RecentPageEntityTable recentPageEntity =
      $RecentPageEntityTable(this);
  late final $BookmarkEntityTable bookmarkEntity = $BookmarkEntityTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        trackEntity,
        alignmentWordResultEntity,
        fileSizeCacheEntity,
        recentPageEntity,
        bookmarkEntity
      ];
}

typedef $$TrackEntityTableCreateCompanionBuilder = TrackEntityCompanion
    Function({
  Value<int> id,
  required int startJuzNumber,
  required int startPageNumber,
  required int startSurahNumber,
  required int startVerseNumber,
  required int endJuzNumber,
  required int endPageNumber,
  required int endSurahNumber,
  required int endVerseNumber,
  Value<String?> reciter,
  required int reciteVerseCount,
  required bool isLoop,
  required bool enableEnglishRecitation,
  Value<String?> bluetoothAddress,
  Value<TrackType?> type,
  Value<MushafCode?> designCode,
  Value<String?> lastHighlightedVerseKey,
  Value<LastRecitedWord?> lastRecitedWord,
  Value<String?> name,
  required TrackRangePortion rangePortion,
  Value<int?> scheduleDayNumber,
  Value<String?> scheduleTime,
  required bool enableRecitationChecker,
  required String zoomState,
  Value<int?> reminderDayNumber,
  Value<String?> reminderTime,
  Value<int?> openingTimestamp,
  required bool enableDownloadAudio,
  required int audioSize,
  required bool isAudioDownloaded,
});
typedef $$TrackEntityTableUpdateCompanionBuilder = TrackEntityCompanion
    Function({
  Value<int> id,
  Value<int> startJuzNumber,
  Value<int> startPageNumber,
  Value<int> startSurahNumber,
  Value<int> startVerseNumber,
  Value<int> endJuzNumber,
  Value<int> endPageNumber,
  Value<int> endSurahNumber,
  Value<int> endVerseNumber,
  Value<String?> reciter,
  Value<int> reciteVerseCount,
  Value<bool> isLoop,
  Value<bool> enableEnglishRecitation,
  Value<String?> bluetoothAddress,
  Value<TrackType?> type,
  Value<MushafCode?> designCode,
  Value<String?> lastHighlightedVerseKey,
  Value<LastRecitedWord?> lastRecitedWord,
  Value<String?> name,
  Value<TrackRangePortion> rangePortion,
  Value<int?> scheduleDayNumber,
  Value<String?> scheduleTime,
  Value<bool> enableRecitationChecker,
  Value<String> zoomState,
  Value<int?> reminderDayNumber,
  Value<String?> reminderTime,
  Value<int?> openingTimestamp,
  Value<bool> enableDownloadAudio,
  Value<int> audioSize,
  Value<bool> isAudioDownloaded,
});

class $$TrackEntityTableFilterComposer
    extends Composer<_$QuranDatabase, $TrackEntityTable> {
  $$TrackEntityTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get startJuzNumber => $composableBuilder(
      column: $table.startJuzNumber,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get startPageNumber => $composableBuilder(
      column: $table.startPageNumber,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get startSurahNumber => $composableBuilder(
      column: $table.startSurahNumber,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get startVerseNumber => $composableBuilder(
      column: $table.startVerseNumber,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get endJuzNumber => $composableBuilder(
      column: $table.endJuzNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get endPageNumber => $composableBuilder(
      column: $table.endPageNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get endSurahNumber => $composableBuilder(
      column: $table.endSurahNumber,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get endVerseNumber => $composableBuilder(
      column: $table.endVerseNumber,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get reciter => $composableBuilder(
      column: $table.reciter, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get reciteVerseCount => $composableBuilder(
      column: $table.reciteVerseCount,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isLoop => $composableBuilder(
      column: $table.isLoop, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get enableEnglishRecitation => $composableBuilder(
      column: $table.enableEnglishRecitation,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get bluetoothAddress => $composableBuilder(
      column: $table.bluetoothAddress,
      builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<TrackType?, TrackType, String> get type =>
      $composableBuilder(
          column: $table.type,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnWithTypeConverterFilters<MushafCode?, MushafCode, String>
      get designCode => $composableBuilder(
          column: $table.designCode,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get lastHighlightedVerseKey => $composableBuilder(
      column: $table.lastHighlightedVerseKey,
      builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<LastRecitedWord?, LastRecitedWord, String>
      get lastRecitedWord => $composableBuilder(
          column: $table.lastRecitedWord,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<TrackRangePortion, TrackRangePortion, String>
      get rangePortion => $composableBuilder(
          column: $table.rangePortion,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<int> get scheduleDayNumber => $composableBuilder(
      column: $table.scheduleDayNumber,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get scheduleTime => $composableBuilder(
      column: $table.scheduleTime, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get enableRecitationChecker => $composableBuilder(
      column: $table.enableRecitationChecker,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get zoomState => $composableBuilder(
      column: $table.zoomState, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get reminderDayNumber => $composableBuilder(
      column: $table.reminderDayNumber,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get reminderTime => $composableBuilder(
      column: $table.reminderTime, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get openingTimestamp => $composableBuilder(
      column: $table.openingTimestamp,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get enableDownloadAudio => $composableBuilder(
      column: $table.enableDownloadAudio,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get audioSize => $composableBuilder(
      column: $table.audioSize, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isAudioDownloaded => $composableBuilder(
      column: $table.isAudioDownloaded,
      builder: (column) => ColumnFilters(column));
}

class $$TrackEntityTableOrderingComposer
    extends Composer<_$QuranDatabase, $TrackEntityTable> {
  $$TrackEntityTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get startJuzNumber => $composableBuilder(
      column: $table.startJuzNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get startPageNumber => $composableBuilder(
      column: $table.startPageNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get startSurahNumber => $composableBuilder(
      column: $table.startSurahNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get startVerseNumber => $composableBuilder(
      column: $table.startVerseNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get endJuzNumber => $composableBuilder(
      column: $table.endJuzNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get endPageNumber => $composableBuilder(
      column: $table.endPageNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get endSurahNumber => $composableBuilder(
      column: $table.endSurahNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get endVerseNumber => $composableBuilder(
      column: $table.endVerseNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get reciter => $composableBuilder(
      column: $table.reciter, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get reciteVerseCount => $composableBuilder(
      column: $table.reciteVerseCount,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isLoop => $composableBuilder(
      column: $table.isLoop, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get enableEnglishRecitation => $composableBuilder(
      column: $table.enableEnglishRecitation,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get bluetoothAddress => $composableBuilder(
      column: $table.bluetoothAddress,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get designCode => $composableBuilder(
      column: $table.designCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get lastHighlightedVerseKey => $composableBuilder(
      column: $table.lastHighlightedVerseKey,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get lastRecitedWord => $composableBuilder(
      column: $table.lastRecitedWord,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get rangePortion => $composableBuilder(
      column: $table.rangePortion,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get scheduleDayNumber => $composableBuilder(
      column: $table.scheduleDayNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get scheduleTime => $composableBuilder(
      column: $table.scheduleTime,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get enableRecitationChecker => $composableBuilder(
      column: $table.enableRecitationChecker,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get zoomState => $composableBuilder(
      column: $table.zoomState, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get reminderDayNumber => $composableBuilder(
      column: $table.reminderDayNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get reminderTime => $composableBuilder(
      column: $table.reminderTime,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get openingTimestamp => $composableBuilder(
      column: $table.openingTimestamp,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get enableDownloadAudio => $composableBuilder(
      column: $table.enableDownloadAudio,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get audioSize => $composableBuilder(
      column: $table.audioSize, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isAudioDownloaded => $composableBuilder(
      column: $table.isAudioDownloaded,
      builder: (column) => ColumnOrderings(column));
}

class $$TrackEntityTableAnnotationComposer
    extends Composer<_$QuranDatabase, $TrackEntityTable> {
  $$TrackEntityTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get startJuzNumber => $composableBuilder(
      column: $table.startJuzNumber, builder: (column) => column);

  GeneratedColumn<int> get startPageNumber => $composableBuilder(
      column: $table.startPageNumber, builder: (column) => column);

  GeneratedColumn<int> get startSurahNumber => $composableBuilder(
      column: $table.startSurahNumber, builder: (column) => column);

  GeneratedColumn<int> get startVerseNumber => $composableBuilder(
      column: $table.startVerseNumber, builder: (column) => column);

  GeneratedColumn<int> get endJuzNumber => $composableBuilder(
      column: $table.endJuzNumber, builder: (column) => column);

  GeneratedColumn<int> get endPageNumber => $composableBuilder(
      column: $table.endPageNumber, builder: (column) => column);

  GeneratedColumn<int> get endSurahNumber => $composableBuilder(
      column: $table.endSurahNumber, builder: (column) => column);

  GeneratedColumn<int> get endVerseNumber => $composableBuilder(
      column: $table.endVerseNumber, builder: (column) => column);

  GeneratedColumn<String> get reciter =>
      $composableBuilder(column: $table.reciter, builder: (column) => column);

  GeneratedColumn<int> get reciteVerseCount => $composableBuilder(
      column: $table.reciteVerseCount, builder: (column) => column);

  GeneratedColumn<bool> get isLoop =>
      $composableBuilder(column: $table.isLoop, builder: (column) => column);

  GeneratedColumn<bool> get enableEnglishRecitation => $composableBuilder(
      column: $table.enableEnglishRecitation, builder: (column) => column);

  GeneratedColumn<String> get bluetoothAddress => $composableBuilder(
      column: $table.bluetoothAddress, builder: (column) => column);

  GeneratedColumnWithTypeConverter<TrackType?, String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumnWithTypeConverter<MushafCode?, String> get designCode =>
      $composableBuilder(
          column: $table.designCode, builder: (column) => column);

  GeneratedColumn<String> get lastHighlightedVerseKey => $composableBuilder(
      column: $table.lastHighlightedVerseKey, builder: (column) => column);

  GeneratedColumnWithTypeConverter<LastRecitedWord?, String>
      get lastRecitedWord => $composableBuilder(
          column: $table.lastRecitedWord, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumnWithTypeConverter<TrackRangePortion, String>
      get rangePortion => $composableBuilder(
          column: $table.rangePortion, builder: (column) => column);

  GeneratedColumn<int> get scheduleDayNumber => $composableBuilder(
      column: $table.scheduleDayNumber, builder: (column) => column);

  GeneratedColumn<String> get scheduleTime => $composableBuilder(
      column: $table.scheduleTime, builder: (column) => column);

  GeneratedColumn<bool> get enableRecitationChecker => $composableBuilder(
      column: $table.enableRecitationChecker, builder: (column) => column);

  GeneratedColumn<String> get zoomState =>
      $composableBuilder(column: $table.zoomState, builder: (column) => column);

  GeneratedColumn<int> get reminderDayNumber => $composableBuilder(
      column: $table.reminderDayNumber, builder: (column) => column);

  GeneratedColumn<String> get reminderTime => $composableBuilder(
      column: $table.reminderTime, builder: (column) => column);

  GeneratedColumn<int> get openingTimestamp => $composableBuilder(
      column: $table.openingTimestamp, builder: (column) => column);

  GeneratedColumn<bool> get enableDownloadAudio => $composableBuilder(
      column: $table.enableDownloadAudio, builder: (column) => column);

  GeneratedColumn<int> get audioSize =>
      $composableBuilder(column: $table.audioSize, builder: (column) => column);

  GeneratedColumn<bool> get isAudioDownloaded => $composableBuilder(
      column: $table.isAudioDownloaded, builder: (column) => column);
}

class $$TrackEntityTableTableManager extends RootTableManager<
    _$QuranDatabase,
    $TrackEntityTable,
    TrackEntityData,
    $$TrackEntityTableFilterComposer,
    $$TrackEntityTableOrderingComposer,
    $$TrackEntityTableAnnotationComposer,
    $$TrackEntityTableCreateCompanionBuilder,
    $$TrackEntityTableUpdateCompanionBuilder,
    (
      TrackEntityData,
      BaseReferences<_$QuranDatabase, $TrackEntityTable, TrackEntityData>
    ),
    TrackEntityData,
    PrefetchHooks Function()> {
  $$TrackEntityTableTableManager(_$QuranDatabase db, $TrackEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$TrackEntityTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$TrackEntityTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$TrackEntityTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> startJuzNumber = const Value.absent(),
            Value<int> startPageNumber = const Value.absent(),
            Value<int> startSurahNumber = const Value.absent(),
            Value<int> startVerseNumber = const Value.absent(),
            Value<int> endJuzNumber = const Value.absent(),
            Value<int> endPageNumber = const Value.absent(),
            Value<int> endSurahNumber = const Value.absent(),
            Value<int> endVerseNumber = const Value.absent(),
            Value<String?> reciter = const Value.absent(),
            Value<int> reciteVerseCount = const Value.absent(),
            Value<bool> isLoop = const Value.absent(),
            Value<bool> enableEnglishRecitation = const Value.absent(),
            Value<String?> bluetoothAddress = const Value.absent(),
            Value<TrackType?> type = const Value.absent(),
            Value<MushafCode?> designCode = const Value.absent(),
            Value<String?> lastHighlightedVerseKey = const Value.absent(),
            Value<LastRecitedWord?> lastRecitedWord = const Value.absent(),
            Value<String?> name = const Value.absent(),
            Value<TrackRangePortion> rangePortion = const Value.absent(),
            Value<int?> scheduleDayNumber = const Value.absent(),
            Value<String?> scheduleTime = const Value.absent(),
            Value<bool> enableRecitationChecker = const Value.absent(),
            Value<String> zoomState = const Value.absent(),
            Value<int?> reminderDayNumber = const Value.absent(),
            Value<String?> reminderTime = const Value.absent(),
            Value<int?> openingTimestamp = const Value.absent(),
            Value<bool> enableDownloadAudio = const Value.absent(),
            Value<int> audioSize = const Value.absent(),
            Value<bool> isAudioDownloaded = const Value.absent(),
          }) =>
              TrackEntityCompanion(
            id: id,
            startJuzNumber: startJuzNumber,
            startPageNumber: startPageNumber,
            startSurahNumber: startSurahNumber,
            startVerseNumber: startVerseNumber,
            endJuzNumber: endJuzNumber,
            endPageNumber: endPageNumber,
            endSurahNumber: endSurahNumber,
            endVerseNumber: endVerseNumber,
            reciter: reciter,
            reciteVerseCount: reciteVerseCount,
            isLoop: isLoop,
            enableEnglishRecitation: enableEnglishRecitation,
            bluetoothAddress: bluetoothAddress,
            type: type,
            designCode: designCode,
            lastHighlightedVerseKey: lastHighlightedVerseKey,
            lastRecitedWord: lastRecitedWord,
            name: name,
            rangePortion: rangePortion,
            scheduleDayNumber: scheduleDayNumber,
            scheduleTime: scheduleTime,
            enableRecitationChecker: enableRecitationChecker,
            zoomState: zoomState,
            reminderDayNumber: reminderDayNumber,
            reminderTime: reminderTime,
            openingTimestamp: openingTimestamp,
            enableDownloadAudio: enableDownloadAudio,
            audioSize: audioSize,
            isAudioDownloaded: isAudioDownloaded,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int startJuzNumber,
            required int startPageNumber,
            required int startSurahNumber,
            required int startVerseNumber,
            required int endJuzNumber,
            required int endPageNumber,
            required int endSurahNumber,
            required int endVerseNumber,
            Value<String?> reciter = const Value.absent(),
            required int reciteVerseCount,
            required bool isLoop,
            required bool enableEnglishRecitation,
            Value<String?> bluetoothAddress = const Value.absent(),
            Value<TrackType?> type = const Value.absent(),
            Value<MushafCode?> designCode = const Value.absent(),
            Value<String?> lastHighlightedVerseKey = const Value.absent(),
            Value<LastRecitedWord?> lastRecitedWord = const Value.absent(),
            Value<String?> name = const Value.absent(),
            required TrackRangePortion rangePortion,
            Value<int?> scheduleDayNumber = const Value.absent(),
            Value<String?> scheduleTime = const Value.absent(),
            required bool enableRecitationChecker,
            required String zoomState,
            Value<int?> reminderDayNumber = const Value.absent(),
            Value<String?> reminderTime = const Value.absent(),
            Value<int?> openingTimestamp = const Value.absent(),
            required bool enableDownloadAudio,
            required int audioSize,
            required bool isAudioDownloaded,
          }) =>
              TrackEntityCompanion.insert(
            id: id,
            startJuzNumber: startJuzNumber,
            startPageNumber: startPageNumber,
            startSurahNumber: startSurahNumber,
            startVerseNumber: startVerseNumber,
            endJuzNumber: endJuzNumber,
            endPageNumber: endPageNumber,
            endSurahNumber: endSurahNumber,
            endVerseNumber: endVerseNumber,
            reciter: reciter,
            reciteVerseCount: reciteVerseCount,
            isLoop: isLoop,
            enableEnglishRecitation: enableEnglishRecitation,
            bluetoothAddress: bluetoothAddress,
            type: type,
            designCode: designCode,
            lastHighlightedVerseKey: lastHighlightedVerseKey,
            lastRecitedWord: lastRecitedWord,
            name: name,
            rangePortion: rangePortion,
            scheduleDayNumber: scheduleDayNumber,
            scheduleTime: scheduleTime,
            enableRecitationChecker: enableRecitationChecker,
            zoomState: zoomState,
            reminderDayNumber: reminderDayNumber,
            reminderTime: reminderTime,
            openingTimestamp: openingTimestamp,
            enableDownloadAudio: enableDownloadAudio,
            audioSize: audioSize,
            isAudioDownloaded: isAudioDownloaded,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$TrackEntityTableProcessedTableManager = ProcessedTableManager<
    _$QuranDatabase,
    $TrackEntityTable,
    TrackEntityData,
    $$TrackEntityTableFilterComposer,
    $$TrackEntityTableOrderingComposer,
    $$TrackEntityTableAnnotationComposer,
    $$TrackEntityTableCreateCompanionBuilder,
    $$TrackEntityTableUpdateCompanionBuilder,
    (
      TrackEntityData,
      BaseReferences<_$QuranDatabase, $TrackEntityTable, TrackEntityData>
    ),
    TrackEntityData,
    PrefetchHooks Function()>;
typedef $$AlignmentWordResultEntityTableCreateCompanionBuilder
    = AlignmentWordResultEntityCompanion Function({
  required String id,
  required int trackId,
  required int verseId,
  required String verseKey,
  required int wordId,
  required int wordIndex,
  required int pageNumber,
  required String verseCodeV1,
  required String codeV1,
  required String type,
  required bool isCorrect,
  required int time,
  Value<int> rowid,
});
typedef $$AlignmentWordResultEntityTableUpdateCompanionBuilder
    = AlignmentWordResultEntityCompanion Function({
  Value<String> id,
  Value<int> trackId,
  Value<int> verseId,
  Value<String> verseKey,
  Value<int> wordId,
  Value<int> wordIndex,
  Value<int> pageNumber,
  Value<String> verseCodeV1,
  Value<String> codeV1,
  Value<String> type,
  Value<bool> isCorrect,
  Value<int> time,
  Value<int> rowid,
});

class $$AlignmentWordResultEntityTableFilterComposer
    extends Composer<_$QuranDatabase, $AlignmentWordResultEntityTable> {
  $$AlignmentWordResultEntityTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get trackId => $composableBuilder(
      column: $table.trackId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get verseId => $composableBuilder(
      column: $table.verseId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get verseKey => $composableBuilder(
      column: $table.verseKey, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get wordId => $composableBuilder(
      column: $table.wordId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get wordIndex => $composableBuilder(
      column: $table.wordIndex, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get verseCodeV1 => $composableBuilder(
      column: $table.verseCodeV1, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get codeV1 => $composableBuilder(
      column: $table.codeV1, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isCorrect => $composableBuilder(
      column: $table.isCorrect, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get time => $composableBuilder(
      column: $table.time, builder: (column) => ColumnFilters(column));
}

class $$AlignmentWordResultEntityTableOrderingComposer
    extends Composer<_$QuranDatabase, $AlignmentWordResultEntityTable> {
  $$AlignmentWordResultEntityTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get trackId => $composableBuilder(
      column: $table.trackId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get verseId => $composableBuilder(
      column: $table.verseId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get verseKey => $composableBuilder(
      column: $table.verseKey, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get wordId => $composableBuilder(
      column: $table.wordId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get wordIndex => $composableBuilder(
      column: $table.wordIndex, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get verseCodeV1 => $composableBuilder(
      column: $table.verseCodeV1, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get codeV1 => $composableBuilder(
      column: $table.codeV1, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isCorrect => $composableBuilder(
      column: $table.isCorrect, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get time => $composableBuilder(
      column: $table.time, builder: (column) => ColumnOrderings(column));
}

class $$AlignmentWordResultEntityTableAnnotationComposer
    extends Composer<_$QuranDatabase, $AlignmentWordResultEntityTable> {
  $$AlignmentWordResultEntityTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get trackId =>
      $composableBuilder(column: $table.trackId, builder: (column) => column);

  GeneratedColumn<int> get verseId =>
      $composableBuilder(column: $table.verseId, builder: (column) => column);

  GeneratedColumn<String> get verseKey =>
      $composableBuilder(column: $table.verseKey, builder: (column) => column);

  GeneratedColumn<int> get wordId =>
      $composableBuilder(column: $table.wordId, builder: (column) => column);

  GeneratedColumn<int> get wordIndex =>
      $composableBuilder(column: $table.wordIndex, builder: (column) => column);

  GeneratedColumn<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => column);

  GeneratedColumn<String> get verseCodeV1 => $composableBuilder(
      column: $table.verseCodeV1, builder: (column) => column);

  GeneratedColumn<String> get codeV1 =>
      $composableBuilder(column: $table.codeV1, builder: (column) => column);

  GeneratedColumn<String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<bool> get isCorrect =>
      $composableBuilder(column: $table.isCorrect, builder: (column) => column);

  GeneratedColumn<int> get time =>
      $composableBuilder(column: $table.time, builder: (column) => column);
}

class $$AlignmentWordResultEntityTableTableManager extends RootTableManager<
    _$QuranDatabase,
    $AlignmentWordResultEntityTable,
    AlignmentWordResultEntityData,
    $$AlignmentWordResultEntityTableFilterComposer,
    $$AlignmentWordResultEntityTableOrderingComposer,
    $$AlignmentWordResultEntityTableAnnotationComposer,
    $$AlignmentWordResultEntityTableCreateCompanionBuilder,
    $$AlignmentWordResultEntityTableUpdateCompanionBuilder,
    (
      AlignmentWordResultEntityData,
      BaseReferences<_$QuranDatabase, $AlignmentWordResultEntityTable,
          AlignmentWordResultEntityData>
    ),
    AlignmentWordResultEntityData,
    PrefetchHooks Function()> {
  $$AlignmentWordResultEntityTableTableManager(
      _$QuranDatabase db, $AlignmentWordResultEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AlignmentWordResultEntityTableFilterComposer(
                  $db: db, $table: table),
          createOrderingComposer: () =>
              $$AlignmentWordResultEntityTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AlignmentWordResultEntityTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<int> trackId = const Value.absent(),
            Value<int> verseId = const Value.absent(),
            Value<String> verseKey = const Value.absent(),
            Value<int> wordId = const Value.absent(),
            Value<int> wordIndex = const Value.absent(),
            Value<int> pageNumber = const Value.absent(),
            Value<String> verseCodeV1 = const Value.absent(),
            Value<String> codeV1 = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<bool> isCorrect = const Value.absent(),
            Value<int> time = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AlignmentWordResultEntityCompanion(
            id: id,
            trackId: trackId,
            verseId: verseId,
            verseKey: verseKey,
            wordId: wordId,
            wordIndex: wordIndex,
            pageNumber: pageNumber,
            verseCodeV1: verseCodeV1,
            codeV1: codeV1,
            type: type,
            isCorrect: isCorrect,
            time: time,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required int trackId,
            required int verseId,
            required String verseKey,
            required int wordId,
            required int wordIndex,
            required int pageNumber,
            required String verseCodeV1,
            required String codeV1,
            required String type,
            required bool isCorrect,
            required int time,
            Value<int> rowid = const Value.absent(),
          }) =>
              AlignmentWordResultEntityCompanion.insert(
            id: id,
            trackId: trackId,
            verseId: verseId,
            verseKey: verseKey,
            wordId: wordId,
            wordIndex: wordIndex,
            pageNumber: pageNumber,
            verseCodeV1: verseCodeV1,
            codeV1: codeV1,
            type: type,
            isCorrect: isCorrect,
            time: time,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AlignmentWordResultEntityTableProcessedTableManager
    = ProcessedTableManager<
        _$QuranDatabase,
        $AlignmentWordResultEntityTable,
        AlignmentWordResultEntityData,
        $$AlignmentWordResultEntityTableFilterComposer,
        $$AlignmentWordResultEntityTableOrderingComposer,
        $$AlignmentWordResultEntityTableAnnotationComposer,
        $$AlignmentWordResultEntityTableCreateCompanionBuilder,
        $$AlignmentWordResultEntityTableUpdateCompanionBuilder,
        (
          AlignmentWordResultEntityData,
          BaseReferences<_$QuranDatabase, $AlignmentWordResultEntityTable,
              AlignmentWordResultEntityData>
        ),
        AlignmentWordResultEntityData,
        PrefetchHooks Function()>;
typedef $$FileSizeCacheEntityTableCreateCompanionBuilder
    = FileSizeCacheEntityCompanion Function({
  required String fileName,
  required int size,
  required DateTime lastModified,
  Value<int> rowid,
});
typedef $$FileSizeCacheEntityTableUpdateCompanionBuilder
    = FileSizeCacheEntityCompanion Function({
  Value<String> fileName,
  Value<int> size,
  Value<DateTime> lastModified,
  Value<int> rowid,
});

class $$FileSizeCacheEntityTableFilterComposer
    extends Composer<_$QuranDatabase, $FileSizeCacheEntityTable> {
  $$FileSizeCacheEntityTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get fileName => $composableBuilder(
      column: $table.fileName, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get size => $composableBuilder(
      column: $table.size, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastModified => $composableBuilder(
      column: $table.lastModified, builder: (column) => ColumnFilters(column));
}

class $$FileSizeCacheEntityTableOrderingComposer
    extends Composer<_$QuranDatabase, $FileSizeCacheEntityTable> {
  $$FileSizeCacheEntityTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get fileName => $composableBuilder(
      column: $table.fileName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get size => $composableBuilder(
      column: $table.size, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastModified => $composableBuilder(
      column: $table.lastModified,
      builder: (column) => ColumnOrderings(column));
}

class $$FileSizeCacheEntityTableAnnotationComposer
    extends Composer<_$QuranDatabase, $FileSizeCacheEntityTable> {
  $$FileSizeCacheEntityTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get fileName =>
      $composableBuilder(column: $table.fileName, builder: (column) => column);

  GeneratedColumn<int> get size =>
      $composableBuilder(column: $table.size, builder: (column) => column);

  GeneratedColumn<DateTime> get lastModified => $composableBuilder(
      column: $table.lastModified, builder: (column) => column);
}

class $$FileSizeCacheEntityTableTableManager extends RootTableManager<
    _$QuranDatabase,
    $FileSizeCacheEntityTable,
    FileSizeCacheEntityData,
    $$FileSizeCacheEntityTableFilterComposer,
    $$FileSizeCacheEntityTableOrderingComposer,
    $$FileSizeCacheEntityTableAnnotationComposer,
    $$FileSizeCacheEntityTableCreateCompanionBuilder,
    $$FileSizeCacheEntityTableUpdateCompanionBuilder,
    (
      FileSizeCacheEntityData,
      BaseReferences<_$QuranDatabase, $FileSizeCacheEntityTable,
          FileSizeCacheEntityData>
    ),
    FileSizeCacheEntityData,
    PrefetchHooks Function()> {
  $$FileSizeCacheEntityTableTableManager(
      _$QuranDatabase db, $FileSizeCacheEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$FileSizeCacheEntityTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$FileSizeCacheEntityTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$FileSizeCacheEntityTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> fileName = const Value.absent(),
            Value<int> size = const Value.absent(),
            Value<DateTime> lastModified = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              FileSizeCacheEntityCompanion(
            fileName: fileName,
            size: size,
            lastModified: lastModified,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String fileName,
            required int size,
            required DateTime lastModified,
            Value<int> rowid = const Value.absent(),
          }) =>
              FileSizeCacheEntityCompanion.insert(
            fileName: fileName,
            size: size,
            lastModified: lastModified,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$FileSizeCacheEntityTableProcessedTableManager = ProcessedTableManager<
    _$QuranDatabase,
    $FileSizeCacheEntityTable,
    FileSizeCacheEntityData,
    $$FileSizeCacheEntityTableFilterComposer,
    $$FileSizeCacheEntityTableOrderingComposer,
    $$FileSizeCacheEntityTableAnnotationComposer,
    $$FileSizeCacheEntityTableCreateCompanionBuilder,
    $$FileSizeCacheEntityTableUpdateCompanionBuilder,
    (
      FileSizeCacheEntityData,
      BaseReferences<_$QuranDatabase, $FileSizeCacheEntityTable,
          FileSizeCacheEntityData>
    ),
    FileSizeCacheEntityData,
    PrefetchHooks Function()>;
typedef $$RecentPageEntityTableCreateCompanionBuilder
    = RecentPageEntityCompanion Function({
  required String id,
  required int pageNumber,
  required int time,
  Value<int> rowid,
});
typedef $$RecentPageEntityTableUpdateCompanionBuilder
    = RecentPageEntityCompanion Function({
  Value<String> id,
  Value<int> pageNumber,
  Value<int> time,
  Value<int> rowid,
});

class $$RecentPageEntityTableFilterComposer
    extends Composer<_$QuranDatabase, $RecentPageEntityTable> {
  $$RecentPageEntityTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get time => $composableBuilder(
      column: $table.time, builder: (column) => ColumnFilters(column));
}

class $$RecentPageEntityTableOrderingComposer
    extends Composer<_$QuranDatabase, $RecentPageEntityTable> {
  $$RecentPageEntityTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get time => $composableBuilder(
      column: $table.time, builder: (column) => ColumnOrderings(column));
}

class $$RecentPageEntityTableAnnotationComposer
    extends Composer<_$QuranDatabase, $RecentPageEntityTable> {
  $$RecentPageEntityTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => column);

  GeneratedColumn<int> get time =>
      $composableBuilder(column: $table.time, builder: (column) => column);
}

class $$RecentPageEntityTableTableManager extends RootTableManager<
    _$QuranDatabase,
    $RecentPageEntityTable,
    RecentPageEntityData,
    $$RecentPageEntityTableFilterComposer,
    $$RecentPageEntityTableOrderingComposer,
    $$RecentPageEntityTableAnnotationComposer,
    $$RecentPageEntityTableCreateCompanionBuilder,
    $$RecentPageEntityTableUpdateCompanionBuilder,
    (
      RecentPageEntityData,
      BaseReferences<_$QuranDatabase, $RecentPageEntityTable,
          RecentPageEntityData>
    ),
    RecentPageEntityData,
    PrefetchHooks Function()> {
  $$RecentPageEntityTableTableManager(
      _$QuranDatabase db, $RecentPageEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$RecentPageEntityTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$RecentPageEntityTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$RecentPageEntityTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<int> pageNumber = const Value.absent(),
            Value<int> time = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              RecentPageEntityCompanion(
            id: id,
            pageNumber: pageNumber,
            time: time,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required int pageNumber,
            required int time,
            Value<int> rowid = const Value.absent(),
          }) =>
              RecentPageEntityCompanion.insert(
            id: id,
            pageNumber: pageNumber,
            time: time,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$RecentPageEntityTableProcessedTableManager = ProcessedTableManager<
    _$QuranDatabase,
    $RecentPageEntityTable,
    RecentPageEntityData,
    $$RecentPageEntityTableFilterComposer,
    $$RecentPageEntityTableOrderingComposer,
    $$RecentPageEntityTableAnnotationComposer,
    $$RecentPageEntityTableCreateCompanionBuilder,
    $$RecentPageEntityTableUpdateCompanionBuilder,
    (
      RecentPageEntityData,
      BaseReferences<_$QuranDatabase, $RecentPageEntityTable,
          RecentPageEntityData>
    ),
    RecentPageEntityData,
    PrefetchHooks Function()>;
typedef $$BookmarkEntityTableCreateCompanionBuilder = BookmarkEntityCompanion
    Function({
  required String verseKey,
  required int pageNumber,
  required int time,
  Value<int> rowid,
});
typedef $$BookmarkEntityTableUpdateCompanionBuilder = BookmarkEntityCompanion
    Function({
  Value<String> verseKey,
  Value<int> pageNumber,
  Value<int> time,
  Value<int> rowid,
});

class $$BookmarkEntityTableFilterComposer
    extends Composer<_$QuranDatabase, $BookmarkEntityTable> {
  $$BookmarkEntityTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get verseKey => $composableBuilder(
      column: $table.verseKey, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get time => $composableBuilder(
      column: $table.time, builder: (column) => ColumnFilters(column));
}

class $$BookmarkEntityTableOrderingComposer
    extends Composer<_$QuranDatabase, $BookmarkEntityTable> {
  $$BookmarkEntityTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get verseKey => $composableBuilder(
      column: $table.verseKey, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get time => $composableBuilder(
      column: $table.time, builder: (column) => ColumnOrderings(column));
}

class $$BookmarkEntityTableAnnotationComposer
    extends Composer<_$QuranDatabase, $BookmarkEntityTable> {
  $$BookmarkEntityTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get verseKey =>
      $composableBuilder(column: $table.verseKey, builder: (column) => column);

  GeneratedColumn<int> get pageNumber => $composableBuilder(
      column: $table.pageNumber, builder: (column) => column);

  GeneratedColumn<int> get time =>
      $composableBuilder(column: $table.time, builder: (column) => column);
}

class $$BookmarkEntityTableTableManager extends RootTableManager<
    _$QuranDatabase,
    $BookmarkEntityTable,
    BookmarkEntityData,
    $$BookmarkEntityTableFilterComposer,
    $$BookmarkEntityTableOrderingComposer,
    $$BookmarkEntityTableAnnotationComposer,
    $$BookmarkEntityTableCreateCompanionBuilder,
    $$BookmarkEntityTableUpdateCompanionBuilder,
    (
      BookmarkEntityData,
      BaseReferences<_$QuranDatabase, $BookmarkEntityTable, BookmarkEntityData>
    ),
    BookmarkEntityData,
    PrefetchHooks Function()> {
  $$BookmarkEntityTableTableManager(
      _$QuranDatabase db, $BookmarkEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$BookmarkEntityTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$BookmarkEntityTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$BookmarkEntityTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> verseKey = const Value.absent(),
            Value<int> pageNumber = const Value.absent(),
            Value<int> time = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              BookmarkEntityCompanion(
            verseKey: verseKey,
            pageNumber: pageNumber,
            time: time,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String verseKey,
            required int pageNumber,
            required int time,
            Value<int> rowid = const Value.absent(),
          }) =>
              BookmarkEntityCompanion.insert(
            verseKey: verseKey,
            pageNumber: pageNumber,
            time: time,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$BookmarkEntityTableProcessedTableManager = ProcessedTableManager<
    _$QuranDatabase,
    $BookmarkEntityTable,
    BookmarkEntityData,
    $$BookmarkEntityTableFilterComposer,
    $$BookmarkEntityTableOrderingComposer,
    $$BookmarkEntityTableAnnotationComposer,
    $$BookmarkEntityTableCreateCompanionBuilder,
    $$BookmarkEntityTableUpdateCompanionBuilder,
    (
      BookmarkEntityData,
      BaseReferences<_$QuranDatabase, $BookmarkEntityTable, BookmarkEntityData>
    ),
    BookmarkEntityData,
    PrefetchHooks Function()>;

class $QuranDatabaseManager {
  final _$QuranDatabase _db;
  $QuranDatabaseManager(this._db);
  $$TrackEntityTableTableManager get trackEntity =>
      $$TrackEntityTableTableManager(_db, _db.trackEntity);
  $$AlignmentWordResultEntityTableTableManager get alignmentWordResultEntity =>
      $$AlignmentWordResultEntityTableTableManager(
          _db, _db.alignmentWordResultEntity);
  $$FileSizeCacheEntityTableTableManager get fileSizeCacheEntity =>
      $$FileSizeCacheEntityTableTableManager(_db, _db.fileSizeCacheEntity);
  $$RecentPageEntityTableTableManager get recentPageEntity =>
      $$RecentPageEntityTableTableManager(_db, _db.recentPageEntity);
  $$BookmarkEntityTableTableManager get bookmarkEntity =>
      $$BookmarkEntityTableTableManager(_db, _db.bookmarkEntity);
}
