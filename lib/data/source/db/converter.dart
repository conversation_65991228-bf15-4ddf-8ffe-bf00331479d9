import 'package:drift/drift.dart';
import 'package:mushafi/presentation/model/recitation/last_recited_word.dart';

class Converter {
  Converter._();

  static JsonTypeConverter<LastRecitedWord, String> lastRecitedWordConverter =
      TypeConverter.json(
    fromJson: (json) => (json != null)
        ? LastRecitedWord.fromJson(json as Map<String, Object?>)
        : const LastRecitedWord(),
    toJson: (pref) => pref.toJson(),
  );
}
