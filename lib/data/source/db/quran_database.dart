import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:mushafi/data/source/db/converter.dart';
import 'package:mushafi/data/source/db/model/alignment_word_result_entity.dart';
import 'package:mushafi/data/source/db/model/bookmark_entity.dart';
import 'package:mushafi/data/source/db/model/file_size_cache_entity.dart';
import 'package:mushafi/data/source/db/model/recent_page_entity.dart';
import 'package:mushafi/data/source/db/model/track_entity.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/quran/mushaf_code.dart';
import 'package:mushafi/presentation/model/recitation/last_recited_word.dart';
import 'package:mushafi/presentation/model/track/track_range_portion.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/utils/file_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:sqlite3/sqlite3.dart';
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';

part 'quran_database.g.dart';

@DriftDatabase(tables: [
  TrackEntity,
  AlignmentWordResultEntity,
  FileSizeCacheEntity,
  RecentPageEntity,
  BookmarkEntity,
])
class QuranDatabase extends _$QuranDatabase {
  QuranDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 27;

  static LazyDatabase _openConnection() {
    // the LazyDatabase util lets us find the right location for the file async.
    return LazyDatabase(() async {
      final dbDirectory = await getApplicationDocumentsDirectory();
      final file = File(p.join(dbDirectory.path, 'quran.db'));

      // Also work around limitations on old Android versions
      if (Platform.isAndroid) {
        await applyWorkaroundToOpenSqlite3OnOldAndroidVersions();
      }

      // Make sqlite3 pick a more suitable location for temporary files - the
      // one from the system may be inaccessible due to sandboxing.
      final cachebase = (await getTemporaryDirectory()).path;
      // We can't access /tmp on Android, which sqlite3 would try by default.
      // Explicitly tell it about the correct temporary directory.
      sqlite3.tempDirectory = cachebase;

      return NativeDatabase.createInBackground(file);
    });
  }

  static Future<void> applyExistingDatabase() async {
    final packageInfo = await PackageInfo.fromPlatform();
    final packageName = packageInfo.packageName;

    final nativeAndroidDatabasePath = '/data/data/$packageName/databases/';
    final nativeAndroidDbFile = File('${nativeAndroidDatabasePath}quran.db');
    final nativeAndroidShmFile =
        File('${nativeAndroidDatabasePath}quran.db-shm');
    final nativeAndroidWalFile =
        File('${nativeAndroidDatabasePath}quran.db-wal');

    final dbExist = await nativeAndroidDbFile.exists();
    final shmExist = await nativeAndroidShmFile.exists();
    final walExist = await nativeAndroidWalFile.exists();

    final databaseDirectory = await getApplicationDocumentsDirectory();

    if (dbExist) {
      final dbFile = File(p.join(databaseDirectory.path, 'quran.db'));
      await FileUtils.moveFile(nativeAndroidDbFile, dbFile.path);
    }

    if (shmExist) {
      final shmFile = File(p.join(databaseDirectory.path, 'quran.db-shm'));
      await FileUtils.moveFile(nativeAndroidShmFile, shmFile.path);
    }

    if (walExist) {
      final walFile = File(p.join(databaseDirectory.path, 'quran.db-wal'));
      await FileUtils.moveFile(nativeAndroidWalFile, walFile.path);
    }
  }

  @override
  MigrationStrategy get migration => MigrationStrategy(
        onUpgrade: (m, from, to) async {
          if (from == 23) {
            await m.database.customStatement('''
              UPDATE tracks
              SET type = CASE
                WHEN type = 'LISTENING' THEN 'listening'
                WHEN type = 'READING_HABITUAL' THEN 'readingHabitual'
                WHEN type = 'READING_COVER_TO_COVER' THEN 'readingCoverToCover'
                WHEN type = 'READING_WITH_AI' THEN 'readingWithAi'
                WHEN type = 'MEMORIZING' THEN 'memorizing'
                ELSE type
              END
            ''');

            await m.database.customStatement('''
              UPDATE tracks
              SET reciter = CASE
                WHEN reciter = 'MISHARY' THEN 'afasy'
                ELSE LOWER(reciter)
              END
            ''');

            await m.database.customStatement('''
              UPDATE tracks
              SET designCode = CASE
                WHEN designCode = 'DEFAULT' THEN 'defaultCode'
                WHEN designCode = 'MEDINA' THEN 'medina'
                WHEN designCode = 'OLD_MEDINA' THEN 'oldMedina'
                WHEN designCode = 'QALOON' THEN 'qaloon'
                WHEN designCode = 'MEDIUM_MEDINA' THEN 'mediumMedina'
                WHEN designCode = 'SHUBAH' THEN 'shubah'
                WHEN designCode = 'WARSH' THEN 'warsh'
                ELSE designCode
              END
            ''');

            await m.database.customStatement('''
              UPDATE tracks
              SET rangePortion = CASE
                WHEN rangePortion = 'SURAH' THEN 'surah'
                WHEN rangePortion = 'JUZ' THEN 'juz'
                WHEN rangePortion = 'RANGE' THEN 'range'
                WHEN rangePortion = 'PAGE' THEN 'page'
                ELSE rangePortion
              END
            ''');

            try {
              await m.database.customStatement('''
                ALTER TABLE memorization_mistakes
                RENAME TO alignment_word_results
            ''');
            } catch (e, st) {
              // possibly memorization_mistakes has been renamed to alignment_word_results
              talker.handle(e, st);
              FirebaseCrashlytics.instance.recordError(e, st);
            }

            try {
              await m.database.customStatement('''
                  ALTER TABLE alignment_word_results
                  RENAME COLUMN wordCodeV1 TO codeV1
                ''');
            } catch (e, st) {
              // possibly wordCodeV1 has been renamed to codeV1
              talker.handle(e, st);
              FirebaseCrashlytics.instance.recordError(e, st);
            }

            try {
              await m.database.customStatement('''
                ALTER TABLE alignment_word_results
                ADD COLUMN verseId INTEGER DEFAULT 0 NOT NULL
              ''');
            } catch (e, st) {
              // possibly verseId already exists
              talker.handle(e, st);
              FirebaseCrashlytics.instance.recordError(e, st);
            }

            try {
              await m.database.customStatement('''
                ALTER TABLE alignment_word_results
                ADD COLUMN wordId INTEGER DEFAULT 0 NOT NULL
              ''');
            } catch (e, st) {
              // possibly wordId already exists
              talker.handle(e, st);
              FirebaseCrashlytics.instance.recordError(e, st);
            }

            try {
              await m.database.customStatement('''
                INSERT INTO alignment_word_results (id, trackId, verseId, verseKey, wordId, wordIndex, pageNumber, verseCodeV1, codeV1, isCorrect, time)
                SELECT hex(randomblob(8)), trackId, 0, verseKey, 0, wordIndex, pageNumber, verseCodeV1, wordCodeV1, 0, time
                FROM recitation_mistakes
              ''');
            } catch (e, st) {
              // possibly recitation_mistakes table does not exist, so skip it
              talker.handle(e, st);
              FirebaseCrashlytics.instance.recordError(e, st);
            }

            try {
              await m.database.customStatement('''
                ALTER TABLE alignment_word_results
                ADD COLUMN type TEXT DEFAULT 'memorizing' NOT NULL
              ''');
            } catch (e, st) {
              // possibly type already exists
              talker.handle(e, st);
              FirebaseCrashlytics.instance.recordError(e, st);
            }

            try {
              await m.database.customStatement('''
                UPDATE alignment_word_results
                SET type = COALESCE(
                  (
                    SELECT tracks.type
                    FROM tracks
                    WHERE tracks.id = alignment_word_results.trackId
                  ),
                  'memorizing'
                )
              ''');
            } catch (e, st) {
              talker.handle(e, st);
              FirebaseCrashlytics.instance.recordError(e, st);
            }

            await m.database.customStatement('''
              CREATE TABLE IF NOT EXISTS fileSizeCaches (
                fileName TEXT PRIMARY KEY NOT NULL,
                size INTEGER NOT NULL,
                lastModified INTEGER NOT NULL
              )
            ''');
          }
          if (from == 24) {
            await m.database.customStatement('''
              CREATE TABLE IF NOT EXISTS recent_pages (
                id TEXT PRIMARY KEY NOT NULL,
                pageNumber INTEGER NOT NULL,
                time INTEGER NOT NULL
              )
            ''');
          }
          if (from == 25) {
            await m.database.customStatement('''
              DELETE FROM recent_pages
            ''');
          }
          if (from == 26) {
            await m.database.customStatement('''
              CREATE TABLE IF NOT EXISTS bookmarks (
                verseKey TEXT PRIMARY KEY NOT NULL,
                pageNumber INTEGER NOT NULL,
                time INTEGER NOT NULL
              )
            ''');
          }
        },
      );
}
