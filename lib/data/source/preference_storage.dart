import 'package:collection/collection.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mushafi/presentation/model/ai/vad_model_type.dart';
import 'package:mushafi/presentation/model/quran/juz_name_script.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PreferenceStorage {
  PreferenceStorage._();

  static final _storage = GetStorage();

  /// Migrates data from SharedPreferences to GetStorage
  static Future<void> migrateToGetStorage() async {
    final prefs = await SharedPreferences.getInstance();

    // Migrate favorite reciters
    final favoriteReciters = prefs.getStringList(favoriteRecitersKey);
    if (favoriteReciters != null) {
      _storage.write(favoriteRecitersKey, favoriteReciters);
      await prefs.remove(favoriteRecitersKey);
    }

    // Migrate default reciter
    final defaultReciter = prefs.getString(defaultReciterKey);
    if (defaultReciter != null) {
      _storage.write(defaultReciterKey, defaultReciter);
      await prefs.remove(defaultReciterKey);
    }

    // Migrate default translation
    final defaultTranslation = prefs.getString(defaultTranslationKey);
    if (defaultTranslation != null) {
      _storage.write(defaultTranslationKey, defaultTranslation);
      await prefs.remove(defaultTranslationKey);
    }

    // Migrate juz name script
    final juzScript = prefs.getString(juzNameScriptKey);
    if (juzScript != null) {
      _storage.write(juzNameScriptKey, juzScript);
      await prefs.remove(juzNameScriptKey);
    }
  }

  // Update existing methods to use GetStorage
  static Future<void> saveFavoriteReciterKeys(List<String> keys) async {
    await _storage.write(favoriteRecitersKey, keys);
  }

  static List<String> getFavoriteReciterKeys() {
    return (_storage.read<List>(favoriteRecitersKey) ?? []).cast<String>();
  }

  static Future<void> saveDefaultReciterKey(String key) async {
    await _storage.write(defaultReciterKey, key);
  }

  static String getDefaultReciterKey() {
    return _storage.read<String>(defaultReciterKey) ?? "husary";
  }

  static Future<void> saveDefaultTranslationKey(String key) async {
    await _storage.write(defaultTranslationKey, key);
  }

  static String getDefaultTranslationKey() {
    return _storage.read<String>(defaultTranslationKey) ??
        "en_sahih_international";
  }

  static Future<void> saveJuzNameScript(JuzNameScript script) async {
    await _storage.write(juzNameScriptKey, script.name);
  }

  static JuzNameScript getJuzNameScript() {
    final scriptName = _storage.read<String>(juzNameScriptKey);
    return JuzNameScript.values
            .firstWhereOrNull((element) => element.name == scriptName) ??
        JuzNameScript.arabic;
  }

  static Future<void> saveSelectedModel(VadModelType type) {
    return _storage.write(selectedModelKey, type.name);
  }

  static VadModelType getSelectedModel() {
    return VadModelType.stride4Q;

    final modelName = _storage.read<String>(selectedModelKey);
    return VadModelType.values
            .firstWhereOrNull((element) => element.name == modelName) ??
        VadModelType.stride2Q;
  }

  static Future<void> saveZoomState(ZoomState zoomState) {
    return _storage.write(zoomStateKey, zoomState.name);
  }

  static ZoomState getZoomState() {
    final zoomStateName = _storage.read<String>(zoomStateKey);
    return ZoomState.values
            .firstWhereOrNull((element) => element.name == zoomStateName) ??
        ZoomState.defaultZoom;
  }

  static Future<void> saveShowIncompatibleCard(bool show) {
    return _storage.write(showIncompatibleCardKey, show);
  }

  static bool getShowIncompatibleCard() {
    return _storage.read<bool>(showIncompatibleCardKey) ?? false;
  }

  static Future<void> saveCurrentModelVersionNumber(int versionNumber) {
    return _storage.write(currentModelVersionNumberKey, versionNumber);
  }

  static int getCurrentModelVersionNumber() {
    return _storage.read<int>(currentModelVersionNumberKey) ?? 1;
  }

  static Future<void> saveModelCompatible(bool isCompatible) {
    return _storage.write(modelCompatibleKey, isCompatible);
  }

  static bool getModelCompatible() {
    return _storage.read<bool>(modelCompatibleKey) ?? true;
  }

  static Future<void> saveLatestTestedModelVersion(String? version) {
    return _storage.write(latestTestedModelVersionKey, version);
  }

  static String? getLatestTestedModelVersion() {
    return _storage.read<String>(latestTestedModelVersionKey);
  }

  static Future<void> saveAssetPackRemoved(bool removed) {
    return _storage.write(assetPackRemovedKey, removed);
  }

  static bool getAssetPackRemoved() {
    return _storage.read<bool>(assetPackRemovedKey) ?? false;
  }

  static const String emailKey = 'email';
  static Future<void> saveEmail(String email) async {
    await _storage.write(emailKey, email);
  }

  static String? getEmail() {
    return _storage.read<String>(emailKey);
  }

  static const String customerIdKey = 'customer_id';
  static Future<void> saveCustomerId(String customerId) async {
    await _storage.write(customerIdKey, customerId);
  }

  static String? getCustomerId() {
    return _storage.read<String>(customerIdKey);
  }

  static const String currencyKey = 'user_currency';

  /// Saves the user's preferred currency code (e.g., 'USD', 'AED', 'SAR')
  ///
  /// This method stores the selected currency to persist user preference
  /// across app sessions. The currency should be a valid 3-letter ISO code.
  ///
  /// [currency] - The 3-letter currency code (e.g., 'USD', 'AED', 'SAR')
  static Future<void> saveCurrency(String currency) async {
    await _storage.write(currencyKey, currency);
  }

  /// Retrieves the user's saved currency preference
  ///
  /// Returns the previously saved currency code, or null if no
  /// currency preference has been saved yet.
  ///
  /// Returns: String? - The 3-letter currency code, or null if not set
  static String? getCurrency() {
    return _storage.read<String>(currencyKey);
  }
}
