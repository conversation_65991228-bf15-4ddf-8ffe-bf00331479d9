import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';

class JsonDataManager {
  List<Surah> _surahList = [];

  Future<List<Surah>> getSurahs() async {
    if (_surahList.isNotEmpty) {
      return _surahList;
    }

    // lazy load
    final jsonString = await rootBundle.loadString(Assets.jsonChapters);
    final jsonResponse = jsonDecode(jsonString);
    _surahList = jsonResponse
        .map<Surah>((jsonSurah) => Surah.fromJson(jsonSurah))
        .toList();

    return _surahList;
  }
}
