import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dnsolve/dnsolve.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:minio/minio.dart';
import 'package:mushafi/credentials.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/ai/asr_model_type.dart';
import 'package:mushafi/presentation/model/ai/cond_model_type.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/presentation/model/ai/vad_model_type.dart';
import 'package:mushafi/utils/file_utils.dart';
import 'package:path_provider/path_provider.dart';

class MinioDataManager {
  static Minio minio = Minio(
    endPoint: endPoint,
    accessKey: minioAccessKey,
    secretKey: minioSecretKey,
  );

  static String endPoint = 's3.mushafi.app';

  static final bucketName = 'appdata';
  static final mushafsPath = 'mushafs';
  static final audioSegmentsPath = 'audio_segments';
  static final translationsTafsirsPath = 'translations_tafsirs';
  static final modelsPath = 'models';
  static final quranCorpusPath = 'quran_corpus';
  static final modelCompatPath = 'model_compat';

  final String stride2QModel = 'hb_text_ft_fan2_stride2_ts_q.onnx';
  final String condQModel = 'hb_text_ft_fan2_stride_ts_text_enc_q.onnx';
  final String vadModel = 'vad_model.onnx';

  static Future<void> resolveDNS() async {
    // 4-Tier DNS Resolution Strategy:
    // Tier 1: Check connectivity (skip if offline)
    // Tier 2: System DNS (fastest, most reliable)
    // Tier 3: Custom DNS servers (Google/Cloudflare fallback)
    // Tier 4: Hardcoded IP (absolute last resort)

    // Tier 1: Check internet connectivity first
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult.contains(ConnectivityResult.none)) {
      return;
    }

    // Tier 2: Try system DNS resolution first
    try {
      final result = await InternetAddress.lookup('s3.mushafi.app');
      if (result.isNotEmpty) {
        endPoint = 's3.mushafi.app';
        minio = Minio(
          endPoint: endPoint,
          accessKey: minioAccessKey,
          secretKey: minioSecretKey,
        );
        return;
      }
    } catch (e, st) {
      talker.handle(e, st);
      FirebaseCrashlytics.instance.recordError(e, st);
    }

    // Tier 3: Fallback to custom DNS servers only as last resort
    final dnsolve = DNSolve();
    final providers = DNSProvider.values;
    String? resolvedAddress;

    for (final provider in providers) {
      try {
        final response = await dnsolve.lookup(
          's3.mushafi.app',
          dnsSec: true,
          type: RecordType.A,
          provider: provider,
        );

        resolvedAddress = response.answer?.records?.firstOrNull?.data;
        if (resolvedAddress != null) break;
      } catch (e, st) {
        talker.handle(e, st);
        FirebaseCrashlytics.instance.recordError(e, st);
      }
    }

    // Tier 4: Hardcoded IP address as absolute last resort
    if (resolvedAddress != null) {
      endPoint = resolvedAddress;
    } else {
      endPoint = '************';
    }

    minio = Minio(
      endPoint: endPoint,
      accessKey: minioAccessKey,
      secretKey: minioSecretKey,
    );
  }

  Future<File?> getObject(
    String parentPath,
    String fileName, {
    int? cachedFileSize,
    Function(int size)? cacheAction,
    Function(double percentage)? onDownloading,
    bool fetch = false,
  }) async {
    final dataDirectory = await getApplicationDocumentsDirectory();

    final object = '$parentPath/$fileName';
    final path = '${dataDirectory.path}/$object';
    final file = File(path);

    bool fileExists = await file.exists();
    int fileSize = 0;
    if (fileExists) {
      fileSize = await file.length();
    }
    fileExists = fileSize > 0;

    if (fetch || !fileExists || fileSize != cachedFileSize) {
      try {
        await FileUtils.createDocumentDirectoryIfNotExists(parentPath);

        final stream = await minio.getObject(bucketName, object);
        final contentLength = stream.contentLength ?? 0;
        if (contentLength > 0) {
          cacheAction?.call(contentLength);
        }

        final sink = file.openWrite();
        int downloaded = 0;
        final downloadCompleter = Completer();

        stream.timeout(
          const Duration(seconds: 5),
          onTimeout: (sink) {
            sink.addError(TimeoutException(
                'Download timeout: No data received for 5 seconds'));
            sink.close();
          },
        ).listen((chunk) {
          sink.add(chunk);
          downloaded += chunk.length;

          if (contentLength > 0) {
            final percentage = downloaded.toDouble() / contentLength;
            onDownloading?.call(percentage);
          }
        })
          ..onError((e, st) async {
            FirebaseCrashlytics.instance.recordError(e, st);
            talker.handle(e, st);
            await sink.close();
            if (!downloadCompleter.isCompleted) {
              downloadCompleter.complete();
            }
          })
          ..onDone(() async {
            await sink.close();
            if (!downloadCompleter.isCompleted) {
              downloadCompleter.complete();
            }
          });

        await downloadCompleter.future;
      } catch (e, st) {
        talker.handle(e, st);
        FirebaseCrashlytics.instance.recordError(e, st);
      }
    }

    fileExists = await file.exists() && await file.length() > 0;

    return (fileExists) ? file : null;
  }

  // null means failed to update
  Future<File?> updateObject(
    String parentPath,
    String fileName,
  ) async {
    final dataDirectory = await getApplicationDocumentsDirectory();

    final object = '$parentPath/$fileName';
    final path = '${dataDirectory.path}/$object';

    final tempObject = '$parentPath/${fileName}_temp';
    final tempPath = '${dataDirectory.path}/$tempObject';
    final tempFile = File(tempPath);

    int contentLength = 0;

    try {
      await FileUtils.createDocumentDirectoryIfNotExists(parentPath);

      final stream = await minio.getObject(bucketName, object);
      contentLength = stream.contentLength ?? 0;

      final sink = tempFile.openWrite();
      final downloadCompleter = Completer();

      stream.timeout(
        const Duration(seconds: 5),
        onTimeout: (sink) {
          sink.addError(TimeoutException(
              'Download timeout: No data received for 5 seconds'));
          sink.close();
        },
      ).listen((chunk) {
        sink.add(chunk);
      })
        ..onError((e, st) async {
          FirebaseCrashlytics.instance.recordError(e, st);
          talker.handle(e, st);
          await sink.close();
          if (!downloadCompleter.isCompleted) {
            downloadCompleter.complete();
          }
        })
        ..onDone(() async {
          await sink.close();
          if (!downloadCompleter.isCompleted) {
            downloadCompleter.complete();
          }
        });

      await downloadCompleter.future;
    } catch (e, st) {
      talker.handle(e, st);
      FirebaseCrashlytics.instance.recordError(e, st);
    }

    bool tempFileExists = await tempFile.exists();
    int tempFileSize = 0;
    if (tempFileExists) {
      tempFileSize = await tempFile.length();
    }
    tempFileExists = tempFileSize > 0;

    if (!tempFileExists || tempFileSize != contentLength) {
      return null;
    }

    await tempFile.copy(path);

    if (await tempFile.exists()) {
      await tempFile.delete();
    }

    return File(path);
  }

  Future<void> updateAllIndexes() async {
    updateIndex(audioSegmentsPath);
    updateIndex(translationsTafsirsPath);
  }

  Future<File?> updateIndex(String parentPath) async {
    return updateObject(parentPath, 'index.csv');
  }

  Future<File?> getMushaf(
    String fileName, {
    int? cachedFileSize,
    required Function(int size) cacheAction,
    bool fetch = false,
  }) async {
    return getObject(
      mushafsPath,
      fileName,
      cachedFileSize: cachedFileSize,
      cacheAction: cacheAction,
      fetch: fetch,
    );
  }

  Future<File?> getTranslationTafsir(
    String fileName, {
    int? cachedFileSize,
    required Function(int size) cacheAction,
    Function(double percentage)? onDownloading,
    bool fetch = false,
  }) async {
    return getObject(
      translationsTafsirsPath,
      fileName,
      cachedFileSize: cachedFileSize,
      cacheAction: cacheAction,
      onDownloading: onDownloading,
      fetch: fetch,
    );
  }

  Future<File?> getAudioSegment(
    String fileName, {
    int? cachedFileSize,
    required Function(int size) cacheAction,
    bool fetch = false,
  }) async {
    return getObject(
      audioSegmentsPath,
      fileName,
      cachedFileSize: cachedFileSize,
      cacheAction: cacheAction,
      fetch: fetch,
    );
  }

  Future<File?> getAudioSegmentIndex({
    int? cachedFileSize,
    required Function(int size) cacheAction,
    bool fetch = false,
  }) async {
    return getObject(
      audioSegmentsPath,
      'index.csv',
      cachedFileSize: cachedFileSize,
      cacheAction: cacheAction,
      fetch: fetch,
    );
  }

  Future<File?> getTranslationTafsirIndex({
    int? cachedFileSize,
    required Function(int size) cacheAction,
    bool fetch = false,
  }) async {
    return getObject(
      translationsTafsirsPath,
      'index.csv',
      cachedFileSize: cachedFileSize,
      cacheAction: cacheAction,
      fetch: fetch,
    );
  }

  Future<List<File>> getCachedAudioSegments() async {
    final dataDirectory = await getApplicationDocumentsDirectory();
    final directory = Directory('${dataDirectory.path}/$audioSegmentsPath');
    if (!await directory.exists()) {
      return [];
    }

    final files = await directory.list().toList();
    return files.map((file) => File(file.path)).toList();
  }

  Future<List<File>> getCachedTranslationTafsirs() async {
    final dataDirectory = await getApplicationDocumentsDirectory();
    final directory =
        Directory('${dataDirectory.path}/$translationsTafsirsPath');
    if (!await directory.exists()) {
      return [];
    }

    final files = await directory.list().toList();
    return files.map((file) => File(file.path)).toList();
  }

  Future<List<File>> getCachedAsrModels() async {
    final allAsrModelNames =
        AsrModelType.values.map((value) => value.name).toList();

    final dataDirectory = await getApplicationDocumentsDirectory();
    final directory = Directory('${dataDirectory.path}/$modelsPath');
    if (!await directory.exists()) {
      return [];
    }

    List<FileSystemEntity> files = await directory.list().toList();

    files = files
        .where(
            (entity) => allAsrModelNames.contains(entity.path.split('/').last))
        .toList();

    return files.map((file) => File(file.path)).toList();
  }

  Future<List<File>> getCachedCondModels() async {
    final allCondModelNames =
        CondModelType.values.map((value) => value.name).toList();

    final dataDirectory = await getApplicationDocumentsDirectory();
    final directory = Directory('${dataDirectory.path}/$modelsPath');
    if (!await directory.exists()) {
      return [];
    }

    List<FileSystemEntity> files = await directory.list().toList();

    files = files
        .where(
            (entity) => allCondModelNames.contains(entity.path.split('/').last))
        .toList();

    return files.map((file) => File(file.path)).toList();
  }

  Future<List<File>> getCachedVadModels() async {
    final allVadModelNames =
        VadModelType.values.map((value) => value.name).toList();

    final dataDirectory = await getApplicationDocumentsDirectory();
    final directory = Directory('${dataDirectory.path}/$modelsPath');
    if (!await directory.exists()) {
      return [];
    }

    List<FileSystemEntity> files = await directory.list().toList();

    files = files
        .where(
            (entity) => allVadModelNames.contains(entity.path.split('/').last))
        .toList();

    return files.map((file) => File(file.path)).toList();
  }

  Future<void> fetchInferenceModel({
    required InferenceModel model,
    int? cachedSize,
    int? cachedCondSize,
    int? cachedVadSize,
    int? cachedCorpusSize,
    Function(String name, double percentage)? onDownloading,
    required Function(String name, int size) cacheAction,
  }) async {
    await getObject(
      '$modelsPath/${model.version}',
      model.name,
      cachedFileSize: cachedSize,
      onDownloading: (percentage) {
        onDownloading?.call(model.name, percentage);
      },
      cacheAction: (size) {
        cacheAction(model.name, size);
      },
    );
    await getObject(
      '$modelsPath/${model.version}',
      model.condName,
      cachedFileSize: cachedCondSize,
      onDownloading: (percentage) {
        onDownloading?.call(model.condName, percentage);
      },
      cacheAction: (size) {
        cacheAction(model.condName, size);
      },
    );
    await getObject(
      '$modelsPath/${model.version}',
      model.vadName,
      cachedFileSize: cachedVadSize,
      onDownloading: (percentage) {
        onDownloading?.call(model.vadName, percentage);
      },
      cacheAction: (size) {
        cacheAction(model.vadName, size);
      },
    );
    await getObject(
      quranCorpusPath,
      model.encodedVersesName,
      cachedFileSize: cachedCorpusSize,
      onDownloading: (percentage) {
        onDownloading?.call(model.encodedVersesName, percentage);
      },
      cacheAction: (size) {
        cacheAction(model.encodedVersesName, size);
      },
    );
  }

  Future<File?> getModelCompatIndex() =>
      getObject(modelCompatPath, 'index.yaml');

  Future<File?> updateModelCompatIndex() {
    return updateObject(modelCompatPath, 'index.yaml');
  }
}
