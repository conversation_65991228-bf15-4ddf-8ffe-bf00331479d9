import 'dart:async';
import 'dart:io';

import 'package:asset_delivery/asset_delivery.dart';

class AssetPackManager {
  static final mushafPackName = 'mushaf_asset';
  static final mushafFileName = 'quran_code_v1.fb';

  static final inferencePackName = 'inference_asset';

  // onEvent:
  static Future<File?> fetchMushaf(
      Function(Map<String, dynamic>) onEvent) async {
    final downloadCompleter = Completer();

    AssetDelivery.getAssetPackStatus((event) {
      onEvent(event);

      final status = event['status'];
      if (status == 'COMPLETED') {
        if (!downloadCompleter.isCompleted) {
          downloadCompleter.complete();
        }
      }
    });

    await AssetDelivery.fetch(mushafPackName);
    await downloadCompleter.future;

    final folderPath = await AssetDelivery.getAssetPackPath(
      assetPackName: mushafPackName,
      count: 1,
      namingPattern: mushafFileName.split('.').first,
      fileExtension: mushafFileName.split('.').last,
    );

    if (folderPath == null) return null;

    return File('$folderPath/$mushafFileName');
  }

  static Future<File?> getMushaf(Function(Map<String, dynamic>) onEvent) async {
    final folderPath = await AssetDelivery.getAssetPackPath(
      assetPackName: mushafPackName,
      count: 1,
      namingPattern: mushafFileName.split('.').first,
      fileExtension: mushafFileName.split('.').last,
    );

    if (folderPath == null) return null;

    return File('$folderPath/$mushafFileName');
  }

  static Future<void> fetchInferenceModel(
    Function(Map<String, dynamic>) onEvent,
  ) async {
    final downloadCompleter = Completer();

    AssetDelivery.getAssetPackStatus((event) {
      onEvent(event);

      final status = event['status'];
      if (status == 'COMPLETED') {
        if (!downloadCompleter.isCompleted) {
          downloadCompleter.complete();
        }
      }
    });

    await AssetDelivery.fetch(inferencePackName);
    await downloadCompleter.future;
  }
}
