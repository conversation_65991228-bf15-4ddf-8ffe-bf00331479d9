import 'package:mushafi/data/source/firestore_data_manager.dart';
import 'package:mushafi/domain/firestore_repository.dart';

class FirestoreRepositoryImpl implements FirestoreRepository {
  final FirestoreDataManager dataManager;

  FirestoreRepositoryImpl(this.dataManager);

  @override
  Future<double?> getRTF(String modelVersion) {
    return dataManager.getRTF(modelVersion);
  }

  @override
  Future<void> setRTF(double rtf, String modelVersion) {
    return dataManager.setRTF(rtf, modelVersion);
  }
}
