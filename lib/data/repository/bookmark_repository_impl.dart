import 'package:drift/drift.dart';
import 'package:mushafi/data/mapper/bookmark_mapper.dart';
import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/domain/bookmark_repository.dart';
import 'package:mushafi/presentation/model/home/<USER>';

class BookmarkRepositoryImpl implements BookmarkRepository {
  final QuranDatabase quranDatabase;

  BookmarkRepositoryImpl(this.quranDatabase);

  @override
  Future<void> addBookmark(String verseKey, int pageNumber) {
    final bookmark = Bookmark(
      verseKey: verseKey,
      pageNumber: pageNumber,
      time: DateTime.now().millisecondsSinceEpoch,
    );
    final insertable = BookmarkMapper.mapToInsertEntity(bookmark);
    return quranDatabase.into(quranDatabase.bookmarkEntity).insertOnConflictUpdate(insertable);
  }

  @override
  Future<void> deleteBookmark(String verseKey) {
    final statement = quranDatabase.delete(quranDatabase.bookmarkEntity)
      ..where((bookmark) => bookmark.verseKey.equals(verseKey));
    return statement.go();
  }

  @override
  Stream<List<Bookmark>> watchBookmarkList() {
    final statement = quranDatabase.select(quranDatabase.bookmarkEntity);
    statement.orderBy([
      (t) => OrderingTerm(expression: t.time, mode: OrderingMode.desc),
    ]);
    return statement.watch().asyncMap((bookmarkList) async {
      return bookmarkList.map((e) => BookmarkMapper.mapFromEntity(e)).toList();
    });
  }
}
