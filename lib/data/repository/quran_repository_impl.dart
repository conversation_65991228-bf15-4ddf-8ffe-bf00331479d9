import 'dart:io';

import 'package:collection/collection.dart';
import 'package:mushafi/data/mapper/quran_verse_mapper.dart';
import 'package:mushafi/data/source/quran_data_manager.dart';
import 'package:mushafi/domain/quran_repository.dart';
import 'package:mushafi/extension/integer_extension.dart';
import 'package:mushafi/extension/quran_word_list_extension.dart';
import 'package:mushafi/extension/verse_key_parser.dart';
import 'package:mushafi/extension/verse_list_extension.dart';
import 'package:mushafi/extension/word_extension.dart';
import 'package:mushafi/generated/flatbuffer_flatbuffer_generated.dart';
import 'package:mushafi/presentation/model/quran/quran_audio_segment.dart';
import 'package:mushafi/presentation/model/quran/quran_page.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/utils/constants.dart';

class QuranRepositoryImpl implements QuranRepository {
  final QuranDataManager quranDataManager;

  QuranRepositoryImpl(this.quranDataManager);

  @override
  Future<void> initQuranData(File file) async {
    await quranDataManager.initQuran(file);
  }

  @override
  Future<void> initTranslationData(File file) async {
    await quranDataManager.initTranslation(file);
  }

  @override
  Future<void> initAudioSegmentData(File file) async {
    await quranDataManager.initAudioSegment(file);
  }

  @override
  Future<QuranPage?> getCompleteQuranPage(int pageNumber) async {
    final page = await quranDataManager.getPageById(pageNumber);
    if (page == null) return null;

    final completeVerses = await quranDataManager.getCompleteVersesByPage(page);
    if (completeVerses == null) return null;

    final previousVerse =
        await quranDataManager.getPreviousVerse(completeVerses.first.verse);
    final nextVerse =
        await quranDataManager.getNextVerse(completeVerses.last.verse);

    final isGlobalFont = await quranDataManager.isGlobalFont();
    List<Word>? globalFontBismillahWordList;
    if (isGlobalFont) {
      globalFontBismillahWordList =
          await quranDataManager.getFirstVerse().then((verse) {
        List<Word> wordList = [];
        verse?.words?.forEach((word) {
          if (word.lineNumber != 2) {
            return;
          }

          if (word.text == null || word.text?.isEmpty == true) {
            return;
          }

          final isNumber = numbersRegex.hasMatch(word.text ?? '');
          if (isNumber) {
            return;
          }

          wordList.add(word);
        });

        return wordList;
      });
    }

    final verses =
        completeVerses.map((completeVerse) => completeVerse.verse).toList();

    final quranWords = verses.convertToQuranWords(
      bismillahWordList: globalFontBismillahWordList,
      previousVerse: previousVerse,
      nextVerse: nextVerse,
    );
    final lines = quranWords.convertToQuranLines();

    final fontByteList = page.font ?? await quranDataManager.getGlobalFont();
    if (fontByteList == null) return null;

    final fontName = (quranWords.first.codeV1.isNotEmpty)
        ? pageNumber.threeDigitsFormat()
        : (await quranDataManager.getCurrentMushafFileName()) ?? '';

    // todo: optimize
    final List<QuranVerse> quranVerses = [];

    for (final completeVerse in completeVerses) {
      final verse = completeVerse.verse;
      final quranAudioSegments = completeVerse.audioSegments.map((segment) {
        return QuranAudioSegment(
          index: segment.index,
          startTime: segment.startTime,
          endTime: segment.endTime,
        );
      }).toList();

      // get the basmallah verse
      final List<QuranWord> bismillahWords = [];
      for (var index = quranWords.length - 1; index >= 0; index--) {
        final quranWord = quranWords[index];
        if (quranWord.verseKey.parseSurahNumber() ==
            verse.verseKey?.parseSurahNumber()) {
          if (quranWord.verseKey.parseVerseNumber() == 0) {
            bismillahWords.add(quranWords.removeAt(index));
          }
        }
      }
      if (bismillahWords.isNotEmpty) {
        final bismillahQuranVerse = QuranVerse(
          id: -1,
          surahId: verse.surahId,
          pageId: verse.pageId,
          verseKey: '${verse.surahId}:0',
          words: bismillahWords.reversed.toList(),
          audioSegments: [],
          translationOrTafseer: '',
        );
        quranVerses.add(bismillahQuranVerse);
      }

      // get the other verses
      final List<QuranWord> words = [];
      for (var index = quranWords.length - 1; index >= 0; index--) {
        if (quranWords[index].verseKey == verse.verseKey) {
          words.add(quranWords.removeAt(index));
        }
      }
      final quranVerse = QuranVerse(
        id: verse.verseId,
        surahId: verse.surahId,
        pageId: verse.pageId,
        verseKey: verse.verseKey ?? '',
        words: words.reversed.toList(),
        audioSegments: quranAudioSegments,
        translationOrTafseer: completeVerse.translation,
      );
      quranVerses.add(quranVerse);
    }

    return QuranPage(
      surahNumber: page.surahId?.last ?? 0,
      juzNumber: page.juzId?.last ?? 0,
      verseList: quranVerses,
      lineList: lines,
      pageNumber: pageNumber,
      fontByteList: fontByteList,
      fontName: fontName,
      isGlobalFont: page.font == null,
    );
  }

  @override
  Future<List<Page>?> getPageList() {
    return quranDataManager.getPages();
  }

  @override
  Future<Page?> getPageByPageNumber(int pageNumber) async {
    return await quranDataManager.getPageById(pageNumber);
  }

  @override
  Future<Juz?> getJuzByJuzNumber(int juzNumber) async {
    return await quranDataManager.getJuzById(juzNumber);
  }

  @override
  Future<List<Juz>?> getJuzList() {
    return quranDataManager.getJuzs();
  }

  @override
  Future<QuranVerse?> getQuranVerseById(int id) async {
    final completeVerse = await quranDataManager.getCompleteVerseById(id);
    if (completeVerse == null) return null;

    return QuranVerseMapper.mapFromCompleteVerse(completeVerse);
  }

  @override
  Future<List<QuranVerse>?> getQuranVerseByIdList(List<int> idList) async {
    final completeVerseList =
        await quranDataManager.getCompleteVerseByIds(idList);
    if (completeVerseList == null) return null;

    return completeVerseList
        .map((data) => QuranVerseMapper.mapFromCompleteVerse(data))
        .whereNotNull()
        .toList();
  }

  @override
  Future<QuranVerse?> getQuranVerseByVerseKey(String verseKey) async {
    final completeVerse =
        await quranDataManager.getCompleteVerseByVerseKey(verseKey);
    if (completeVerse == null) return null;

    return QuranVerseMapper.mapFromCompleteVerse(completeVerse);
  }

  @override
  Future<List<QuranVerse>?> getQuranVerseList(
      String startVerseKey, String endVerseKey) async {
    final completeVerseList =
        await quranDataManager.getCompleteVerseList(startVerseKey, endVerseKey);
    final quranVerseList = completeVerseList
        ?.map((completeVerse) {
          return QuranVerseMapper.mapFromCompleteVerse(completeVerse);
        })
        .whereNotNull()
        .toList();

    return quranVerseList;
  }
}
