import 'dart:async';
import 'dart:io';

import 'package:asset_delivery/asset_delivery.dart';
import 'package:drift/drift.dart';
import 'package:fast_csv/csv_converter.dart';
import 'package:flutter/services.dart';
import 'package:mushafi/data/source/asset_pack_manager.dart';
import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/data/source/minio_data_manager.dart';
import 'package:mushafi/domain/minio_data_repository.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/presentation/model/recitation/reciter.dart';
import 'package:mushafi/presentation/model/translation/translation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:yaml/yaml.dart';

class MinioDataRepositoryImpl implements MinioDataRepository {
  final MinioDataManager minioDataManager;
  final QuranDatabase database;

  List<Reciter> allReciterList = [];
  List<Translation> allTranslationList = [];

  MinioDataRepositoryImpl(this.minioDataManager, this.database);

  @override
  Future<void> updateAllIndexes() {
    return minioDataManager.updateAllIndexes();
  }

  Future<int?> getFileSizeCache(String fileName) async {
    final result = await (database.select(database.fileSizeCacheEntity)
          ..where((tbl) => tbl.fileName.equals(fileName)))
        .getSingleOrNull();
    return result?.size;
  }

  Future<void> insertFileSizeCache(String fileName, int size) async {
    await database.into(database.fileSizeCacheEntity).insert(
          FileSizeCacheEntityCompanion.insert(
            fileName: fileName,
            size: size,
            lastModified: DateTime.now(),
          ),
          mode: InsertMode.replace,
        );
  }

  @override
  Future<File?> fetchMushaf({
    String fileName = 'quran_code_v1.fb',
    Function(double percentage)? onDownloading,
  }) async {
    if (Platform.isAndroid) {
      onEvent(Map<String, dynamic> event) {
        onDownloading?.call(event['downloadProgress']);
      }

      return AssetPackManager.fetchMushaf(onEvent);
    }

    final cachedFileSize = await getFileSizeCache(fileName);
    return minioDataManager.getMushaf(
      fileName,
      cachedFileSize: cachedFileSize,
      cacheAction: (size) async {
        await insertFileSizeCache(fileName, size);
      },
      fetch: true,
    );
  }

  @override
  Future<File?> getMushaf({
    String fileName = 'quran_code_v1.fb',
    Function(double percentage)? onDownloading,
  }) async {
    if (Platform.isAndroid) {
      onEvent(Map<String, dynamic> event) {
        onDownloading?.call(event['downloadProgress']);
      }

      return await AssetPackManager.getMushaf(onEvent) ??
          await fetchMushaf(onDownloading: onDownloading);
    }

    final cachedFileSize = await getFileSizeCache(fileName);
    return minioDataManager.getMushaf(
      fileName,
      cachedFileSize: cachedFileSize,
      cacheAction: (size) async {
        await insertFileSizeCache(fileName, size);
      },
    );
  }

  @override
  Future<File?> getTranslationTafsir({
    String fileName = 'en_sahih_international.fb',
    Function(double percentage)? onDownloading,
  }) async {
    final cachedFileSize = await getFileSizeCache(fileName);
    return minioDataManager.getTranslationTafsir(
      fileName,
      cachedFileSize: cachedFileSize,
      cacheAction: (size) async {
        await insertFileSizeCache(fileName, size);
      },
      onDownloading: onDownloading,
    );
  }

  @override
  Future<File?> getAudioSegment({String fileName = 'afasy.fb'}) async {
    final cachedFileSize = await getFileSizeCache(fileName);
    return minioDataManager.getAudioSegment(
      fileName,
      cachedFileSize: cachedFileSize,
      cacheAction: (size) async {
        await insertFileSizeCache(fileName, size);
      },
    );
  }

  @override
  Future<bool> audioSegmentExists(String fileName) async {
    final cachedFileSize = await getFileSizeCache(fileName);

    final dataDirectory = await getApplicationDocumentsDirectory();
    final path =
        '${dataDirectory.path}/${MinioDataManager.audioSegmentsPath}/$fileName';
    final file = File(path);

    final fileExists = await file.exists();

    if (!fileExists) {
      return false;
    }

    if (await file.length() == 0) {
      return false;
    }

    return await file.length() == cachedFileSize;
  }

  @override
  Future<File?> getAudioSegmentIndex() async {
    final cacheFileName = 'audio_segment_index.fb';
    final cachedFileSize = await getFileSizeCache(cacheFileName);
    return minioDataManager.getAudioSegmentIndex(
      cachedFileSize: cachedFileSize,
      cacheAction: (size) async {
        await insertFileSizeCache(cacheFileName, size);
      },
    );
  }

  @override
  Future<File?> getTranslationTafsirIndex() async {
    final cacheFileName = 'translation_tafsir_index.fb';
    final cachedFileSize = await getFileSizeCache(cacheFileName);
    return minioDataManager.getTranslationTafsirIndex(
      cachedFileSize: cachedFileSize,
      cacheAction: (size) async {
        await insertFileSizeCache(cacheFileName, size);
      },
    );
  }

  @override
  Future<List<String>> getCachedAudioSegmentNameList() async {
    final fileList = await minioDataManager.getCachedAudioSegments();
    final List<String> filteredFileNameList = [];

    await Future.forEach(fileList, (file) async {
      bool fileExists = await file.exists();
      int fileSize = 0;
      if (fileExists) {
        fileSize = await file.length();
      }

      final fileName = file.path.split('/').last;
      final cachedFileSize = await getFileSizeCache(fileName);

      // corrupted file considered as not cached
      if (fileSize == cachedFileSize && fileName.endsWith('.fb')) {
        filteredFileNameList.add(fileName);
      }
    });

    return filteredFileNameList;
  }

  @override
  Future<List<String>> getCachedTranslationTafsirNameList() async {
    final fileList = await minioDataManager.getCachedTranslationTafsirs();
    final List<String> filteredFileNameList = [];

    await Future.forEach(fileList, (file) async {
      bool fileExists = await file.exists();
      int fileSize = 0;
      if (fileExists) {
        fileSize = await file.length();
      }

      final fileName = file.path.split('/').last;
      final cachedFileSize = await getFileSizeCache(fileName);

      // corrupted file considered as not cached
      if (fileSize == cachedFileSize && fileName.endsWith('.fb')) {
        filteredFileNameList.add(fileName);
      }
    });

    return filteredFileNameList;
  }

  Future<List<String>> getCachedModelNameList(List<File> fileList) async {
    final List<String> filteredFileNameList = [];

    await Future.forEach(fileList, (file) async {
      bool fileExists = await file.exists();
      int fileSize = 0;
      if (fileExists) {
        fileSize = await file.length();
      }

      final fileName = file.path.split('/').last;
      final cachedFileSize = await getFileSizeCache(fileName);

      // corrupted file considered as not cached
      if (fileSize == cachedFileSize && fileName.endsWith('.onnx')) {
        filteredFileNameList.add(fileName);
      }
    });

    return filteredFileNameList;
  }

  @override
  Future<List<String>> getCachedAsrModelNameList() async {
    final fileList = await minioDataManager.getCachedAsrModels();
    return getCachedModelNameList(fileList);
  }

  @override
  Future<List<String>> getCachedCondModelNameList() async {
    final fileList = await minioDataManager.getCachedCondModels();
    return getCachedModelNameList(fileList);
  }

  @override
  Future<List<String>> getCachedVadModelNameList() async {
    final fileList = await minioDataManager.getCachedVadModels();
    return getCachedModelNameList(fileList);
  }

  @override
  Future<List<Reciter>> getReciterList() async {
    if (allReciterList.isNotEmpty) return allReciterList;

    final indexFile = await getAudioSegmentIndex();
    if (indexFile == null) return [];

    final csvString = await indexFile.readAsString();
    final rows = CsvConverter().convert(csvString);

    allReciterList.clear();
    for (final row in rows.skip(1)) {
      final reciter = Reciter.fromCsvRow(row);

      if (reciter.audioSegmentFileName.isEmpty) continue;
      if (allReciterList.any((curReciter) => curReciter.name == reciter.name))
        continue;

      allReciterList.add(reciter);
    }

    return allReciterList;
  }

  @override
  Future<Reciter> getReciter(String key) async {
    if (allReciterList.isEmpty) await getReciterList();
    return allReciterList.firstWhere((reciter) => reciter.key == key);
  }

  @override
  Future<List<Translation>> getTranslationList() async {
    if (allTranslationList.isNotEmpty) return allTranslationList;

    final indexFile = await getTranslationTafsirIndex();
    if (indexFile == null) return [];

    final csvString = await indexFile.readAsString();
    final rows = CsvConverter().convert(csvString);

    allTranslationList.clear();
    for (final row in rows.skip(1)) {
      try {
        final translation = Translation.fromCsvRow(row);
        if (translation.fileName.isEmpty) continue;

        if (allTranslationList
            .any((curTranslation) => curTranslation.name == translation.name)) {
          continue;
        }

        allTranslationList.add(translation);
      } catch (e) {
        continue;
      }
    }

    return allTranslationList;
  }

  @override
  Future<Translation> getTranslation(String key) async {
    if (allTranslationList.isEmpty) await getTranslationList();
    return allTranslationList
        .firstWhere((translation) => translation.key == key);
  }

  @override
  Future<void> fetchInferenceModel(
    InferenceModel model, {
    Function(double percentage)? onDownloading,
  }) async {
    if (Platform.isAndroid) {
      onEvent(Map<String, dynamic> event) {
        onDownloading?.call(event['downloadProgress']);
      }

      return AssetPackManager.fetchInferenceModel(onEvent);
    }

    final cachedSize = await getFileSizeCache(model.name);
    final cachedCondSize = await getFileSizeCache(model.condName);
    final cachedVadSize = await getFileSizeCache(model.vadName);
    final cachedCorpusSize = await getFileSizeCache(model.encodedVersesName);

    final Map<String, double> downloadingPercentageMap = {};

    await minioDataManager.fetchInferenceModel(
      model: model,
      cachedSize: cachedSize,
      cachedCondSize: cachedCondSize,
      cachedVadSize: cachedVadSize,
      cachedCorpusSize: cachedCorpusSize,
      onDownloading: (name, percentage) {
        downloadingPercentageMap[name] = percentage;

        double totalPercentage = 0;
        for (final name in downloadingPercentageMap.keys) {
          final modelPercentage = downloadingPercentageMap[name];
          totalPercentage += (modelPercentage ?? 0) / 4;
        }

        onDownloading?.call(totalPercentage);
      },
      cacheAction: (name, size) async {
        await insertFileSizeCache(name, size);
      },
    );
  }

  @override
  Future<bool> doModelsExist(InferenceModel model) async {
    if (Platform.isAndroid) {
      final folderPath = await AssetDelivery.getAssetPackPath(
        assetPackName: AssetPackManager.inferencePackName,
        count: 4,
        namingPattern: '', // todo for ios
        fileExtension: '', // todo for ios
      );

      return folderPath != null;
    }

    final file = await model.file;
    final condFile = await model.condFile;
    final vadFile = await model.vadFile;
    final encodedVersesFile = await model.encodedVersesFile;

    if (file == null ||
        condFile == null ||
        vadFile == null ||
        encodedVersesFile == null) {
      return false;
    }

    final cachedSize = await getFileSizeCache(model.name);
    final cachedCondSize = await getFileSizeCache(model.condName);
    final cachedVadSize = await getFileSizeCache(model.vadName);
    final cachedCorpusSize = await getFileSizeCache(model.encodedVersesName);

    return await file.length() == cachedSize &&
        await condFile.length() == cachedCondSize &&
        await vadFile.length() == cachedVadSize &&
        await encodedVersesFile.length() == cachedCorpusSize;
  }

  @override
  Future<File?> updateModelCompatIndex() async {
    if (Platform.isAndroid) {
      final byteData = await rootBundle.load(Assets.yamlsModelIndex);
      final uint8List = byteData.buffer.asUint8List();

      final dataDirectory = await getApplicationDocumentsDirectory();
      final dirPath =
          '${dataDirectory.path}/${MinioDataManager.modelCompatPath}';
      final path = '$dirPath/index.yaml';

      // Create the directory if it doesn't exist
      final dir = Directory(dirPath);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      final file = File(path);
      await file.writeAsBytes(uint8List);

      return file;
    }

    return minioDataManager.updateModelCompatIndex();
  }

  @override
  Future<File?> getModelCompatIndex() async {
    if (Platform.isAndroid) {
      final byteData = await rootBundle.load(Assets.yamlsModelIndex);
      final uint8List = byteData.buffer.asUint8List();

      final dataDirectory = await getApplicationDocumentsDirectory();
      final dirPath =
          '${dataDirectory.path}/${MinioDataManager.modelCompatPath}';
      final path = '$dirPath/index.yaml';

      // Create the directory if it doesn't exist
      final dir = Directory(dirPath);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      final file = File(path);
      await file.writeAsBytes(uint8List);

      return file;
    }

    return minioDataManager.getModelCompatIndex();
  }

  @override
  Future<String?> updateAndGetLatestModelVersion() async {
    final indexFile =
        await updateModelCompatIndex() ?? await getModelCompatIndex();
    if (indexFile == null) return null;

    final indexYaml = loadYaml(await indexFile.readAsString());
    final indexMap = Map<int, YamlList>.from(indexYaml);

    if (!indexMap.containsKey(InferenceModel.modelCompat)) {
      return null;
    }

    final currentModelVersionList =
        List<String>.from(indexMap[InferenceModel.modelCompat] ?? []);
    return currentModelVersionList.lastOrNull;
  }

  @override
  Future<String?> getLatestModelVersion() async {
    final indexFile = await getModelCompatIndex();
    if (indexFile == null) return null;

    final indexYaml = loadYaml(await indexFile.readAsString());
    final indexMap = Map<int, YamlList>.from(indexYaml);

    if (!indexMap.containsKey(InferenceModel.modelCompat)) {
      return null;
    }

    final currentModelVersionList =
        List<String>.from(indexMap[InferenceModel.modelCompat] ?? []);
    return currentModelVersionList.lastOrNull;
  }

  @override
  Future<Map<int, List<String>>> getModelIndexMap() async {
    final indexFile = await getModelCompatIndex();
    if (indexFile == null) return {};

    final indexYaml = loadYaml(await indexFile.readAsString());
    return Map<int, YamlList>.from(indexYaml).map((key, value) {
      return MapEntry(key, List<String>.from(value));
    });
  }
}
