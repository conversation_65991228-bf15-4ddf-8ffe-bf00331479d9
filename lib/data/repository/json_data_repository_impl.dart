import 'package:mushafi/data/source/json_data_manager.dart';
import 'package:mushafi/domain/json_data_repository.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';

class JsonDataRepositoryImpl implements JsonDataRepository {
  final JsonDataManager jsonDataManager;

  JsonDataRepositoryImpl(this.jsonDataManager);

  @override
  Future<List<Surah>> getSurahList() async {
    return jsonDataManager.getSurahs();
  }
}
