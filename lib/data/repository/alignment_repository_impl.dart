import 'package:drift/drift.dart';
import 'package:mushafi/data/mapper/alignment_word_result_mapper.dart';
import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/domain/alignment_repository.dart';
import 'package:mushafi/presentation/model/quran/alignment_word_result.dart';

class AlignmentRepositoryImpl implements AlignmentRepository {
  final QuranDatabase quranDatabase;

  AlignmentRepositoryImpl(this.quranDatabase);

  @override
  Future<void> addAlignmentWordResults(
      List<AlignmentWordResult> resultList) async {
    await quranDatabase.batch((batch) {
      final entityList = resultList
          .map((result) => AlignmentWordResultMapper.mapToInsertEntity(result))
          .toList();
      batch.insertAllOnConflictUpdate(
          quranDatabase.alignmentWordResultEntity, entityList);
    });
  }

  @override
  Future<List<AlignmentWordResult>> getAlignmentWordResults(int trackId) {
    final statement = quranDatabase
        .select(quranDatabase.alignmentWordResultEntity)
      ..where((result) => result.trackId.equals(trackId));

    return statement.get().then((value) =>
        value.map((e) => AlignmentWordResultMapper.mapFromEntity(e)).toList());
  }

  @override
  Future<void> deleteAlignmentWordResults(
      int trackId, int startTime, int endTime) {
    final statement =
        quranDatabase.delete(quranDatabase.alignmentWordResultEntity)
          ..where((result) {
            return result.trackId.equals(trackId);
          })
          ..where((result) {
            return result.time.isBetweenValues(startTime, endTime);
          });
    return statement.go();
  }

  @override
  Future<void> deleteTrackResults(int trackId) {
    final statement =
        quranDatabase.delete(quranDatabase.alignmentWordResultEntity)
          ..where((result) {
            return result.trackId.equals(trackId);
          });
    return statement.go();
  }
}
