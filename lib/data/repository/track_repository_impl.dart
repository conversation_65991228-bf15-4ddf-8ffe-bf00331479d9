import 'package:drift/drift.dart';
import 'package:mushafi/data/mapper/track_mapper.dart';
import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/domain/track_repository.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/utils/common_utils.dart';

class TrackRepositoryImpl implements TrackRepository {
  final QuranDatabase quranDatabase;

  TrackRepositoryImpl(this.quranDatabase);

  @override
  Future<Track> addTrack(Track track) async {
    final insertable = TrackMapper.mapToInsertEntity(track, withId: false);
    final id =
        await quranDatabase.into(quranDatabase.trackEntity).insert(insertable);
    return track.copyWith(id: id);
  }

  @override
  Future<bool> updateTrack(Track track) {
    final insertable = TrackMapper.mapToInsertEntity(track, withId: true);
    return quranDatabase.update(quranDatabase.trackEntity).replace(insertable);
  }

  @override
  Future<void> updateHighlightedVerse(int id, String? verseKey) async {
    final statement = quranDatabase.update(quranDatabase.trackEntity)
      ..where((track) => track.id.equals(id));

    await statement
        .write(TrackEntityCompanion(lastHighlightedVerseKey: Value(verseKey)));
  }

  @override
  Future<Track?> getTrack(int id) async {
    final statement = quranDatabase.select(quranDatabase.trackEntity)
      ..where((track) => track.id.equals(id));
    final entity = await statement.getSingleOrNull();

    if (entity == null) return null;
    final isDarkMode = await CommonUtils.isDarkMode;
    return TrackMapper.mapFromEntity(entity, isDarkMode);
  }

  @override
  Stream<List<Track>> watchTrackList() {
    return quranDatabase
        .select(quranDatabase.trackEntity)
        .watch()
        .asyncMap((trackList) async {
      final isDarkMode = await CommonUtils.isDarkMode;
      return trackList
          .map((e) => TrackMapper.mapFromEntity(e, isDarkMode))
          .toList();
    });
  }

  @override
  Future<void> deleteTrack(int id) {
    final statement = quranDatabase.delete(quranDatabase.trackEntity)
      ..where((track) => track.id.equals(id));
    return statement.go();
  }
}
