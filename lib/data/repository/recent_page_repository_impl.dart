import 'package:collection/collection.dart';
import 'package:drift/drift.dart';
import 'package:mushafi/data/mapper/recent_page_mapper.dart';
import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/domain/json_data_repository.dart';
import 'package:mushafi/domain/recent_page_repository.dart';
import 'package:mushafi/presentation/model/home/<USER>';

class RecentPageRepositoryImpl implements RecentPageRepository {
  final QuranDatabase quranDatabase;
  final JsonDataRepository jsonDataRepository;

  RecentPageRepositoryImpl(this.quranDatabase, this.jsonDataRepository);

  @override
  Future<void> addRecentPage(RecentPage recentPage) async {
    final surahList = await jsonDataRepository.getSurahList();
    final surah = surahList.firstWhereOrNull((surah) {
      final maxPage = surah.pages.max;
      final minPage = surah.pages.min;
      return recentPage.pageNumber >= minPage &&
          recentPage.pageNumber <= maxPage;
    });
    if (surah == null) return;

    final recentPageWithId = RecentPage(
      id: '${surah.id}',
      pageNumber: recentPage.pageNumber,
      time: recentPage.time,
    );
    final insertable = RecentPageMapper.mapToInsertEntity(recentPageWithId);
    quranDatabase
        .into(quranDatabase.recentPageEntity)
        .insertOnConflictUpdate(insertable);
  }

  @override
  Stream<List<RecentPage>> watchRecentPageList() {
    final statement = quranDatabase.select(quranDatabase.recentPageEntity);
    statement.orderBy([
      (t) => OrderingTerm(expression: t.time, mode: OrderingMode.desc),
    ]);
    return statement.watch().asyncMap((recentPageList) async {
      return recentPageList
          .map((e) => RecentPageMapper.mapFromEntity(e))
          .toList();
    });
  }
}
