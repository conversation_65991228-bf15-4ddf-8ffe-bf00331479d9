import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/repository/alignment_repository_impl.dart';
import 'package:mushafi/data/repository/bookmark_repository_impl.dart';
import 'package:mushafi/data/repository/firestore_repository_impl.dart';
import 'package:mushafi/data/repository/json_data_repository_impl.dart';
import 'package:mushafi/data/repository/minio_data_repository_impl.dart';
import 'package:mushafi/data/repository/quran_repository_impl.dart';
import 'package:mushafi/data/repository/recent_page_repository_impl.dart';
import 'package:mushafi/data/repository/track_repository_impl.dart';
import 'package:mushafi/domain/alignment_repository.dart';
import 'package:mushafi/domain/bookmark_repository.dart';
import 'package:mushafi/domain/firestore_repository.dart';
import 'package:mushafi/domain/json_data_repository.dart';
import 'package:mushafi/domain/minio_data_repository.dart';
import 'package:mushafi/domain/quran_repository.dart';
import 'package:mushafi/domain/recent_page_repository.dart';
import 'package:mushafi/domain/track_repository.dart';

import 'data_source_provider.dart';

final quranRepositoryProvider = Provider<QuranRepository>((ref) {
  final quranDataManager = ref.watch(quranDataManagerProvider);
  return QuranRepositoryImpl(quranDataManager);
});

final trackRepositoryProvider = Provider<TrackRepository>((ref) {
  final quranDatabase = ref.watch(quranDatabaseProvider);
  return TrackRepositoryImpl(quranDatabase);
});

final jsonDataRepositoryProvider = Provider<JsonDataRepository>((ref) {
  final jsonDataManager = ref.watch(jsonDataManagerProvider);
  return JsonDataRepositoryImpl(jsonDataManager);
});

final alignmentRepositoryProvider = Provider<AlignmentRepository>((ref) {
  final quranDatabase = ref.watch(quranDatabaseProvider);
  return AlignmentRepositoryImpl(quranDatabase);
});

final minioDataRepositoryProvider = Provider<MinioDataRepository>((ref) {
  final minioDataManager = ref.watch(minioDataManagerProvider);
  final quranDatabase = ref.watch(quranDatabaseProvider);
  return MinioDataRepositoryImpl(minioDataManager, quranDatabase);
});

final recentPageRepositoryProvider = Provider<RecentPageRepository>((ref) {
  final quranDatabase = ref.watch(quranDatabaseProvider);
  final jsonDataRepository = ref.watch(jsonDataRepositoryProvider);
  return RecentPageRepositoryImpl(quranDatabase, jsonDataRepository);
});

final firestoreRepositoryProvider = Provider<FirestoreRepository>((ref) {
  final firestoreDataManager = ref.watch(firestoreDataManagerProvider);
  return FirestoreRepositoryImpl(firestoreDataManager);
});

final bookmarkRepositoryProvider = Provider<BookmarkRepository>((ref) {
  final quranDatabase = ref.watch(quranDatabaseProvider);
  return BookmarkRepositoryImpl(quranDatabase);
});
