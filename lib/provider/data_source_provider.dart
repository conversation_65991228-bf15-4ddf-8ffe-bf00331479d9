import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/source/db/quran_database.dart';
import 'package:mushafi/data/source/firestore_data_manager.dart';
import 'package:mushafi/data/source/json_data_manager.dart';
import 'package:mushafi/data/source/minio_data_manager.dart';
import 'package:mushafi/data/source/quran_data_manager.dart';

final quranDataManagerProvider = Provider<QuranDataManager>((ref) {
  return QuranDataManager();
});

final quranDatabaseProvider = Provider<QuranDatabase>((ref) {
  return QuranDatabase();
});

final jsonDataManagerProvider = Provider<JsonDataManager>((ref) {
  return JsonDataManager();
});

final minioDataManagerProvider = Provider<MinioDataManager>((ref) {
  return MinioDataManager();
});

final firestoreDataManagerProvider = Provider<FirestoreDataManager>((ref) {
  return FirestoreDataManager();
});
