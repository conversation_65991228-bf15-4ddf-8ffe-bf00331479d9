import 'dart:ui';

final class ColorResource {
  ColorResource._();

  // Primary Color
  static Color primary(bool isDark) =>
      isDark ? const Color(0xFF96C8A6) : const Color(0xFF2F9D54);
  static Color onPrimary(bool isDark) =>
      isDark ? const Color(0xFF1A202C) : const Color(0xFFFFFFFF);
  static Color danger(bool isDark) =>
      isDark ? const Color(0xFFEE5662) : const Color(0xFFFF0216);

  // Text Colors
  static Color textDefault(bool isDark) =>
      isDark ? const Color(0xFFC5C5C5) : const Color(0xFF1A202C);
  static Color textGrey(bool isDark) => const Color(0xFF7D8898);
  static Color textBlue(bool isDark) =>
      isDark ? const Color(0xFF4C9AFE) : const Color(0xFF007AFF);

  // Background Colors
  static Color backgroundDefault(bool isDark) =>
      isDark ? const Color(0xFF121212) : const Color(0xFFE2EDEC);
  static Color backgroundGreyLight(bool isDark) =>
      isDark ? const Color(0xFF7D8898) : const Color(0xFFEDF2F7);
  static Color backgroundGrey(bool isDark) =>
      isDark ? const Color(0xFF2A2C2F) : const Color(0xFFE4EDF1);
  static Color backgroundGreyDark(bool isDark) => const Color(0xFFCBD5E0);
  static Color backgroundBlackAlpha90(bool isDark) =>
      isDark ? const Color(0xE6FFFFFF) : const Color(0xE6000000);
  static Color backgroundWhite(bool isDark) =>
      isDark ? const Color(0xFF1E1E1E) : const Color(0xFFFAFAFA);
  static Color backgroundBlue(bool isDark) =>
      isDark ? const Color(0xFF4A5596) : const Color(0xFF5B68C9);

  // Chip Colors
  static Color chipBackgroundSelected(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF1A202C);
  static Color chipTextSelected(bool isDark) =>
      isDark ? const Color(0xFF00091B) : const Color(0xFFFFFFFF);
  static Color chipBackground(bool isDark) =>
      isDark ? const Color(0xFF242426) : const Color(0xFFF1F8FB);
  static Color chipText(bool isDark) =>
      isDark ? const Color(0xFF949396) : const Color(0xFF1A202C);

  // Border Color
  static Color border(bool isDark) => const Color(0xFFCBD5E0);

  // Button Segment Colors
  static Color buttonSegmentBackgroundSelected(bool isDark) =>
      isDark ? const Color(0xFF001312) : const Color(0xFFD8F3E1);
  static Color buttonSegmentTextSelected(bool isDark) =>
      isDark ? const Color(0xFF96C8A6) : const Color(0xFF2F9D54);
  static Color buttonSegmentBackground(bool isDark) =>
      isDark ? const Color(0xFF1E1E1E) : const Color(0xFFF1F8FB);
  static Color buttonSegmentText(bool isDark) =>
      isDark ? const Color(0xFF949396) : const Color(0xFF7D8898);

  // Default Theme Color
  static Color defaultDrawer1(bool isDark) =>
      isDark ? const Color(0xFF1E1E1E) : const Color(0xFFE2E9F0);
  static Color defaultDrawer2(bool isDark) =>
      isDark ? const Color(0xFF1A1A1A) : const Color(0xFFEDF2F7);
  static Color defaultHighlightFull(bool isDark) =>
      isDark ? const Color(0xFF326048) : const Color(0xFF96C8A6);
  static Color defaultHighlightFaded(bool isDark) =>
      isDark ? const Color(0xFF1F3A2E) : const Color(0xFFD8F3E1);
  static Color defaultBackground(bool isDark) =>
      isDark ? const Color(0xFF121212) : const Color(0xFFFFFFFF);
  static Color defaultActivityCardBackground(bool isDark) =>
      isDark ? const Color(0xFF00091B) : const Color(0xFFFFFFFF);
  static Color defaultActivityCardBorder(bool isDark) =>
      isDark ? const Color(0xFF1A1A1A) : const Color(0xFFEDF2F7);
  static Color defaultSurah1(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color defaultSurah2(bool isDark) =>
      isDark ? const Color(0xFF7D8898) : const Color(0xFF000000);
  static Color defaultSurah3(bool isDark) =>
      isDark ? const Color(0xFF7D8898) : const Color(0xFF7D8898);
  static Color defaultBorder1(bool isDark) =>
      isDark ? const Color(0x00FFFFFF) : const Color(0xFFFFFFFF);
  static Color defaultBorder2(bool isDark) =>
      isDark ? const Color(0xFF293644) : const Color(0xFFCBD5E0);
  static Color defaultVerse1(bool isDark) =>
      isDark ? const Color(0xFFA2A2A2) : const Color(0xFFCBD5E0);
  static Color defaultVerse2(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color defaultVerse3(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color defaultHizb1(bool isDark) =>
      isDark ? const Color(0xFF00091B) : const Color(0xFFFFFFFF);
  static Color defaultHizb2(bool isDark) =>
      isDark ? const Color(0xFF7D8898) : const Color(0xFF7D8898);
  static Color defaultHizb3(bool isDark) =>
      isDark ? const Color(0xFF4E8D63) : const Color(0xFF96C8A6);

  // Medina Theme Color
  static Color medinaDrawer1(bool isDark) =>
      isDark ? const Color(0xFF1A1E1B) : const Color(0xFFCBE5BE);
  static Color medinaDrawer2(bool isDark) =>
      isDark ? const Color(0xFFFBFFF9) : const Color(0xFF081701);
  static Color medinaHighlightFull(bool isDark) =>
      isDark ? const Color(0xFF63325A) : const Color(0xFFDC8EBB);
  static Color medinaHighlightFaded(bool isDark) =>
      isDark ? const Color(0xFF3D1F37) : const Color(0xFFEDC6DD);
  static Color medinaBackground(bool isDark) =>
      isDark ? const Color(0xFF0F1210) : const Color(0xFFFBFFF9);
  static Color medinaActivityCardBackground(bool isDark) =>
      isDark ? const Color(0xFF081701) : const Color(0xFFFBFFF9);
  static Color medinaActivityCardBorder(bool isDark) =>
      isDark ? const Color(0xFF325321) : const Color(0xFFCBE5BE);
  static Color medinaBorder1(bool isDark) =>
      isDark ? const Color(0xFF1C440A) : const Color(0xFFB8DBA9);
  static Color medinaBorder2(bool isDark) =>
      isDark ? const Color(0xFFE0DCDD) : const Color(0xFF231F20);
  static Color medinaBorder3(bool isDark) =>
      isDark ? const Color(0xFF3A771E) : const Color(0xFF5B9144);
  static Color medinaBorder4(bool isDark) =>
      isDark ? const Color(0xFF1C440A) : const Color(0xFFAFD8A3);
  static Color medinaBorder5(bool isDark) =>
      isDark ? const Color(0xFF4C1837) : const Color(0xFFE79EC5);
  static Color medinaBorder6(bool isDark) =>
      isDark ? const Color(0xFF844569) : const Color(0xFFAD5F84);
  static Color medinaBorder7(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color medinaBorder8(bool isDark) =>
      isDark ? const Color(0xFF844569) : const Color(0xFFE89DC4);
  static Color medinaBorder9(bool isDark) =>
      isDark ? const Color(0xFF4C8C40) : const Color(0xFF4C8C40);
  static Color medinaBorder10(bool isDark) =>
      isDark ? const Color(0xFFE79EC5) : const Color(0xFFE79EC5);
  static Color medinaBorder11(bool isDark) =>
      isDark ? const Color(0xFF753E5A) : const Color(0xFF753E5A);
  static Color medinaBorder12(bool isDark) =>
      isDark ? const Color(0xFF4C4C4E) : const Color(0xFF4C4C4E);
  static Color medinaBorder13(bool isDark) =>
      isDark ? const Color(0x00000000) : const Color(0x00000000);
  static Color medinaSurah1(bool isDark) =>
      isDark ? const Color(0xFF1C440A) : const Color(0xFFCBE5BE);
  static Color medinaSurah2(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color medinaSurah3(bool isDark) =>
      isDark ? const Color(0xFF3A771E) : const Color(0xFF419644);
  static Color medinaSurah4(bool isDark) =>
      isDark ? const Color(0xFF1C440A) : const Color(0xFFCBDFC0);
  static Color medinaSurah5(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color medinaSurah6(bool isDark) =>
      isDark ? const Color(0xFF844569) : const Color(0xFFDC8EBB);
  static Color medinaVerse1(bool isDark) =>
      isDark ? const Color(0xFF844569) : const Color(0xFFDC8EBB);
  static Color medinaVerse2(bool isDark) =>
      isDark ? const Color(0xFF1C440A) : const Color(0xFFCBE5BE);
  static Color medinaVerse3(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color medinaHizb1(bool isDark) =>
      isDark ? const Color(0xFF1C440A) : const Color(0xFFCBE5BE);
  static Color medinaHizb2(bool isDark) =>
      isDark ? const Color(0xFFF1F8FB) : const Color(0xFF1A202C);
  static Color medinaHizb3(bool isDark) =>
      isDark ? const Color(0xFF3A771E) : const Color(0xFF419644);
  static Color medinaHizb4(bool isDark) =>
      isDark ? const Color(0xFF081701) : const Color(0xFFFBFFF9);
  static Color medinaHizb5(bool isDark) =>
      isDark ? const Color(0xFF844569) : const Color(0xFFDC8EBB);

  // Old Medina Theme Color
  static Color oldMedinaDrawer1(bool isDark) =>
      isDark ? const Color(0xFF1E1D1B) : const Color(0xFFF2EFDB);
  static Color oldMedinaDrawer2(bool isDark) =>
      isDark ? const Color(0xFFFFFDF5) : const Color(0xFF1A1701);
  static Color oldMedinaHighlightFull(bool isDark) =>
      isDark ? const Color(0xFF496337) : const Color(0xFF7EB37B);
  static Color oldMedinaHighlightFaded(bool isDark) =>
      isDark ? const Color(0xFF2C3D21) : const Color(0xFFD6EACA);
  static Color oldMedinaBackground(bool isDark) =>
      isDark ? const Color(0xFF121211) : const Color(0xFFFFFFDF5);
  static Color oldMedinaActivityCardBackground(bool isDark) =>
      isDark ? const Color(0xFF1A1701) : const Color(0xFFFFFFDF5);
  static Color oldMedinaActivityCardBorder(bool isDark) =>
      isDark ? const Color(0xFF342F09) : const Color(0xFFFAF7E1);
  static Color oldMedinaBorder1(bool isDark) =>
      isDark ? const Color(0xFF1A1701) : const Color(0xFFF6F2DC);
  static Color oldMedinaBorder2(bool isDark) =>
      isDark ? const Color(0xFFA88745) : const Color(0xFF917237);
  static Color oldMedinaBorder3(bool isDark) =>
      isDark ? const Color(0xFF7EB37B) : const Color(0xFF358B42);
  static Color oldMedinaBorder4(bool isDark) =>
      isDark ? const Color(0xFFD6EACA) : const Color(0xFFD5E8C6);
  static Color oldMedinaBorder5(bool isDark) =>
      isDark ? const Color(0xFFA88745) : const Color(0xFF917337);
  static Color oldMedinaSurah1(bool isDark) =>
      isDark ? const Color(0xFF1A1701) : const Color(0x00000000);
  static Color oldMedinaSurah2(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color oldMedinaSurah3(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF231F20);
  static Color oldMedinaSurah4(bool isDark) =>
      isDark ? const Color(0xFFA88745) : const Color(0xFF95793E);
  static Color oldMedinaSurah5(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF020202);
  static Color oldMedinaSurah6(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color oldMedinaSurah7(bool isDark) =>
      isDark ? const Color(0xFF324B1E) : const Color(0xFFD5E9C9);
  static Color oldMedinaSurah8(bool isDark) =>
      isDark ? const Color(0xFFA88745) : const Color(0xFF967A42);
  static Color oldMedinaSurah9(bool isDark) =>
      isDark ? const Color(0xFF373313) : const Color(0xFFFBF7DF);
  static Color oldMedinaVerse1(bool isDark) =>
      isDark ? const Color(0xFF7EB37B) : const Color(0xFF7EB480);
  static Color oldMedinaVerse2(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color oldMedinaVerse3(bool isDark) =>
      isDark ? const Color(0xFFA88745) : const Color(0xFF96793E);
  static Color oldMedinaHizb1(bool isDark) =>
      isDark ? const Color(0xFFA88745) : const Color(0xFF96793E);
  static Color oldMedinaHizb2(bool isDark) =>
      isDark ? const Color(0xFFF1F8FB) : const Color(0xFF1A202C);
  static Color oldMedinaHizb3(bool isDark) =>
      isDark ? const Color(0xFF2C471C) : const Color(0xFF7EB37B);

  // Qaloon Theme Color
  static Color qaloonDrawer1(bool isDark) =>
      isDark ? const Color(0xFF1A1D20) : const Color(0xFFBCE4F3);
  static Color qaloonDrawer2(bool isDark) =>
      isDark ? const Color(0xFFF3FAFD) : const Color(0xFF00161F);
  static Color qaloonHighlightFull(bool isDark) =>
      isDark ? const Color(0xFF63324D) : const Color(0xFFE17FAF);
  static Color qaloonHighlightFaded(bool isDark) =>
      isDark ? const Color(0xFF3D1F2C) : const Color(0xFFECA7CA);
  static Color qaloonBackground(bool isDark) =>
      isDark ? const Color(0xFF101214) : const Color(0xFFF3FAFD);
  static Color qaloonActivityCardBackground(bool isDark) =>
      isDark ? const Color(0xFF00161F) : const Color(0xFFF3FAFD);
  static Color qaloonActivityCardBorder(bool isDark) =>
      isDark ? const Color(0xFF133C50) : const Color(0xFFC4E3F1);
  static Color qaloonBorder1(bool isDark) =>
      isDark ? const Color(0xFF7D8898) : const Color(0xFF000000);
  static Color qaloonBorder2(bool isDark) =>
      isDark ? const Color(0xFF104559) : const Color(0xFFB4ECFB);
  static Color qaloonBorder3(bool isDark) =>
      isDark ? const Color(0xFF00161F) : const Color(0xFFBFBFBF);
  static Color qaloonBorder4(bool isDark) =>
      isDark ? const Color(0xFF2A9AD2) : const Color(0xFF0D9FDB);
  static Color qaloonBorder5(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color qaloonBorder6(bool isDark) =>
      isDark ? const Color(0xFFECA7CA) : const Color(0xFFE11482);
  static Color qaloonSurah1(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color qaloonSurah2(bool isDark) =>
      isDark ? const Color(0xFFCBD5E0) : const Color(0xFF231F20);
  static Color qaloonSurah3(bool isDark) =>
      isDark ? const Color(0xFF2A9AD2) : const Color(0xFF3C96B8);
  static Color qaloonSurah4(bool isDark) =>
      isDark ? const Color(0x00000000) : const Color(0x00000000);
  static Color qaloonSurah5(bool isDark) =>
      isDark ? const Color(0xFFCA5F9A) : const Color(0xFFECA7CA);
  static Color qaloonSurah6(bool isDark) =>
      isDark ? const Color(0xFFCBD5E0) : const Color(0xFF000000);
  static Color qaloonSurah7(bool isDark) =>
      isDark ? const Color(0xFF104559) : const Color(0xFFC3E8F6);
  static Color qaloonSurah8(bool isDark) =>
      isDark ? const Color(0xFFADB9BF) : const Color(0xFFADB9BF);
  static Color qaloonSurah9(bool isDark) =>
      isDark ? const Color(0xFF104559) : const Color(0xFF84979F);
  static Color qaloonSurah10(bool isDark) =>
      isDark ? const Color(0xFF2A9AD2) : const Color(0xFF10A6DE);
  static Color qaloonSurah11(bool isDark) =>
      isDark ? const Color(0xFFECA7CA) : const Color(0xFFDD4A9A);
  static Color qaloonSurah12(bool isDark) =>
      isDark ? const Color(0xFFECA7CA) : const Color(0xFFF0C3DB);
  static Color qaloonSurah13(bool isDark) =>
      isDark ? const Color(0xFFECA7CA) : const Color(0xFFFFFFFF);
  static Color qaloonSurah14(bool isDark) =>
      isDark ? const Color(0xFF231F20) : const Color(0xFF231F20);
  static Color qaloonSurah15(bool isDark) =>
      isDark ? const Color(0xFF424244) : const Color(0xFF424244);
  static Color qaloonSurah16(bool isDark) =>
      isDark ? const Color(0xFF5C5C5F) : const Color(0xFF5C5C5F);
  static Color qaloonSurah17(bool isDark) =>
      isDark ? const Color(0xFF104559) : const Color(0xFFBCE4F3);
  static Color qaloonVerse1(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color qaloonVerse2(bool isDark) =>
      isDark ? const Color(0xFF2A9AD2) : const Color(0xFF288EBE);
  static Color qaloonVerse3(bool isDark) =>
      isDark ? const Color(0xFFE0DCDD) : const Color(0xFF231F20);
  static Color qaloonVerse4(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color qaloonHizb1(bool isDark) =>
      isDark ? const Color(0xFF1C6A88) : const Color(0xFF268BBD);
  static Color qaloonHizb2(bool isDark) =>
      isDark ? const Color(0xFFF1F8FB) : const Color(0xFF1A202C);
  static Color qaloonHizb3(bool isDark) =>
      isDark ? const Color(0xFF104559) : const Color(0xFFBCE4F3);
  static Color qaloonHizb4(bool isDark) =>
      isDark ? const Color(0xFF1A202C) : const Color(0xFFFFFFFF);
  static Color qaloonHizb5(bool isDark) =>
      isDark ? const Color(0xFF00161F) : const Color(0xFFF3FAFD);
  static Color qaloonHizb6(bool isDark) =>
      isDark ? const Color(0xFF8D3164) : const Color(0xFFECA7CA);

  // Medium Medina Theme Color
  static Color mediumMedinaDrawer1(bool isDark) =>
      isDark ? const Color(0xFF1E1C1A) : const Color(0xFFE8D9C8);
  static Color mediumMedinaDrawer2(bool isDark) =>
      isDark ? const Color(0xFFFFFBF8) : const Color(0xFF1D1000);
  static Color mediumMedinaHighlightFull(bool isDark) =>
      isDark ? const Color(0xFF4D3969) : const Color(0xFFAF8AB5);
  static Color mediumMedinaHighlightFaded(bool isDark) =>
      isDark ? const Color(0xFF2F2240) : const Color(0xFFCEB6D9);
  static Color mediumMedinaBackground(bool isDark) =>
      isDark ? const Color(0xFF121110) : const Color(0xFFFFFBF8);
  static Color mediumMedinaActivityCardBackground(bool isDark) =>
      isDark ? const Color(0xFF1D1000) : const Color(0xFFFFFBF8);
  static Color mediumMedinaActivityCardBorder(bool isDark) =>
      isDark ? const Color(0xFF2F2212) : const Color(0xFFF4ECE2);
  static Color mediumMedinaBorder1(bool isDark) =>
      isDark ? const Color(0xFF9F6D3B) : const Color(0xFFCE9E6E);
  static Color mediumMedinaBorder2(bool isDark) =>
      isDark ? const Color(0xFFCBD5E0) : const Color(0xFF000000);
  static Color mediumMedinaBorder3(bool isDark) =>
      isDark ? const Color(0xFF1D1000) : const Color(0xFFFFFFFF);
  static Color mediumMedinaBorder4(bool isDark) =>
      isDark ? const Color(0xFFCEB6D9) : const Color(0xFFA28CAC);
  static Color mediumMedinaBorder5(bool isDark) =>
      isDark ? const Color(0xFFA28CAC) : const Color(0xFF9B9693);
  static Color mediumMedinaSurah1(bool isDark) =>
      isDark ? const Color(0xFF49361F) : const Color(0xFFF6ECE1);
  static Color mediumMedinaSurah2(bool isDark) =>
      isDark ? const Color(0xFF9F6D3B) : const Color(0xFFD09E6D);
  static Color mediumMedinaSurah3(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color mediumMedinaSurah4(bool isDark) =>
      isDark ? const Color(0xFF7D8898) : const Color(0xFFD1D3D4);
  static Color mediumMedinaSurah5(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color mediumMedinaVerse1(bool isDark) =>
      isDark ? const Color(0xFFA28CAC) : const Color(0xFFD1D3D4);
  static Color mediumMedinaVerse2(bool isDark) =>
      isDark ? const Color(0xFF49361F) : const Color(0xFFF6ECE1);
  static Color mediumMedinaVerse3(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color mediumMedinaHizb1(bool isDark) =>
      isDark ? const Color(0xFF49361F) : const Color(0xFFE8D9C8);
  static Color mediumMedinaHizb2(bool isDark) =>
      isDark ? const Color(0xFFF1F8FB) : const Color(0xFF1A202C);

  // Shubah Theme Color
  static Color shubahDrawer1(bool isDark) =>
      isDark ? const Color(0xFF1E1919) : const Color(0xFFF3C6B0);
  static Color shubahDrawer2(bool isDark) =>
      isDark ? const Color(0xFFFFF5F0) : const Color(0xFF1F0A00);
  static Color shubahHighlightFull(bool isDark) =>
      isDark ? const Color(0xFF4D5728) : const Color(0xFF979F5C);
  static Color shubahHighlightFaded(bool isDark) =>
      isDark ? const Color(0xFF2F3417) : const Color(0xFFCCD398);
  static Color shubahBackground(bool isDark) =>
      isDark ? const Color(0xFF130F0F) : const Color(0xFFFFF5F0);
  static Color shubahActivityCardBackground(bool isDark) =>
      isDark ? const Color(0xFF1F0A00) : const Color(0xFFFFF5F0);
  static Color shubahActivityCardBorder(bool isDark) =>
      isDark ? const Color(0xFF652C0F) : const Color(0xFFF3C6B0);
  static Color shubahBorder1(bool isDark) =>
      isDark ? const Color(0xFFCBD5E0) : const Color(0xFF231F20);
  static Color shubahBorder2(bool isDark) =>
      isDark ? const Color(0xFF1F0A00) : const Color(0xFFFFFFFF);
  static Color shubahBorder3(bool isDark) =>
      isDark ? const Color(0xFF90211D) : const Color(0xFF781F19);
  static Color shubahBorder4(bool isDark) =>
      isDark ? const Color(0xFF652C0F) : const Color(0xFFDAB49F);
  static Color shubahBorder5(bool isDark) =>
      isDark ? const Color(0xFF652C0F) : const Color(0xFFF0C78F);
  static Color shubahBorder6(bool isDark) =>
      isDark ? const Color(0xFF652C0F) : const Color(0xFFCF7842);
  static Color shubahSurah1(bool isDark) =>
      isDark ? const Color(0xFF90211D) : const Color(0xFF7B1D19);
  static Color shubahSurah2(bool isDark) =>
      isDark ? const Color(0xFF1F0A00) : const Color(0xFFFFFFFF);
  static Color shubahSurah3(bool isDark) =>
      isDark ? const Color(0xFF652C0F) : const Color(0xFFF3C6B0);
  static Color shubahSurah4(bool isDark) =>
      isDark ? const Color(0xFFE0DCDD) : const Color(0xFF231F20);
  static Color shubahSurah5(bool isDark) =>
      isDark ? const Color(0xFFDAAF9D) : const Color(0xFFDAAF9D);
  static Color shubahSurah6(bool isDark) =>
      isDark ? const Color(0xFF90211D) : const Color(0xFFF26F29);
  static Color shubahVerse1(bool isDark) =>
      isDark ? const Color(0xFF90211D) : const Color(0xFF7B1D19);
  static Color shubahVerse2(bool isDark) =>
      isDark ? const Color(0xFF652C0F) : const Color(0xFFF3C6B0);
  static Color shubahVerse3(bool isDark) =>
      isDark ? const Color(0xFFCBD5E0) : const Color(0xFF231F20);
  static Color shubahHizb1(bool isDark) =>
      isDark ? const Color(0xFF652C0F) : const Color(0xFFF3C6B0);
  static Color shubahHizb2(bool isDark) =>
      isDark ? const Color(0xFFF1F8FB) : const Color(0xFF1A202C);
  static Color shubahHizb3(bool isDark) =>
      isDark ? const Color(0xFF90211D) : const Color(0xFF7B1D19);
  static Color shubahHizb4(bool isDark) =>
      isDark ? const Color(0xFF1D1000) : const Color(0xFFFBF8);

  // Warsh Theme Color
  static Color warshDrawer1(bool isDark) =>
      isDark ? const Color(0xFF1E1B1D) : const Color(0xFFF1D4DF);
  static Color warshDrawer2(bool isDark) =>
      isDark ? const Color(0xFFFFF6FA) : const Color(0xFF24000E);
  static Color warshHighlightFull(bool isDark) =>
      isDark ? const Color(0xFF2B5369) : const Color(0xFF10A6DE);
  static Color warshHighlightFaded(bool isDark) =>
      isDark ? const Color(0xFF1A3340) : const Color(0xFFC4E3F1);
  static Color warshBackground(bool isDark) =>
      isDark ? const Color(0xFF121011) : const Color(0xFFFAF6FA);
  static Color warshActivityCardBackground(bool isDark) =>
      isDark ? const Color(0xFF24000E) : const Color(0xFFFAF6FA);
  static Color warshActivityCardBorder(bool isDark) =>
      isDark ? const Color(0xFF391228) : const Color(0xFFECE7EF);
  static Color warshBorder1(bool isDark) =>
      isDark ? const Color(0xFF7D8898) : const Color(0xFF787878);
  static Color warshBorder2(bool isDark) =>
      isDark ? const Color(0xFF8C3565) : const Color(0xFFECAACE);
  static Color warshBorder3(bool isDark) =>
      isDark ? const Color(0xFF133C50) : const Color(0xFFBEBEBE);
  static Color warshBorder4(bool isDark) =>
      isDark ? const Color(0xFF24000E) : const Color(0xFFFFFFFF);
  static Color warshBorder5(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF000000);
  static Color warshBorder6(bool isDark) =>
      isDark ? const Color(0xFF24000E) : const Color(0xFF7E7C7C);
  static Color warshBorder7(bool isDark) =>
      isDark ? const Color(0xFF24000E) : const Color(0xFFA19F9F);
  static Color warshBorder8(bool isDark) =>
      isDark ? const Color(0xFF24000E) : const Color(0xFFFFFFFF);
  static Color warshBorder9(bool isDark) =>
      isDark ? const Color(0xFF9ADEF0) : const Color(0xFFE1E1E1);
  static Color warshBorder10(bool isDark) =>
      isDark ? const Color(0xFF9ADEF0) : const Color(0xFF35B2D4);
  static Color warshBorder11(bool isDark) =>
      isDark ? const Color(0xFF9ADEF0) : const Color(0xFFB27ECEE4);
  static Color warshBorder12(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF723F64);
  static Color warshBorder13(bool isDark) =>
      isDark ? const Color(0x00000000) : const Color(0x00000000);
  static Color warshBorder14(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF000000);
  static Color warshSurah1(bool isDark) =>
      isDark ? const Color(0xFF3E1424) : const Color(0xFFA9B1B3);
  static Color warshSurah2(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF000000);
  static Color warshSurah3(bool isDark) =>
      isDark ? const Color(0xFF8C3565) : const Color(0xFFCC92AF);
  static Color warshSurah4(bool isDark) =>
      isDark ? const Color(0xFF8C3565) : const Color(0xFF9ADEF0);
  static Color warshSurah5(bool isDark) =>
      isDark ? const Color(0xFFF5A8D6) : const Color(0xFFF5A8D6);
  static Color warshSurah6(bool isDark) =>
      isDark ? const Color(0xFF3E1424) : const Color(0xFFC3E8F6);
  static Color warshSurah7(bool isDark) =>
      isDark ? const Color(0xFFFFFFFF) : const Color(0xFF231F20);
  static Color warshSurah8(bool isDark) =>
      isDark ? const Color(0xFF0D0D0D) : const Color(0xFFFFFFFF);
  static Color warshSurah9(bool isDark) =>
      isDark ? const Color(0xFFC4E3F1) : const Color(0xFF10A6DE);
  static Color warshSurah10(bool isDark) =>
      isDark ? const Color(0xFF9ADEF0) : const Color(0xFF9ADEF0);
  static Color warshSurah11(bool isDark) =>
      isDark ? const Color(0xFF9ADEF0) : const Color(0xFFFFFFFF);
  static Color warshSurah12(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF231F20);
  static Color warshSurah13(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF424244);
  static Color warshSurah14(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF5C5C5F);
  static Color warshSurah15(bool isDark) =>
      isDark ? const Color(0xFF3E1424) : const Color(0xFFD7C9D0);
  static Color warshSurah16(bool isDark) =>
      isDark ? const Color(0xFF3E1424) : const Color(0xFFFCE7EF);
  static Color warshSurah17(bool isDark) =>
      isDark ? const Color(0x00000000) : const Color(0x00000000);
  static Color warshVerse1(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF000000);
  static Color warshVerse2(bool isDark) =>
      isDark ? const Color(0xFF8C3565) : const Color(0xFFE3AAC6);
  static Color warshVerse3(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF000000);
  static Color warshVerse4(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF268DBD);
  static Color warshVerse5(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFF231F20);
  static Color warshVerse6(bool isDark) =>
      isDark ? const Color(0xFFEDF2F7) : const Color(0xFFFFFFFF);
  static Color warshHizb1(bool isDark) =>
      isDark ? const Color(0xFF8C3565) : const Color(0xFFCA8BAA);
  static Color warshHizb2(bool isDark) =>
      isDark ? const Color(0xFFF1F8FB) : const Color(0xFF1A202C);
  static Color warshHizb3(bool isDark) =>
      isDark ? const Color(0xFF24000E) : const Color(0xFFFAF6FA);
  static Color warshHizb4(bool isDark) =>
      isDark ? const Color(0xFF1A202C) : const Color(0xFFFFFFFF);
}
