// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get title => 'Mushafi';

  @override
  String get titleContinue => 'Continue';

  @override
  String get sentenceReadListenQuranOnboardingTitle => 'Read and listen Quran';

  @override
  String get sentenceReadListenQuranOnboardingDescription =>
      'You can read the Quran, seamlessly picking up where you left off, and listen to your favourite Qari.';

  @override
  String get sentenceMemorizingOnboardingTitle => 'Quran memorizing partner';

  @override
  String get sentenceMemorizingOnboardingDescription =>
      'We help you to check your memorization anywhere without the Internet connection.';

  @override
  String get sentenceVoiceSearchOnboardingTitle => 'Easily find any verse';

  @override
  String get sentenceVoiceSearchOnboardingDescription =>
      'Search for any verse by reciting a part of it to see the whole verse and its location in the Quran.';

  @override
  String get sentenceQuranLoadingError => 'Error while loading the data';

  @override
  String get sentenceVoiceSearchHoldMicrophoneToBegin =>
      'Hold the microphone to begin reciting';

  @override
  String get sentenceVoiceSearchBackToHome => 'Back to home';

  @override
  String get titleRecent => 'Recent';

  @override
  String get titleReading => 'Reading';

  @override
  String get titleListening => 'Listening';

  @override
  String get titleMemorizing => 'Memorizing';

  @override
  String get sentenceEmptyRecentActivityTitle =>
      'You have no any recent activity';

  @override
  String get sentenceEmptyRecentActivityDescription =>
      'Get started by creating a new activity';

  @override
  String get sentenceEmptyReadingActivityTitle =>
      'You can track your daily Quran reading';

  @override
  String get sentenceEmptyReadingActivityDescription =>
      'Get started by creating the \'Reading\' activity';

  @override
  String get sentenceEmptyListeningActivityTitle =>
      'You can create your listening track';

  @override
  String get sentenceEmptyListeningActivityDescription =>
      'Get started by creating the \'Listening\' activity';

  @override
  String get sentenceEmptyMemorizingActivityTitle =>
      'You can check your memorization with AI';

  @override
  String get sentenceEmptyMemorizingActivityDescription =>
      'Get started by creating the \'Memorizing\' activity';

  @override
  String get sentenceMemorizationTemplateTitle =>
      'Check your memorization with AI';

  @override
  String get titleMemorizationTemplateSubtitle => 'Memorizing';

  @override
  String get sentenceAIRecitationTemplateTitle =>
      'Improve your recitation with AI';

  @override
  String get sentenceAIRecitationTemplateSubtitle => 'Recite with AI';

  @override
  String get sentenceAlKahfReadingTemplateTitle => 'Read Al-Kahf every Friday';

  @override
  String get titleAlKahfReadingTemplateSubtitle => 'Habitual Reading';

  @override
  String get sentenceTryNowTemplateButton => 'Try now';

  @override
  String get sentenceReadNowTemplateButton => 'Read now';

  @override
  String get titleMyActivities => 'My Activities';

  @override
  String get sentenceDoMoreWithMushafi => 'Do more with Mushafi';

  @override
  String get sentenceRecentPages => 'Recent pages';

  @override
  String get titleSurah => 'Surah';

  @override
  String get titleJuz => 'Juz\'';

  @override
  String get sentenceBrowseMushaf => 'Browse mushaf';

  @override
  String get sentenceEmptyRecentPageTitle => 'You have no any recent page';

  @override
  String get sentenceEmptyRecentPageDescription =>
      'Swipe to the left or search your surah or juz\'';
}
