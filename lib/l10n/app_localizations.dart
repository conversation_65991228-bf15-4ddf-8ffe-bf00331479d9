import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Mushafi'**
  String get title;

  /// No description provided for @titleContinue.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get titleContinue;

  /// No description provided for @sentenceReadListenQuranOnboardingTitle.
  ///
  /// In en, this message translates to:
  /// **'Read and listen Quran'**
  String get sentenceReadListenQuranOnboardingTitle;

  /// No description provided for @sentenceReadListenQuranOnboardingDescription.
  ///
  /// In en, this message translates to:
  /// **'You can read the Quran, seamlessly picking up where you left off, and listen to your favourite Qari.'**
  String get sentenceReadListenQuranOnboardingDescription;

  /// No description provided for @sentenceMemorizingOnboardingTitle.
  ///
  /// In en, this message translates to:
  /// **'Quran memorizing partner'**
  String get sentenceMemorizingOnboardingTitle;

  /// No description provided for @sentenceMemorizingOnboardingDescription.
  ///
  /// In en, this message translates to:
  /// **'We help you to check your memorization anywhere without the Internet connection.'**
  String get sentenceMemorizingOnboardingDescription;

  /// No description provided for @sentenceVoiceSearchOnboardingTitle.
  ///
  /// In en, this message translates to:
  /// **'Easily find any verse'**
  String get sentenceVoiceSearchOnboardingTitle;

  /// No description provided for @sentenceVoiceSearchOnboardingDescription.
  ///
  /// In en, this message translates to:
  /// **'Search for any verse by reciting a part of it to see the whole verse and its location in the Quran.'**
  String get sentenceVoiceSearchOnboardingDescription;

  /// No description provided for @sentenceQuranLoadingError.
  ///
  /// In en, this message translates to:
  /// **'Error while loading the data'**
  String get sentenceQuranLoadingError;

  /// No description provided for @sentenceVoiceSearchHoldMicrophoneToBegin.
  ///
  /// In en, this message translates to:
  /// **'Hold the microphone to begin reciting'**
  String get sentenceVoiceSearchHoldMicrophoneToBegin;

  /// No description provided for @sentenceVoiceSearchBackToHome.
  ///
  /// In en, this message translates to:
  /// **'Back to home'**
  String get sentenceVoiceSearchBackToHome;

  /// No description provided for @titleRecent.
  ///
  /// In en, this message translates to:
  /// **'Recent'**
  String get titleRecent;

  /// No description provided for @titleReading.
  ///
  /// In en, this message translates to:
  /// **'Reading'**
  String get titleReading;

  /// No description provided for @titleListening.
  ///
  /// In en, this message translates to:
  /// **'Listening'**
  String get titleListening;

  /// No description provided for @titleMemorizing.
  ///
  /// In en, this message translates to:
  /// **'Memorizing'**
  String get titleMemorizing;

  /// No description provided for @sentenceEmptyRecentActivityTitle.
  ///
  /// In en, this message translates to:
  /// **'You have no any recent activity'**
  String get sentenceEmptyRecentActivityTitle;

  /// No description provided for @sentenceEmptyRecentActivityDescription.
  ///
  /// In en, this message translates to:
  /// **'Get started by creating a new activity'**
  String get sentenceEmptyRecentActivityDescription;

  /// No description provided for @sentenceEmptyReadingActivityTitle.
  ///
  /// In en, this message translates to:
  /// **'You can track your daily Quran reading'**
  String get sentenceEmptyReadingActivityTitle;

  /// No description provided for @sentenceEmptyReadingActivityDescription.
  ///
  /// In en, this message translates to:
  /// **'Get started by creating the \'Reading\' activity'**
  String get sentenceEmptyReadingActivityDescription;

  /// No description provided for @sentenceEmptyListeningActivityTitle.
  ///
  /// In en, this message translates to:
  /// **'You can create your listening track'**
  String get sentenceEmptyListeningActivityTitle;

  /// No description provided for @sentenceEmptyListeningActivityDescription.
  ///
  /// In en, this message translates to:
  /// **'Get started by creating the \'Listening\' activity'**
  String get sentenceEmptyListeningActivityDescription;

  /// No description provided for @sentenceEmptyMemorizingActivityTitle.
  ///
  /// In en, this message translates to:
  /// **'You can check your memorization with AI'**
  String get sentenceEmptyMemorizingActivityTitle;

  /// No description provided for @sentenceEmptyMemorizingActivityDescription.
  ///
  /// In en, this message translates to:
  /// **'Get started by creating the \'Memorizing\' activity'**
  String get sentenceEmptyMemorizingActivityDescription;

  /// No description provided for @sentenceMemorizationTemplateTitle.
  ///
  /// In en, this message translates to:
  /// **'Check your memorization with AI'**
  String get sentenceMemorizationTemplateTitle;

  /// No description provided for @titleMemorizationTemplateSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Memorizing'**
  String get titleMemorizationTemplateSubtitle;

  /// No description provided for @sentenceAIRecitationTemplateTitle.
  ///
  /// In en, this message translates to:
  /// **'Improve your recitation with AI'**
  String get sentenceAIRecitationTemplateTitle;

  /// No description provided for @sentenceAIRecitationTemplateSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Recite with AI'**
  String get sentenceAIRecitationTemplateSubtitle;

  /// No description provided for @sentenceAlKahfReadingTemplateTitle.
  ///
  /// In en, this message translates to:
  /// **'Read Al-Kahf every Friday'**
  String get sentenceAlKahfReadingTemplateTitle;

  /// No description provided for @titleAlKahfReadingTemplateSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Habitual Reading'**
  String get titleAlKahfReadingTemplateSubtitle;

  /// No description provided for @sentenceTryNowTemplateButton.
  ///
  /// In en, this message translates to:
  /// **'Try now'**
  String get sentenceTryNowTemplateButton;

  /// No description provided for @sentenceReadNowTemplateButton.
  ///
  /// In en, this message translates to:
  /// **'Read now'**
  String get sentenceReadNowTemplateButton;

  /// No description provided for @titleMyActivities.
  ///
  /// In en, this message translates to:
  /// **'My Activities'**
  String get titleMyActivities;

  /// No description provided for @sentenceDoMoreWithMushafi.
  ///
  /// In en, this message translates to:
  /// **'Do more with Mushafi'**
  String get sentenceDoMoreWithMushafi;

  /// No description provided for @sentenceRecentPages.
  ///
  /// In en, this message translates to:
  /// **'Recent pages'**
  String get sentenceRecentPages;

  /// No description provided for @titleSurah.
  ///
  /// In en, this message translates to:
  /// **'Surah'**
  String get titleSurah;

  /// No description provided for @titleJuz.
  ///
  /// In en, this message translates to:
  /// **'Juz\''**
  String get titleJuz;

  /// No description provided for @sentenceBrowseMushaf.
  ///
  /// In en, this message translates to:
  /// **'Browse mushaf'**
  String get sentenceBrowseMushaf;

  /// No description provided for @sentenceEmptyRecentPageTitle.
  ///
  /// In en, this message translates to:
  /// **'You have no any recent page'**
  String get sentenceEmptyRecentPageTitle;

  /// No description provided for @sentenceEmptyRecentPageDescription.
  ///
  /// In en, this message translates to:
  /// **'Swipe to the left or search your surah or juz\''**
  String get sentenceEmptyRecentPageDescription;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
