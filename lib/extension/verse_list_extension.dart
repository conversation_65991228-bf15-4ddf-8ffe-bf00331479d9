import 'package:mushafi/extension/integer_extension.dart';
import 'package:mushafi/extension/string_extension.dart';
import 'package:mushafi/generated/flatbuffer_flatbuffer_generated.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/utils/constants.dart';

extension VerseListExtension on List<Verse> {
  QuranWord _buildSurahQuranWord(int surahId, int lineNumber) {
    const id = -2;
    final codeV1 = "${surahId.threeDigitsFormat()}surah";
    return QuranWord(
      id: id,
      verseKey: '$surahId',
      lineNumber: lineNumber,
      codeV1: codeV1,
      textUthmani: codeV1,
      textImlaei: codeV1,
      isVerseNumber: false,
    );
  }

  QuranWord _buildBismillahQuranWord({
    required int surahId,
    required String codeV1,
    required String textUthmani,
    required String textImlaei,
    required int lineNumber,
  }) {
    const id = -1;

    return QuranWord(
      id: id,
      verseKey: '$surahId:0',
      lineNumber: lineNumber,
      codeV1: codeV1,
      textUthmani: textUthmani,
      textImlaei: textImlaei,
      isVerseNumber: false,
    );
  }

  QuranWord _buildQuranWord(
    Word word,
    String verseKey,
    bool isVerseNumber, {
    String? newCodeV1,
    int? newLineNumber,
  }) {
    final lineNumber = newLineNumber ?? word.lineNumber;
    final codeV1 = newCodeV1 ?? word.codeV1 ?? '';
    return QuranWord(
      id: word.wordId,
      verseKey: verseKey,
      lineNumber: lineNumber,
      codeV1: codeV1,
      textUthmani: word.textUthmani ?? '',
      textImlaei: word.textImlaei ?? '',
      isVerseNumber: isVerseNumber,
    );
  }

  List<QuranWord> convertToQuranWords({
    List<Word>? bismillahWordList,
    Verse? previousVerse,
    Verse? nextVerse,
  }) {
    final previousSurahId = previousVerse?.surahId ?? 0;
    int curSurahId = previousSurahId;

    bool addSurahHeader = true;
    bool newSurah = false;

    final quranWords = <QuranWord>[];
    for (var (verseIndex, curVerse) in indexed) {
      if (curSurahId != curVerse.surahId) {
        newSurah = true;
      }

      final curWords = curVerse.words ?? [];
      for (var (wordIndex, curWord) in curWords.indexed) {
        if (newSurah) {
          // No Basmallah for Al-Fatihah & At-Tawbah
          if (curVerse.surahId == 1 || curVerse.surahId == 9) {
            final surahId = curVerse.surahId;
            final lineNumber = curWord.lineNumber - 1;

            final surahWord = _buildSurahQuranWord(surahId, lineNumber);
            quranWords.add(surahWord);
          } else {
            final surahId = curVerse.surahId;

            if (verseIndex == 0 && wordIndex == 0 && curWord.lineNumber == 2) {
              addSurahHeader = false;
            }

            if (addSurahHeader) {
              int surahLineNumber = curWord.lineNumber - 2;
              final surahWord = _buildSurahQuranWord(surahId, surahLineNumber);
              quranWords.add(surahWord);
            }

            int bismillahLineNumber = curWord.lineNumber - 1;
            if (bismillahWordList != null) {
              for (final bismillahWord in bismillahWordList) {
                final word = _buildBismillahQuranWord(
                  surahId: surahId,
                  codeV1: bismillahWord.codeV1 ?? '',
                  textUthmani: bismillahWord.textUthmani ?? '',
                  textImlaei: bismillahWord.textImlaei ?? '',
                  lineNumber: bismillahLineNumber,
                );
                quranWords.add(word);
              }
            } else {
              final word = _buildBismillahQuranWord(
                surahId: surahId,
                codeV1:
                    bismillahCodeV1, // todo word by word instead for zoom in level 2 in page 2
                textUthmani: '',
                textImlaei: '',
                lineNumber: bismillahLineNumber,
              );
              quranWords.add(word);
            }
          }

          curSurahId = curVerse.surahId;
          addSurahHeader = true;
          newSurah = false;
        }

        final verseKey = curVerse.verseKey ?? '';
        bool isVerseNumber = (curWord.codeV1?.isNotEmpty == true)
            ? wordIndex == curWords.length - 1
            : curWord.textUthmani?.isArabicNumericsOnly() == true;

        QuranWord quranWord;
        if (curWord.split) {
          int newCodeV1Index = 0;
          String newCodeV1 = curWord.codeV1?[newCodeV1Index] ?? '';
          int newLineNumber = curWord.lineNumber;
          quranWord = _buildQuranWord(
            curWord,
            verseKey,
            false,
            newCodeV1: newCodeV1,
            newLineNumber: newLineNumber,
          );
          quranWords.add(quranWord);

          newCodeV1Index = (curWord.codeV1?.length ?? 0) - 1;
          newCodeV1 = curWord.codeV1?[newCodeV1Index] ?? '';
          newLineNumber = curWord.lineNumber + 1;
          quranWord = _buildQuranWord(
            curWord,
            verseKey,
            isVerseNumber,
            newCodeV1: newCodeV1,
            newLineNumber: newLineNumber,
          );
          quranWords.add(quranWord);
        } else {
          quranWord = _buildQuranWord(curWord, verseKey, isVerseNumber);
          quranWords.add(quranWord);
        }
      }
    }

    // to add the surah header to the bottom
    if (nextVerse != null) {
      final lastVerse = last;
      final lastWord = lastVerse.words?.last;
      if (lastWord != null &&
          lastWord.lineNumber == 14 &&
          lastVerse.surahId != nextVerse.surahId) {
        int surahLineNumber = 15;
        final surahWord =
            _buildSurahQuranWord(nextVerse.surahId, surahLineNumber);
        quranWords.add(surahWord);
      }
    }

    return quranWords;
  }
}
