import 'package:mushafi/presentation/model/quran/quran_line.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';

extension QuranWordExtension on List<QuranWord> {
  List<QuranLine> convertToQuranLines() {
    final lines = <QuranLine>[];

    int curLineNumber = 1;
    List<QuranWord> curWords = <QuranWord>[];
    String curText = '';
    LineType curLineType = LineType.quran;

    forEach((word) {
      if (word.lineNumber != curLineNumber) {
        lines.add(QuranLine(words: curWords, text: curText, type: curLineType));

        curLineNumber = word.lineNumber;
        curWords = [word];
        curText = word.getText();
      } else {
        curWords.add(word);
        curText += word.getText();
      }

      curLineType = switch (word.id) {
        -2 => LineType.surahHeader,
        -1 => LineType.bismillah,
        _ => LineType.quran,
      };
    });

    lines.add(QuranLine(words: curWords, text: curText, type: curLineType));

    return lines;
  }
}
