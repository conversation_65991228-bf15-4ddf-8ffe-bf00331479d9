import 'package:mushafi/extension/string_extension.dart';

extension VerseKeyParser on String {
  int parseSurahNumber() {
    var surahNumber = -1;

    if (contains(":")) {
      final valueList = split(":");
      if (valueList.length == 2 &&
          valueList.every((element) => element.isNumericsOnly())) {
        surahNumber = int.parse(valueList[0]);
      } else {
        surahNumber = -1;
      }
    } else if (isNumericsOnly()) {
      surahNumber = int.parse(this);
    } else {
      surahNumber = -1;
    }

    return surahNumber;
  }

  int parseVerseNumber() {
    var verseNumber = -1;

    final valueList = split(":");
    if (valueList.length == 2 &&
        valueList.every((element) => element.isNumericsOnly())) {
      verseNumber = int.parse(valueList[1]);
    } else {
      verseNumber = -1;
    }

    return verseNumber;
  }
}
