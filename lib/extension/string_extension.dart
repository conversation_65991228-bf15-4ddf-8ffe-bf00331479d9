extension StringExtension on String {
  /// check if the string contains only numbers
  bool isNumericsOnly() {
    RegExp numeric = RegExp(r'^-?[0-9]+$');
    return numeric.hasMatch(this);
  }

  bool isArabicNumericsOnly() {
    RegExp arabicNumeric = RegExp(r'^-?[٠-٩]+$');
    return arabicNumeric.hasMatch(this);
  }

  String get toCapitalized =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';

  String get toTitleCase => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized)
      .join(' ');

  String get camelToSnakeCase => replaceAllMapped(
        RegExp(r'([a-z])([A-Z])'),
        (Match match) => '${match.group(1)}_${match.group(2)}',
      ).toLowerCase();
}
