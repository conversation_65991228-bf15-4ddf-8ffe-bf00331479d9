import 'dart:ui';

import 'package:flutter_svg/flutter_svg.dart';

class DefaultFrameColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFFFFFFF);
  final Color rawColor2 = const Color(0xFFCBD5E0);

  final Color color1;
  final Color color2;

  DefaultFrameColorMapper(this.color1, this.color2);

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;

    return color;
  }
}

class MedinaFrameColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFB8DBA9);
  final Color rawColor2 = const Color(0xFF231F20);
  final Color rawColor3 = const Color(0xFF5B9144);
  final Color rawColor4 = const Color(0xFFAFD8A3);
  final Color rawColor5 = const Color(0xFFE79EC5);
  final Color rawColor6 = const Color(0xFFAD5F84);
  final Color rawColor7 = const Color(0xFFFFFFFF);
  final Color rawColor8 = const Color(0xFFE89DC4);
  final Color rawColor9 = const Color(0xFF4C8C40);
  final Color rawColor10 = const Color(0xFFE79EC5);
  final Color rawColor11 = const Color(0xFF753E5A);
  final Color rawColor12 = const Color(0xFF4C4C4E);
  final Color rawColor13 = const Color(0x00000000);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;
  final Color color7;
  final Color color8;
  final Color color9;
  final Color color10;
  final Color color11;
  final Color color12;
  final Color color13;

  MedinaFrameColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
    this.color7,
    this.color8,
    this.color9,
    this.color10,
    this.color11,
    this.color12,
    this.color13,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;
    if (color == rawColor7) return color7;
    if (color == rawColor8) return color8;
    if (color == rawColor9) return color9;
    if (color == rawColor10) return color10;
    if (color == rawColor11) return color11;
    if (color == rawColor12) return color12;
    if (color == rawColor13) return color13;

    return color;
  }
}

class OldMedinaFrameColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFF6F2DC);
  final Color rawColor2 = const Color(0xFF917237);
  final Color rawColor3 = const Color(0xFF358B42);
  final Color rawColor4 = const Color(0xFFD5E8C6);
  final Color rawColor5 = const Color(0xFF917337);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;

  OldMedinaFrameColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;

    return color;
  }
}

class QaloonFrameColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFF000000);
  final Color rawColor2 = const Color(0xFFB4ECFB);
  final Color rawColor3 = const Color(0xFFBFBFBF);
  final Color rawColor4 = const Color(0xFF0D9FDB);
  final Color rawColor5 = const Color(0xFFFFFFFF);
  final Color rawColor6 = const Color(0xFFE11482);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;

  QaloonFrameColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;

    return color;
  }
}

class MediumMedinaFrameColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFCE9E6E);
  final Color rawColor2 = const Color(0xFF000000);
  final Color rawColor3 = const Color(0xFFFFFFFF);
  final Color rawColor4 = const Color(0xFFA28CAC);
  final Color rawColor5 = const Color(0xFF9B9693);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;

  MediumMedinaFrameColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;

    return color;
  }
}

class ShubahFrameColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFF231F20);
  final Color rawColor2 = const Color(0xFFFFFFFF);
  final Color rawColor3 = const Color(0xFF781F19);
  final Color rawColor4 = const Color(0xFFDAB49F);
  final Color rawColor5 = const Color(0xFFF0C78F);
  final Color rawColor6 = const Color(0xFFCF7842);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;

  ShubahFrameColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;

    return color;
  }
}

class WarshFrameColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFF787878);
  final Color rawColor2 = const Color(0xFFECAACE);
  final Color rawColor3 = const Color(0xFFBEBEBE);
  final Color rawColor4 = const Color(0xFFFFFFFF);
  final Color rawColor5 = const Color(0xFF000000);
  final Color rawColor6 = const Color(0xFF7E7C7C);
  final Color rawColor7 = const Color(0xFFA19F9F);
  final Color rawColor8 = const Color(0xFFFFFFFF);
  final Color rawColor9 = const Color(0xFFE1E1E1);
  final Color rawColor10 = const Color(0xFF35B2D4);
  final Color rawColor11 = const Color(0xFFB27ECEE4);
  final Color rawColor12 = const Color(0xFF723F64);
  final Color rawColor13 = const Color(0x00000000);
  final Color rawColor14 = const Color(0xFF000000);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;
  final Color color7;
  final Color color8;
  final Color color9;
  final Color color10;
  final Color color11;
  final Color color12;
  final Color color13;
  final Color color14;

  WarshFrameColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
    this.color7,
    this.color8,
    this.color9,
    this.color10,
    this.color11,
    this.color12,
    this.color13,
    this.color14,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;
    if (color == rawColor7) return color7;
    if (color == rawColor8) return color8;
    if (color == rawColor9) return color9;
    if (color == rawColor10) return color10;
    if (color == rawColor11) return color11;
    if (color == rawColor12) return color12;
    if (color == rawColor13) return color13;
    if (color == rawColor14) return color14;

    return color;
  }
}

class DefaultVerseColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFCBD5E0);
  final Color rawColor2 = const Color(0xFFFFFFFF);
  final Color rawColor3 = const Color(0xFF000000);

  final Color color1;
  final Color color2;
  final Color color3;

  DefaultVerseColorMapper(
    this.color1,
    this.color2,
    this.color3,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;

    return color;
  }
}

class MedinaVerseColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFDC8EBB);
  final Color rawColor2 = const Color(0xFFCBE5BE);
  final Color rawColor3 = const Color(0xFF000000);

  final Color color1;
  final Color color2;
  final Color color3;

  MedinaVerseColorMapper(
    this.color1,
    this.color2,
    this.color3,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;

    return color;
  }
}

class OldMedinaVerseColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFF7EB480);
  final Color rawColor2 = const Color(0xFF000000);
  final Color rawColor3 = const Color(0xFF96793E);

  final Color color1;
  final Color color2;
  final Color color3;

  OldMedinaVerseColorMapper(
    this.color1,
    this.color2,
    this.color3,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;

    return color;
  }
}

class QaloonVerseColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFF000000);
  final Color rawColor2 = const Color(0xFF288EBE);
  final Color rawColor3 = const Color(0xFF231F20);
  final Color rawColor4 = const Color(0xFFFFFFFF);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;

  QaloonVerseColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;

    return color;
  }
}

class MediumMedinaVerseColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFD1D3D4);
  final Color rawColor2 = const Color(0xFFF6ECE1);
  final Color rawColor3 = const Color(0xFF000000);

  final Color color1;
  final Color color2;
  final Color color3;

  MediumMedinaVerseColorMapper(
    this.color1,
    this.color2,
    this.color3,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;

    return color;
  }
}

class ShubahVerseColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFF7B1D19);
  final Color rawColor2 = const Color(0xFFF3C6B0);
  final Color rawColor3 = const Color(0xFF231F20);

  final Color color1;
  final Color color2;
  final Color color3;

  ShubahVerseColorMapper(
    this.color1,
    this.color2,
    this.color3,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;

    return color;
  }
}

class WarshVerseColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFF000000);
  final Color rawColor2 = const Color(0xFFE3AAC6);
  final Color rawColor3 = const Color(0xFF000000);
  final Color rawColor4 = const Color(0xFF268DBD);
  final Color rawColor5 = const Color(0xFF231F20);
  final Color rawColor6 = const Color(0xFFFFFFFF);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;

  WarshVerseColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;

    return color;
  }
}

class DefaultSurahColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFFFFFFF);
  final Color rawColor2 = const Color(0xFF000000);
  final Color rawColor3 = const Color(0xFF7D8898);

  final Color color1;
  final Color color2;
  final Color color3;

  DefaultSurahColorMapper(
    this.color1,
    this.color2,
    this.color3,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;

    return color;
  }
}

class MedinaSurahColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFCBE5BE);
  final Color rawColor2 = const Color(0xFF000000);
  final Color rawColor3 = const Color(0xFF419644);
  final Color rawColor4 = const Color(0xFFCBDFC0);
  final Color rawColor5 = const Color(0xFFFFFFFF);
  final Color rawColor6 = const Color(0xFFDC8EBB);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;

  MedinaSurahColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;

    return color;
  }
}

class OldMedinaSurahColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0x00000000);
  final Color rawColor2 = const Color(0xFF000000);
  final Color rawColor3 = const Color(0xFF231F20);
  final Color rawColor4 = const Color(0xFF95793E);
  final Color rawColor5 = const Color(0xFF020202);
  final Color rawColor6 = const Color(0xFFFFFFFF);
  final Color rawColor7 = const Color(0xFFD5E9C9);
  final Color rawColor8 = const Color(0xFF967A42);
  final Color rawColor9 = const Color(0xFFFBF7DF);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;
  final Color color7;
  final Color color8;
  final Color color9;

  OldMedinaSurahColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
    this.color7,
    this.color8,
    this.color9,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;
    if (color == rawColor7) return color7;
    if (color == rawColor8) return color8;
    if (color == rawColor9) return color9;

    return color;
  }
}

class QaloonSurahColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFFFFFFF);
  final Color rawColor2 = const Color(0xFF231F20);
  final Color rawColor3 = const Color(0xFF3C96B8);
  final Color rawColor4 = const Color(0x00000000);
  final Color rawColor5 = const Color(0xFFECA7CA);
  final Color rawColor6 = const Color(0xFF000000);
  final Color rawColor7 = const Color(0xFFC3E8F6);
  final Color rawColor8 = const Color(0xFFADB9BF);
  final Color rawColor9 = const Color(0xFF84979F);
  final Color rawColor10 = const Color(0xFF10A6DE);
  final Color rawColor11 = const Color(0xFFDD4A9A);
  final Color rawColor12 = const Color(0xFFF0C3DB);
  final Color rawColor13 = const Color(0xFFFFFFFF);
  final Color rawColor14 = const Color(0xFF231F20);
  final Color rawColor15 = const Color(0xFF424244);
  final Color rawColor16 = const Color(0xFF5C5C5F);
  final Color rawColor17 = const Color(0xFFBCE4F3);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;
  final Color color7;
  final Color color8;
  final Color color9;
  final Color color10;
  final Color color11;
  final Color color12;
  final Color color13;
  final Color color14;
  final Color color15;
  final Color color16;
  final Color color17;

  QaloonSurahColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
    this.color7,
    this.color8,
    this.color9,
    this.color10,
    this.color11,
    this.color12,
    this.color13,
    this.color14,
    this.color15,
    this.color16,
    this.color17,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;
    if (color == rawColor7) return color7;
    if (color == rawColor8) return color8;
    if (color == rawColor9) return color9;
    if (color == rawColor10) return color10;
    if (color == rawColor11) return color11;
    if (color == rawColor12) return color12;
    if (color == rawColor13) return color13;
    if (color == rawColor14) return color14;
    if (color == rawColor15) return color15;
    if (color == rawColor16) return color16;
    if (color == rawColor17) return color17;

    return color;
  }
}

class MediumMedinaSurahColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFF6ECE1);
  final Color rawColor2 = const Color(0xFFD09E6D);
  final Color rawColor3 = const Color(0xFFFFFFFF);
  final Color rawColor4 = const Color(0xFFD1D3D4);
  final Color rawColor5 = const Color(0xFF000000);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;

  MediumMedinaSurahColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;

    return color;
  }
}

class ShubahSurahColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFF7B1D19);
  final Color rawColor2 = const Color(0xFFFFFFFF);
  final Color rawColor3 = const Color(0xFFF3C6B0);
  final Color rawColor4 = const Color(0xFF231F20);
  final Color rawColor5 = const Color(0xFFDAAF9D);
  final Color rawColor6 = const Color(0xFFF26F29);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;

  ShubahSurahColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;

    return color;
  }
}

class WarshSurahColorMapper implements ColorMapper {
  final Color rawColor1 = const Color(0xFFA9B1B3);
  final Color rawColor2 = const Color(0xFF000000);
  final Color rawColor3 = const Color(0xFFCC92AF);
  final Color rawColor4 = const Color(0xFF9ADEF0);
  final Color rawColor5 = const Color(0xFFF5A8D6);
  final Color rawColor6 = const Color(0xFFC3E8F6);
  final Color rawColor7 = const Color(0xFF231F20);
  final Color rawColor8 = const Color(0xFFFFFFFF);
  final Color rawColor9 = const Color(0xFF10A6DE);
  final Color rawColor10 = const Color(0xFF9ADEF0);
  final Color rawColor11 = const Color(0xFFFFFFFF);
  final Color rawColor12 = const Color(0xFF231F20);
  final Color rawColor13 = const Color(0xFF424244);
  final Color rawColor14 = const Color(0xFF5C5C5F);
  final Color rawColor15 = const Color(0xFFD7C9D0);
  final Color rawColor16 = const Color(0xFFFCE7EF);
  final Color rawColor17 = const Color(0x00000000);

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color4;
  final Color color5;
  final Color color6;
  final Color color7;
  final Color color8;
  final Color color9;
  final Color color10;
  final Color color11;
  final Color color12;
  final Color color13;
  final Color color14;
  final Color color15;
  final Color color16;
  final Color color17;

  WarshSurahColorMapper(
    this.color1,
    this.color2,
    this.color3,
    this.color4,
    this.color5,
    this.color6,
    this.color7,
    this.color8,
    this.color9,
    this.color10,
    this.color11,
    this.color12,
    this.color13,
    this.color14,
    this.color15,
    this.color16,
    this.color17,
  );

  @override
  Color substitute(
      String? id, String elementName, String attributeName, Color color) {
    if (color == rawColor1) return color1;
    if (color == rawColor2) return color2;
    if (color == rawColor3) return color3;
    if (color == rawColor4) return color4;
    if (color == rawColor5) return color5;
    if (color == rawColor6) return color6;
    if (color == rawColor7) return color7;
    if (color == rawColor8) return color8;
    if (color == rawColor9) return color9;
    if (color == rawColor10) return color10;
    if (color == rawColor11) return color11;
    if (color == rawColor12) return color12;
    if (color == rawColor13) return color13;
    if (color == rawColor14) return color14;
    if (color == rawColor15) return color15;
    if (color == rawColor16) return color16;
    if (color == rawColor17) return color17;

    return color;
  }
}
