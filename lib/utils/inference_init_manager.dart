import 'dart:io';

import 'package:async/async.dart';
import 'package:mushafi/data/source/minio_data_manager.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:path_provider/path_provider.dart';

class InferenceInitManager {
  static final InferenceInitManager _instance =
      InferenceInitManager._internal();

  static InferenceInitManager get instance => _instance;

  CancelableOperation<void>? _currentInitOperation;
  bool _isInit = false;
  int _initTryCount = 0;
  bool _success = true;

  InferenceInitManager._internal();

  bool get isInit => _isInit;

  bool get success => _success;

  Future<bool> waitForInitOperation() async {
    if (_currentInitOperation != null) {
      await _currentInitOperation?.valueOrCancellation();
      return success;
    }

    return true;
  }

  Future<bool> runInitOperation(
    InferenceModel model,
    Future<void> Function() fetchOperation,
  ) async {
    if (isInit) {
      return false;
    }

    _isInit = true;
    _success = true;

    try {
      _currentInitOperation?.cancel();
      _currentInitOperation = CancelableOperation.fromFuture(
        Future.doWhile(() async {
          if (navigatorKey.currentContext != null) {
            _success = await InferencePipeline.getInstance().initialize(model);
            if (!success) {
              await deleteModels();
              await fetchOperation();
            }

            _initTryCount++;
            return !success && _initTryCount < 3;
          }

          return false;
        }),
      );

      await _currentInitOperation?.valueOrCancellation();
    } catch (e, st) {
      talker.handle(e, st);
      _success = false;
    } finally {
      _isInit = false;
      _currentInitOperation = null;
    }

    return success;
  }

  Future<void> deleteModels() async {
    final dataDirectory = await getApplicationDocumentsDirectory();
    final modelsDirectory =
        Directory('${dataDirectory.path}/${MinioDataManager.modelsPath}');

    if (await modelsDirectory.exists()) {
      await modelsDirectory.delete(recursive: true);
    }
  }
}
