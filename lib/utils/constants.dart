import 'package:mushafi/presentation/model/track/picker_state.dart';
import 'package:mushafi/presentation/model/track/track_range_portion.dart';

const quranTextSize = 56.0;

const mushafiWebUrl = "https://mushafi.app/";
const mushafiArabicWebUrl = "https://mushafi.app/ar/";
const mushafiPlayStoreUrl =
    "https://play.google.com/store/apps/details?id=my.huda.quranapp";
const mushafiAppStoreUrl = "https://apps.apple.com/app/id6739578303";
const String mushafiInstagramUrl = "https://www.instagram.com/mushafi_app/";
const String mushafiFacebookUrl = "https://www.facebook.com/mushafi.quranapp";
const String mushafiYoutubeUrl = "https://www.youtube.com/@MushafiApp";
const String mushafiTiktokUrl = "https://www.tiktok.com/@mushafi.app";

const String bismillahCodeV1 = "ﭑﭒﭓﭔ";
const int juzCharCode = 64568;

final numbersRegex = RegExp(r"[0-9\u0660-\u0669\u06F0-\u06F9]+");

const pageNumberKey = "initialPageNumberKey";
const favoriteRecitersKey = "favoriteRecitersKey";
const defaultReciterKey = "defaultReciterKey";
const defaultTranslationKey = "defaultTranslationKey";
const juzNameScriptKey = "juzNameScriptKey";
const selectedModelKey = "selectedModelKey";
const zoomStateKey = "zoomStateKey";
const showIncompatibleCardKey = "showIncompatibleCardKey";
const currentModelVersionNumberKey = "currentModelVersionNumberKey";
const modelCompatibleKey = "modelCompatibleKey";
const latestTestedModelVersionKey = "latestTestedModelVersionKey";
const assetPackRemovedKey = "assetPackRemovedKey";

const List<double> grayScaleColorFilter = [
  0.2126,
  0.7152,
  0.0722,
  0,
  0,
  0.2126,
  0.7152,
  0.0722,
  0,
  0,
  0.2126,
  0.7152,
  0.0722,
  0,
  0,
  0,
  0,
  0,
  1,
  0,
];

final startSurahPickerId =
    "${TrackRangePortion.range.name}_${PickerState.start.name}_surah";
final startVersePickerId =
    "${TrackRangePortion.range.name}_${PickerState.start.name}_verse";
final endSurahPickerId =
    "${TrackRangePortion.range.name}_${PickerState.end.name}_surah";
final endVersePickerId =
    "${TrackRangePortion.range.name}_${PickerState.end.name}_verse";
final startPagePickerId =
    "${TrackRangePortion.page.name}_${PickerState.start.name}_page";
final endPagePickerId =
    "${TrackRangePortion.page.name}_${PickerState.end.name}_page";
final juzPickerId = TrackRangePortion.juz.name;
final reciterPickerId = "reciter";
final dayPickerId = "day";

final pageTextImagesPath = 'page_text_images';
