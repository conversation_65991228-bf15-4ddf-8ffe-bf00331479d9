import 'package:flutter/services.dart';

class MethodChannelHelper {
  static const _systemChannel = MethodChannel('my.huda.quranapp/systemChannel');
  static const _isMobileMethodName = 'isMobile';
  static const _isRunningFromTestLabMethodName = "isRunningFromFirebaseTestLab";

  static Future<bool> isMobile() async {
    try {
      return await _systemChannel.invokeMethod<bool>(_isMobileMethodName) ??
          true;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> isRunningFromFirebaseTestLab() async {
    try {
      return await _systemChannel
              .invokeMethod(_isRunningFromTestLabMethodName) ??
          false;
    } catch (e) {
      return false;
    }
  }
}
