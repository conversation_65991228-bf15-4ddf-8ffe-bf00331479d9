import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';

final class Array extends Struct {
  external Pointer<Int32> data;

  @Int32()
  external int size;
}

typedef NewSelfNativeFunction = Long Function(
  Pointer<Utf8>,
  Pointer<Utf8>,
  Pointer<Utf8>,
  Int,
  Pointer<Utf8>,
);
typedef NewSelfFunction = int Function(
  Pointer<Utf8>,
  Pointer<Utf8>,
  Pointer<Utf8>,
  int,
  Pointer<Utf8>,
);

typedef DeleteSelfNativeFunction = Void Function(Long);
typedef DeleteSelfFunction = void Function(int);

typedef OnListeningCallbackFunction = Void Function(Bool);
typedef OnSpeechCallbackFunction = Void Function(Bool);
typedef OnTranscriptionCallbackFunction = Void Function(Pointer<Utf8>);
typedef OnAlignmentResultCallbackFunction = Void Function(Int, Int, Bool, Bool);
typedef OnMatchedVersesCallbackFunction = Void Function(Array);

typedef AlignNativeFunction = Bool Function(
    Long,
    Int,
    Int,
    Pointer<NativeFunction<OnListeningCallbackFunction>>,
    Pointer<NativeFunction<OnSpeechCallbackFunction>>,
    Pointer<NativeFunction<OnTranscriptionCallbackFunction>>,
    Pointer<NativeFunction<OnAlignmentResultCallbackFunction>>);
typedef AlignFunction = bool Function(
    int,
    int,
    int,
    Pointer<NativeFunction<OnListeningCallbackFunction>>,
    Pointer<NativeFunction<OnSpeechCallbackFunction>>,
    Pointer<NativeFunction<OnTranscriptionCallbackFunction>>,
    Pointer<NativeFunction<OnAlignmentResultCallbackFunction>>);

typedef SetAlignerCursorNativeFunction = Bool Function(Long, Int, Int);
typedef SetAlignerCursorFunction = bool Function(int, int, int);

typedef RecognizeVerseNativeFunction = Bool Function(
  Long,
  Pointer<NativeFunction<OnListeningCallbackFunction>>,
  Pointer<NativeFunction<OnSpeechCallbackFunction>>,
  Pointer<NativeFunction<OnTranscriptionCallbackFunction>>,
  Pointer<NativeFunction<OnMatchedVersesCallbackFunction>>,
);
typedef RecognizeVerseFunction = bool Function(
  int,
  Pointer<NativeFunction<OnListeningCallbackFunction>>,
  Pointer<NativeFunction<OnSpeechCallbackFunction>>,
  Pointer<NativeFunction<OnTranscriptionCallbackFunction>>,
  Pointer<NativeFunction<OnMatchedVersesCallbackFunction>>,
);

typedef StopNativeFunction = Bool Function(Long);
typedef StopFunction = bool Function(int);

typedef ResetNativeFunction = Bool Function(Long);
typedef ResetFunction = bool Function(int);

typedef TestCompatibilityNativeFunction = Float Function(Long);
typedef TestCompatibilityFunction = double Function(int);

final dylib = Platform.isAndroid
    ? DynamicLibrary.open('libnativelib.so')
    : DynamicLibrary.process();

class InferencePipeline {
  int _selfAddress = 0;
  bool isInitialized = false;
  bool isInitializing = false;
  bool isRunning = false;

  static InferencePipeline? _instance;

  static InferencePipeline getInstance() {
    _instance ??= InferencePipeline._internal();
    return _instance!;
  }

  InferencePipeline._internal();

  Future<bool> initialize(InferenceModel model) async {
    if (isInitialized) return true;
    bool success = true;

    try {
      isInitializing = true;

      final newSelf = dylib
          .lookupFunction<NewSelfNativeFunction, NewSelfFunction>('newSelf');
      _selfAddress = newSelf(
        (await model.vadPath)!.toNativeUtf8(),
        (await model.path)!.toNativeUtf8(),
        (await model.encodedVersesPath)!.toNativeUtf8(),
        model.layerStride,
        (await model.condPath)!.toNativeUtf8(),
      );

      success = _selfAddress != 0;
      isInitialized = success;
    } catch (e, st) {
      talker.handle(e, st);
      success = false;
    } finally {
      isInitializing = false;
    }

    return success;
  }

  void delete() {
    isRunning = false;
    final deleteSelf =
        dylib.lookupFunction<DeleteSelfNativeFunction, DeleteSelfFunction>(
            'deleteSelf');
    deleteSelf(_selfAddress);
    _selfAddress = 0;
    isInitialized = false;
    _instance = null;
  }

  bool align({
    required int startAyahId,
    required int endAyahId,
    required Function(bool isListening) onListening,
    required Function(bool isSpeech) onSpeech,
    required Function(String transcription) onTranscription,
    required Function(
            int ayahId, int wordIndex, bool isCorrect, bool isComplete)
        onAlignmentResult,
  }) {
    if (isInitializing) return false;

    final align =
        dylib.lookupFunction<AlignNativeFunction, AlignFunction>("align");

    final onListeningCallback =
        NativeCallable<OnListeningCallbackFunction>.listener(
            (bool isListening) {
      onListening(isListening);
    });
    final onSpeechCallback =
        NativeCallable<OnSpeechCallbackFunction>.listener((bool isSpeech) {
      onSpeech(isSpeech);
    });
    final onTranscriptionCallback =
        NativeCallable<OnTranscriptionCallbackFunction>.listener(
            (Pointer<Utf8> transcription) {
      try {
        final dartString = transcription.toDartString();
        if (dartString.isNotEmpty) {
          onTranscription(transcription.toDartString());
        }
      } catch (e, st) {
        talker.handle(e, st);
        onTranscription("");
      } finally {
        malloc.free(transcription);
      }
    });
    final onAlignmentResultCallback =
        NativeCallable<OnAlignmentResultCallbackFunction>.listener(
            (int ayahId, int wordIndex, bool isCorrect, bool isComplete) {
      onAlignmentResult(ayahId, wordIndex, isCorrect, isComplete);
    });

    final success = align(
      _selfAddress,
      startAyahId,
      endAyahId,
      onListeningCallback.nativeFunction,
      onSpeechCallback.nativeFunction,
      onTranscriptionCallback.nativeFunction,
      onAlignmentResultCallback.nativeFunction,
    );

    if (success) {
      isRunning = true;
    }

    return success;
  }

  bool setAlignerCursor({required int ayahId, required int wordIndex}) {
    final setAlignerCursor = dylib.lookupFunction<
        SetAlignerCursorNativeFunction,
        SetAlignerCursorFunction>("setAlignerCursor");
    return setAlignerCursor(_selfAddress, ayahId, wordIndex);
  }

  bool recognizeVerse({
    required Function(bool isListening) onListening,
    required Function(bool isSpeech) onSpeech,
    required Function(String transcription) onTranscription,
    required Function(List<int> matchedVerses) onMatchedVerses,
  }) {
    if (isInitializing) return false;

    final recognizeVerse = dylib.lookupFunction<RecognizeVerseNativeFunction,
        RecognizeVerseFunction>("recognizeVerse");

    final onListeningCallback =
        NativeCallable<OnListeningCallbackFunction>.listener(
            (bool isListening) {
      onListening(isListening);
    });
    final onSpeechCallback =
        NativeCallable<OnSpeechCallbackFunction>.listener((bool isSpeech) {
      onSpeech(isSpeech);
    });
    final onTranscriptionCallback =
        NativeCallable<OnTranscriptionCallbackFunction>.listener(
            (Pointer<Utf8> transcription) {
      try {
        final dartString = transcription.toDartString();
        if (dartString.isNotEmpty) {
          onTranscription(transcription.toDartString());
        }
      } catch (e, st) {
        talker.handle(e, st);
        onTranscription("");
      } finally {
        malloc.free(transcription);
      }
    });
    final onMatchedVersesCallback =
        NativeCallable<OnMatchedVersesCallbackFunction>.listener(
            (Array matchedVerses) {
      try {
        final list = matchedVerses.data.asTypedList(matchedVerses.size);
        onMatchedVerses(list.toList());
      } catch (e, st) {
        talker.handle(e, st);
        onMatchedVerses([]);
      } finally {
        malloc.free(matchedVerses.data);
      }
    });

    final success = recognizeVerse(
      _selfAddress,
      onListeningCallback.nativeFunction,
      onSpeechCallback.nativeFunction,
      onTranscriptionCallback.nativeFunction,
      onMatchedVersesCallback.nativeFunction,
    );

    if (success) {
      isRunning = true;
    }

    return success;
  }

  bool stop() {
    isRunning = false;
    final stop = dylib.lookupFunction<StopNativeFunction, StopFunction>("stop");
    return stop(_selfAddress);
  }

  bool reset() {
    final reset =
        dylib.lookupFunction<ResetNativeFunction, ResetFunction>("reset");
    return reset(_selfAddress);
  }

  double testCompatibility() {
    final testCompatibility = dylib.lookupFunction<
        TestCompatibilityNativeFunction,
        TestCompatibilityFunction>("testCompatibility");
    return testCompatibility(_selfAddress);
  }
}
