import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mushafi/data/source/preference_storage.dart';

/// Service class for detecting and managing user currency preferences
class CurrencyService {
  CurrencyService._(); // Private constructor to prevent instantiation

  /// Saves the user's currency preference
  ///
  /// Stores the user's manually selected currency for future app launches.
  ///
  /// [currency] - The 3-letter currency code to save
  static Future<void> saveUserCurrency(String currency) async {
    await PreferenceStorage.saveCurrency(currency);
  }

  /// Gets the user's preferred currency with intelligent fallback
  ///
  /// Checks for saved user preference first, then detects via IP if needed.
  /// Once detected, saves the result to avoid repeated API calls.
  ///
  /// Returns: Future<String> - The user's currency (saved preference or detected)
  static Future<String?> getUserCurrency() async {
    // Check if user has any saved preference
    final savedCurrency = PreferenceStorage.getCurrency();

    if (savedCurrency != null) {
      // User has a saved preference (could be USD, AED, etc.), use it
      // return savedCurrency;
    }

    // No saved preference, detect from location and save it
    final detectedCurrency = await detectCurrency();
    if (detectedCurrency != null) {
      await PreferenceStorage.saveCurrency(detectedCurrency);
      return detectedCurrency;
    }

    return null;
  }

  /// Detects the user's currency based on their IP geolocation
  ///
  /// Uses HTTPS APIs (ipapi.co, ipwhois.io) to determine the user's current
  /// location and returns the local currency. This is more accurate than device
  /// locale which often defaults to USD regardless of actual location.
  ///
  /// Falls back to secondary API if primary fails due to network issues.
  ///
  /// Returns: Future<String> - The detected 3-letter currency code
  ///
  /// Example:
  /// - User in UAE → Returns "AED"
  /// - User in Saudi → Returns "SAR"
  /// - API failure → Returns null (fallback)
  static Future<String?> detectCurrency() async {
    // Try ipapi.co first (simplest, returns plain text)
    try {
      final response = await http
          .get(
            Uri.parse('https://ipapi.co/currency/'),
          )
          .timeout(const Duration(seconds: 5));
      
      if (response.statusCode == 200 && response.body.isNotEmpty) {
        return response.body.trim().toUpperCase();
      }
    } catch (e) {
      // Continue to fallback
    }
    
    // Fall back to ipwhois.app (always try if ipapi.co didn't succeed)
    try {
      final response = await http
          .get(
            Uri.parse('https://ipwhois.app/json/'),
          )
          .timeout(const Duration(seconds: 5));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        // ipwhois.app returns currency_code at root level, not nested
        final currency = data['currency_code'] as String?;

        if (currency != null && currency.isNotEmpty) {
          return currency.toUpperCase();
        }
      }
    } catch (e) {
      // Continue to return null
    }

    return null;
  }
}
