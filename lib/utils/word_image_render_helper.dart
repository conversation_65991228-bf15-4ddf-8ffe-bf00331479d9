import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/services.dart';
import 'package:mushafi/generated/assets.dart';

class WordImageRenderHelper {
  WordImageRenderHelper._();

  static const preRenderedWordIdToPageNumber = {
    64313: 302,
    37719: 154,
    65677: 161,
    78568: 166,
    46575: 566,
  };

  // key == asset path
  static Map<String, Image?> wordImages = {};

  static List<int> get preRenderedWordIds =>
      preRenderedWordIdToPageNumber.keys.toList();

  static List<int> get preRenderedWordPageNumbers =>
      preRenderedWordIdToPageNumber.values.toList();

  static Image? getWord(String assetPath) {
    return wordImages[assetPath];
  }

  static Future<Image> loadWord(String assetPath) async {
    final image = wordImages[assetPath];
    if (image != null) return image;

    final ByteData data = await rootBundle.load(assetPath);
    final Uint8List bytes = data.buffer.asUint8List();
    final Completer<Image> completer = Completer();
    decodeImageFromList(bytes, (Image img) {
      completer.complete(img);
    });

    wordImages[assetPath] = await completer.future;
    return wordImages[assetPath]!;
  }

  // some words are not rendered on ios because of Flutter framework issue. so use pre-rendered word image from android
  static Future<void> preloadWordImages() async {
    if (Platform.isIOS) {
      // word in surah Al-Kahf ayah 80
      await WordImageRenderHelper.loadWord(Assets.images64313);
      // word in surah Al-A'raf ayah 35
      await WordImageRenderHelper.loadWord(Assets.images37719);
      // word in surah Al-A'raf ayah 84
      await WordImageRenderHelper.loadWord(Assets.images65677);
      // word in surah Al-A'raf ayah 135
      await WordImageRenderHelper.loadWord(Assets.images78568);
      // word in surah Al-Qalam ayah 47
      await WordImageRenderHelper.loadWord(Assets.images46575);
    }
  }
}
