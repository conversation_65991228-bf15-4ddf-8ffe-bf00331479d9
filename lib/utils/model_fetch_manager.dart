import 'package:async/async.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/provider/repository_provider.dart';

class ModelFetchManager {
  static final ModelFetchManager _instance = ModelFetchManager._internal();

  static ModelFetchManager get instance => _instance;

  CancelableOperation<void>? _currentFetchOperation;
  bool _isFetching = false;
  int _fetchTryCount = 0;

  Function(double percentage)? onDownloading;

  ModelFetchManager._internal();

  bool get isFetching => _isFetching;

  Future<void> waitForFetchOperation() async {
    if (_currentFetchOperation != null) {
      await _currentFetchOperation?.valueOrCancellation();
    }
  }

  Future<bool> runFetchOperation(InferenceModel model) async {
    if (_isFetching) {
      return false; // Return false if fetch is already in progress
    }

    _isFetching = true;
    bool success = true;

    try {
      _currentFetchOperation?.cancel();
      _currentFetchOperation = CancelableOperation.fromFuture(
        Future.doWhile(() async {
          if (navigatorKey.currentContext != null) {
            final container =
                ProviderScope.containerOf(navigatorKey.currentContext!);
            await container
                .read(minioDataRepositoryProvider)
                .fetchInferenceModel(
                  model,
                  onDownloading: onDownloading,
                );
            final modelsExist = await container
                .read(minioDataRepositoryProvider)
                .doModelsExist(model);

            _fetchTryCount++;
            if (_fetchTryCount < 3) {
              await Future.delayed(const Duration(milliseconds: 500));
            }
            return !modelsExist && _fetchTryCount < 3;
          }

          return false;
        }),
      );

      await _currentFetchOperation?.valueOrCancellation();
    } catch (e, st) {
      talker.handle(e, st);
      success = false;
    } finally {
      _isFetching = false;
      _currentFetchOperation = null;
    }

    return success;
  }
}
