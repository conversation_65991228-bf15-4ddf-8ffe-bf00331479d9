import 'package:flutter/widgets.dart';
import 'package:mushafi/main.dart';

class AppLifecycleManager with WidgetsBindingObserver {
  static final AppLifecycleManager _instance = AppLifecycleManager._internal();

  factory AppLifecycleManager() => _instance;

  AppLifecycleManager._internal();

  AppLifecycleState? _currentState;

  AppLifecycleState? get currentState => _currentState;

  void initialize() {
    WidgetsBinding.instance.addObserver(this);
  }

  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _currentState = state;
    // Optionally notify listeners or log the state change
    talker.debug("AppLifecycleState changed to: $state");
  }
}
