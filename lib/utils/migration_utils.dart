import 'dart:io';

import 'package:mushafi/data/source/preference_storage.dart';
import 'package:path_provider/path_provider.dart';

class MigrationUtils {
  MigrationUtils._();

  static Future<void> removeAssetPacks() async {
    if (Platform.isAndroid && !PreferenceStorage.getAssetPackRemoved()) {
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/../files/assetpacks';
      final assetPacksDirectory = Directory(path);
      if (await assetPacksDirectory.exists()) {
        await assetPacksDirectory.delete(recursive: true);
        PreferenceStorage.saveAssetPackRemoved(true);
      }
    }
  }
}
