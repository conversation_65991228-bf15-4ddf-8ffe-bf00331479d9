import 'package:adaptive_theme/adaptive_theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:toastification/toastification.dart';

class CommonUtils {
  CommonUtils._();

  static String humanReadableKBCountBin(double kilobytes) {
    if (kilobytes.abs() < 1) {
      return "${kilobytes.toStringAsFixed(1)} KB";
    }

    const units = ['K', 'M', 'G', 'T', 'P', 'E'];
    var value = kilobytes.abs();
    var unitIndex = 0;

    while (value >= 1024 && unitIndex < units.length - 1) {
      value = value / 1024;
      unitIndex++;
    }

    final result = value * kilobytes.sign;
    return "${result.toStringAsFixed(1)} ${units[unitIndex]}B";
  }

  static ToastificationItem createToast(String message) {
    return toastification.show(
      closeOnClick: true,
      title: Text(message),
      style: ToastificationStyle.simple,
      autoCloseDuration: const Duration(seconds: 3),
      alignment: Alignment.bottomCenter,
    );
  }

  static void showToast(String message) {
    toastification.dismissAll();
    createToast(message);
  }

  static Future<bool> get isDarkMode async {
    final themeMode = await AdaptiveTheme.getThemeMode();
    final isDarkMode = (themeMode != null && !themeMode.isSystem)
        ? (themeMode.isDark)
        : WidgetsBinding.instance.platformDispatcher.platformBrightness ==
            Brightness.dark;
    return isDarkMode;
  }

  static double? calculateMedian(List<double> numbers) {
    if (numbers.isEmpty) {
      return null;
    }

    // Create a copy of the list to avoid modifying the original
    List<double> sortedNumbers = List.from(numbers)..sort();

    int middleIndex = sortedNumbers.length ~/ 2;

    if (sortedNumbers.length.isOdd) {
      // If odd, return the middle element
      return sortedNumbers[middleIndex];
    } else {
      // If even, return the average of the two middle elements
      return (sortedNumbers[middleIndex - 1] + sortedNumbers[middleIndex]) / 2;
    }
  }
}
