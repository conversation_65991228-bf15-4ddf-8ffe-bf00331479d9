import 'dart:ui';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/verse_key_extension.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/utils/app_lifecycle_manager.dart';
import 'package:mushafi/utils/recitation_player.dart';

class RecitationHelper {
  RecitationHelper._();

  static void startTrackRecitation(
    Track track,
    String startVerseKey,
    List<QuranVerse> currentVerseList, {
    required Function(String verseKey) onSetup,
    required Function(
      List<QuranVerse> verseList,
      int reciteVerseCount,
      int milliseconds,
      int index,
    ) onPosition,
    required Function(
      List<QuranVerse> verseList,
      int prevIndex,
      int index,
    ) onIndex,
    required VoidCallback onDownloading,
    required VoidCallback onLoop,
    required VoidCallback onPlaying,
    required VoidCallback onPause,
    required VoidCallback onStop,
    required VoidCallback onNextPager,
  }) async {
    if (currentVerseList.isEmpty) return;

    final container = ProviderScope.containerOf(navigatorKey.currentContext!);

    final surahList =
        await container.read(jsonDataRepositoryProvider).getSurahList();
    final currentReciter =
        (await container.read(audioSegmentDataInitProvider.future))?.data;
    final defaultReciterKey = PreferenceStorage.getDefaultReciterKey();
    final defaultReciter = await container
        .read(minioDataRepositoryProvider)
        .getReciter(defaultReciterKey);

    final reciteVerseCount = track.reciteVerseCount;

    onSetup(startVerseKey);

    RecitationPlayer.play(
      currentVerseList,
      surahList,
      currentReciter ?? defaultReciter,
      reciteVerseCount,
      track.isLoop,
      startVerseKey: startVerseKey,
      onDownloading: onDownloading,
      onPlaying: onPlaying,
      onPause: onPause,
      onStop: onStop,
      onPosition: (verseList, milliseconds, index) {
        onPosition(verseList, reciteVerseCount, milliseconds, index);
      },
      onIndex: (verseList, prevIndex, index) async {
        if (AppLifecycleManager().currentState == AppLifecycleState.detached ||
            AppLifecycleManager().currentState == AppLifecycleState.hidden ||
            AppLifecycleManager().currentState == AppLifecycleState.paused) {
          final prevVerse = verseList.elementAtOrNull(prevIndex);
          final verse = verseList.elementAtOrNull(index);

          if (verseList.isEmpty || prevVerse == null || verse == null) return;

          if (prevVerse.pageId != verse.pageId) {
            // prepare the next page
            final quranPage = await container
                .read(quranRepositoryProvider)
                .getCompleteQuranPage(verse.pageId + 1);
            if (quranPage == null) return;

            final nextVerseList = quranPage.verseList;
            final endVerseKey = track.range.endVerseKey;

            final filteredNextVerseList = nextVerseList
                .takeWhile((verse) =>
                    verse.verseKey.verseKeyInt <= endVerseKey.verseKeyInt)
                .toList();
            RecitationPlayer.addPlaylist(filteredNextVerseList);
          }
        } else {
          onIndex(verseList, prevIndex, index);
        }
      },
      onComplete: () {},
    );
  }

  static void startRecitation(
    List<QuranVerse> currentVerseList, {
    required Function(
      List<QuranVerse> verseList,
      int reciteVerseCount,
      int milliseconds,
      int index,
    ) onPosition,
    required Function(List<QuranVerse> verseList, int prevIndex, int index)
        onIndex,
    required VoidCallback onDownloading,
    required VoidCallback onPlaying,
    required VoidCallback onPause,
    required VoidCallback onStop,
    required VoidCallback onNextPager,
  }) async {
    if (currentVerseList.isEmpty) return;

    final container = ProviderScope.containerOf(navigatorKey.currentContext!);

    final surahList =
        await container.read(jsonDataRepositoryProvider).getSurahList();
    final currentReciter =
        (await container.read(audioSegmentDataInitProvider.future))?.data;
    final defaultReciterKey = PreferenceStorage.getDefaultReciterKey();
    final defaultReciter = await container
        .read(minioDataRepositoryProvider)
        .getReciter(defaultReciterKey);

    RecitationPlayer.play(
      currentVerseList,
      surahList,
      currentReciter ?? defaultReciter,
      1,
      false,
      onDownloading: onDownloading,
      onPosition: (verseList, milliseconds, index) {
        onPosition(verseList, 1, milliseconds, index);
      },
      onIndex: (verseList, prevIndex, index) async {
        if (AppLifecycleManager().currentState == AppLifecycleState.detached ||
            AppLifecycleManager().currentState == AppLifecycleState.hidden ||
            AppLifecycleManager().currentState == AppLifecycleState.paused) {
          final prevVerse = verseList.elementAtOrNull(prevIndex);
          final verse = verseList.elementAtOrNull(index);

          if (verseList.isEmpty || prevVerse == null || verse == null) return;

          if (prevVerse.pageId != verse.pageId) {
            // prepare the next page
            final quranPage = await container
                .read(quranRepositoryProvider)
                .getCompleteQuranPage(verse.pageId + 1);
            if (quranPage == null) return;

            final nextVerseList = quranPage.verseList;
            RecitationPlayer.addPlaylist(nextVerseList);
          }
        } else {
          onIndex(verseList, prevIndex, index);
        }
      },
      onPlaying: onPlaying,
      onPause: onPause,
      onStop: onStop,
      onComplete: () {},
    );
  }
}
