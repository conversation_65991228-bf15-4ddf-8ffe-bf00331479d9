import 'dart:async';
import 'dart:ui';

import 'package:collection/collection.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:mushafi/extension/verse_key_parser.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/recitation/reciter.dart';

class RecitationPlayer {
  RecitationPlayer._();

  static AudioPlayer player = AudioPlayer();

  static StreamSubscription? positionSubscription;
  static StreamSubscription? currentIndexSubscription;
  static StreamSubscription? playerStateSubscription;

  static int retryCount = 0;

  static List<Surah> surahList = [];

  static List<QuranVerse> currentVerseList = [];
  static Reciter? currentReciter;
  static int currentReciteVerseCount = 1;
  static ConcatenatingAudioSource currentPlaylist = ConcatenatingAudioSource(
    children: [],
    useLazyPreparation: true,
  );

  static void reset() {
    currentVerseList = [];
    currentReciter = null;
    currentReciteVerseCount = 1;
    currentPlaylist.clear();
  }

  static Future<void> addPlaylist(
    List<QuranVerse> verseList, {
    VoidCallback? onDownloading,
  }) async {
    if (currentReciter == null) return;
    if (verseList.isEmpty) return;
    if (currentVerseList.contains(verseList.first)) return;

    final sourceList = verseList.mapIndexed((index, verse) async {
      final verseKey = verse.verseKey;
      final audioUrl = getVerseAudioUrl(verseKey, currentReciter!.url);

      final surahNumber = verseKey.parseSurahNumber();
      final verseNumber = verseKey.parseVerseNumber();
      final surahText = surahList[surahNumber - 1].nameComplex;
      final title = (verseNumber == 0)
          ? '$surahText, Bismillah'
          : '$surahText, Ayah $verseNumber';

      final repeatCount = (verseNumber == 0) ? 1 : currentReciteVerseCount;
      final repeatedVerses = List.generate(repeatCount, (index) => verse);
      currentVerseList.addAll(repeatedVerses);

      final cacheAudioSource = LockCachingAudioSource(
        Uri.parse(audioUrl),
        tag: MediaItem(
          id: verseKey,
          title: title,
          artist: currentReciter!.name,
        ),
      );

      if (index == 0) {
        final cacheFile = await cacheAudioSource.cacheFile;
        if (!await cacheFile.exists() || await cacheFile.length() == 0) {
          onDownloading?.call();
        }
      }

      return LoopingAudioSource(
        count: repeatCount,
        child: cacheAudioSource..request(),
      );
    }).toList();

    currentPlaylist.addAll(await Future.wait(sourceList));
  }

  static Future<void> play(
    List<QuranVerse> verseList,
    List<Surah> surahList,
    Reciter reciter,
    int reciteVerseCount,
    bool isLoop, {
    String? startVerseKey,
    required VoidCallback onDownloading,
    required Function(List<QuranVerse> verseList, int milliseconds, int index)
        onPosition,
    required Function(List<QuranVerse> verseList, int prevIndex, int index)
        onIndex,
    required VoidCallback onPlaying,
    required VoidCallback onPause,
    required VoidCallback onStop,
    required Function() onComplete,
  }) async {
    await stop();

    await player.dispose();
    player = AudioPlayer();

    RecitationPlayer.surahList = surahList;

    reset();
    currentReciter = reciter;
    currentReciteVerseCount = reciteVerseCount;
    addPlaylist(verseList, onDownloading: onDownloading);

    bool initiallyPlay = true;

    await positionSubscription?.cancel();
    positionSubscription = player.positionStream.listen((position) {
      onPosition(
          currentVerseList, position.inMilliseconds, player.currentIndex ?? -1);
    });

    await playerStateSubscription?.cancel();
    ProcessingState? lastProcessingState;
    playerStateSubscription = player.playerStateStream.listen((state) {
      if (!state.playing && !initiallyPlay) {
        if (state.processingState == ProcessingState.idle) {
          onStop();
        } else if (state.processingState == ProcessingState.ready) {
          onPause();
        }
      }

      if (state.playing) {
        initiallyPlay = false;
        onPlaying();
      }

      if (lastProcessingState != state.processingState) {
        lastProcessingState = state.processingState;
        if (state.processingState == ProcessingState.completed) {
          onComplete();
        }
      }
    });

    try {
      await player.setLoopMode(isLoop ? LoopMode.all : LoopMode.off);
      await player.setAudioSource(currentPlaylist);

      if (startVerseKey != null) {
        final startIndex = currentVerseList
            .indexWhere((verse) => verse.verseKey == startVerseKey);
        if (startIndex != -1) {
          await player.seek(Duration.zero, index: startIndex);
        }
      }

      await currentIndexSubscription?.cancel();
      int prevIndex = 0;
      currentIndexSubscription = player.currentIndexStream.listen((index) {
        if (index != null) {
          onIndex(currentVerseList, prevIndex, index);
          prevIndex = index;
        }
      });

      await player.play();
      retryCount = 0;
    } catch (e, st) {
      talker.handle(e, st);

      if (retryCount == 3) {
        retryCount = 0;
        return;
      }

      retryCount++;

      await Future.delayed(const Duration(milliseconds: 500));
      play(
        verseList,
        surahList,
        reciter,
        reciteVerseCount,
        isLoop,
        onDownloading: onDownloading,
        onPosition: onPosition,
        onIndex: onIndex,
        onPlaying: onPlaying,
        onPause: onPause,
        onStop: onStop,
        onComplete: onComplete,
      );
    }
  }

  static Future<void> playWord(
    List<Surah> surahList,
    String verseKey,
    int start,
    int end,
    Reciter reciter, {
    required VoidCallback onComplete,
  }) async {
    await stop();

    await player.dispose();
    player = AudioPlayer();

    final audioUrl = getVerseAudioUrl(verseKey, reciter.url);

    final surahNumber = verseKey.parseSurahNumber();
    final verseNumber = verseKey.parseVerseNumber();
    final surahText = surahList[surahNumber - 1].nameComplex;
    final title = (verseNumber == 0)
        ? '$surahText, Bismillah'
        : '$surahText, Ayah $verseNumber';

    final mediaItem = MediaItem(
      id: verseKey,
      title: title,
      artist: reciter.name,
    );

    final audioSource = ClippingAudioSource(
      child: AudioSource.uri(
        Uri.parse(audioUrl),
        tag: mediaItem,
      ),
      start: Duration(milliseconds: start),
      end: Duration(milliseconds: end),
      tag: mediaItem,
    );

    playerStateSubscription = player.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.ready) {
        onComplete();
      }
    });

    try {
      await player.setAudioSource(audioSource);
      await player.play();
      retryCount = 0;
    } catch (e, st) {
      talker.handle(e, st);

      if (retryCount == 3) {
        retryCount = 0;
        return;
      }

      retryCount++;

      await Future.delayed(const Duration(milliseconds: 500));
      playWord(surahList, verseKey, start, end, reciter,
          onComplete: onComplete);
    }
  }

  static Future<void> stop() async {
    await player.stop();
    await positionSubscription?.cancel();
    await currentIndexSubscription?.cancel();
    await playerStateSubscription?.cancel();
  }

  static Future<void> pause() async {
    await player.stop();
  }

  static Future<void> next() async {
    if (player.playing && !player.hasNext) {
      await player.seek(player.duration);
      return;
    }

    await player.seekToNext();
  }

  static String getVerseAudioUrl(String verseKey, String baseUrl) {
    final surahNumber = verseKey.parseSurahNumber();
    final verseNumber = verseKey.parseVerseNumber();
    final endpoint = (verseNumber == 0)
        ? "${1.toString().padLeft(3, '0')}${1.toString().padLeft(3, '0')}.mp3"
        : "${surahNumber.toString().padLeft(3, '0')}${verseNumber.toString().padLeft(3, '0')}.mp3";

    return baseUrl + endpoint;
  }
}
