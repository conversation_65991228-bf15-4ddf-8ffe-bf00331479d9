import 'package:async/async.dart';
import 'package:mushafi/presentation/model/quran/verse_tap_type.dart';

class VerseTapHelper {
  String? _tempVerseKey;
  int _tapCount = 0;
  CancelableOperation<void>? _doubleTapDelayOperation;

  VerseTapType tapping(String? verseKey) {
    if (_doubleTapDelayOperation == null ||
        _doubleTapDelayOperation?.isCompleted == true ||
        _doubleTapDelayOperation?.isCanceled == true) {
      _doubleTapDelayOperation = CancelableOperation.fromFuture(
        Future.delayed(const Duration(milliseconds: 500)).then((_) => reset()),
        onCancel: () {},
      );
    }

    _tapCount++;

    if (_tapCount >= 2 && _tempVerseKey == verseKey) {
      reset();
      return VerseTapType.double;
    }

    _tempVerseKey = verseKey;

    return VerseTapType.single;
  }

  void reset() {
    _doubleTapDelayOperation?.cancel();
    _tempVerseKey = null;
    _tapCount = 0;
  }
}
