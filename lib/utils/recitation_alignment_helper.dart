import 'package:collection/collection.dart';
import 'package:mushafi/extension/date_time_extension.dart';
import 'package:mushafi/presentation/model/quran/alignment_type.dart';
import 'package:mushafi/presentation/model/quran/alignment_word_highlight.dart';
import 'package:mushafi/presentation/model/quran/alignment_word_result.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:uuid/uuid.dart';

class RecitationAlignmentHelper {
  Function(
    AlignmentWordHighlight? highlight,
    bool isResultUpdated, {
    bool isInitial,
  })? onHighlightChange;
  Function(AlignmentWordHighlight? highlight)? onManualHighlight;
  Function(int trackId, int startTime, int endTime)? onPreRestart;

  Track? track;

  Map<int, QuranVerse> alignmentVerseByIdMap = {};
  Map<String, QuranVerse> alignmentVerseByVerseKeyMap = {};

  // key == wordId
  Map<int, AlignmentWordResult> resultMap = {};
  Map<int, AlignmentWordResult> resultMapBeforeUndo = {};

  AlignmentWordHighlight? currentHighlight;

  RecitationAlignmentHelper();

  void setOnHighlightChange(
    Function(AlignmentWordHighlight? highlight, bool isResultUpdated,
            {bool isInitial})?
        onHighlightChange,
  ) {
    this.onHighlightChange = onHighlightChange;
  }

  void setOnManualHighlight(
      Function(AlignmentWordHighlight? highlight)? onManualHighlight) {
    this.onManualHighlight = onManualHighlight;
  }

  void setOnPreRestart(
      Function(int trackId, int startTime, int endTime)? onPreRestart) {
    this.onPreRestart = onPreRestart;
  }

  void setup(
    Track track,
    List<QuranVerse> alignmentVerseList,
    List<AlignmentWordResult> allResultList,
  ) {
    this.track = track;

    for (final verse in alignmentVerseList) {
      alignmentVerseByIdMap[verse.id] = verse;
      alignmentVerseByVerseKeyMap[verse.verseKey] = verse;
    }

    AlignmentWordResult? lastResult = allResultList.lastOrNull;
    List<AlignmentWordResult> filteredResultList = [];

    final nowDateTime = DateTime.now();
    final lastResultDateTime = (lastResult != null)
        ? DateTime.fromMillisecondsSinceEpoch(lastResult.time)
        : nowDateTime;

    if (lastResultDateTime.isSameDay(nowDateTime)) {
      filteredResultList =
          filterResultListByDate(allResultList, lastResultDateTime);
    } else {
      final isLastResultFinished = isFinished(lastResult);
      if (!isLastResultFinished) {
        filteredResultList =
            filterResultListByDate(allResultList, lastResultDateTime);
        filteredResultList = copyResultListForToday(filteredResultList);
      }
    }

    lastResult = filteredResultList.lastOrNull;

    for (final result in filteredResultList) {
      AlignmentWordResult newResult = result;

      final verse = alignmentVerseByVerseKeyMap[result.verseKey];
      if (verse != null && (result.verseId == 0 || result.wordId == 0)) {
        newResult = newResult.copyWith(
            verseId: verse.id, wordId: verse.words[result.wordIndex].id);
      }

      if (verse != null && result.verseKey == lastResult?.verseKey) {
        final lastResultWordIndex = lastResult?.wordIndex ?? -1;
        final lastWordIndexWithoutVerseNumber = verse.words.length - 2;
        if (lastResultWordIndex < lastWordIndexWithoutVerseNumber) {
          newResult = newResult.copyWith(isHidden: true);
        }
      }

      resultMap[newResult.wordId] = newResult;
    }

    lastResult = resultMap.values.lastOrNull;

    if (lastResult != null) {
      final alignmentType = (track.type == TrackType.readingWithAi)
          ? AlignmentType.readingWithAi
          : AlignmentType.memorizing;

      verseLoop:
      for (final verse in alignmentVerseList) {
        final verseCodeV1 = verse.words.map((word) => word.codeV1).join(',');
        final verseNumberWordIndex = verse.words.length - 1;

        for (final (wordIndex, word) in verse.words.indexed) {
          if (lastResult.verseKey == verse.verseKey &&
              lastResult.wordIndex == wordIndex) {
            break verseLoop;
          }

          if (wordIndex == verseNumberWordIndex) continue;

          final existingResult = resultMap[word.id];
          if (existingResult == null) {
            final result = AlignmentWordResult(
              trackId: track.id,
              verseId: verse.id,
              verseKey: verse.verseKey,
              wordId: word.id,
              wordIndex: wordIndex,
              pageNumber: verse.pageId,
              verseCodeV1: verseCodeV1,
              codeV1: word.codeV1,
              type: alignmentType,
              isCorrect: true,
              time: lastResult.time,
            );

            resultMap[word.id] = result;
          }
        }
      }
    }

    QuranVerse highlightedVerse;
    if (lastResult != null) {
      final verse = alignmentVerseByIdMap[lastResult.verseId];

      if (verse != null) {
        highlightedVerse = verse;

        final lastWordIndexWithoutVerseNumber = verse.words.length - 2;
        if (lastResult.wordIndex >= lastWordIndexWithoutVerseNumber) {
          final nextVerse = alignmentVerseByIdMap[verse.id + 1];
          if (nextVerse != null) {
            highlightedVerse = nextVerse;
          }
        }
      } else {
        highlightedVerse = alignmentVerseList.first;
      }
    } else {
      highlightedVerse = alignmentVerseList.first;
    }

    currentHighlight = AlignmentWordHighlight(
      verseId: highlightedVerse.id,
      wordIndex: 0,
      word: highlightedVerse.words.first,
      pageNumber: highlightedVerse.pageId,
      isCorrect: true,
      isComplete: false,
    );

    onHighlightChange?.call(currentHighlight, true, isInitial: true);
  }

  bool isAlignmentEnd() {
    final lastResult = resultMap.values.lastOrNull;
    final isLasResultHighlightedAndCompleted =
        lastResult?.wordId == currentHighlight?.word.id &&
            currentHighlight?.isComplete == true;

    return isLasResultHighlightedAndCompleted && isFinished(lastResult);
  }

  bool isFinished(AlignmentWordResult? lastResult) {
    if (lastResult == null) return true;

    final lastVerse = alignmentVerseByIdMap.values.last;
    final isLastVerse = lastVerse.verseKey == lastResult.verseKey;
    if (!isLastVerse) return false;

    final lastWordIndexWithoutVerseNumber = lastVerse.words.length - 2;
    final isLastWord = lastResult.wordIndex == lastWordIndexWithoutVerseNumber;
    if (!isLastWord) return false;

    return true;
  }

  List<AlignmentWordResult> copyResultListForToday(
      List<AlignmentWordResult> resultList) {
    final nowDate = DateTime.now().toDate();
    return resultList.map((result) {
      return result.copyWith(
        id: const Uuid().v1(),
        time: nowDate.millisecondsSinceEpoch,
      );
    }).toList();
  }

  List<AlignmentWordResult> filterResultListByDate(
    List<AlignmentWordResult> resultList,
    DateTime dateTime,
  ) {
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    return resultList.where((result) {
      final resultDateTime = DateTime.fromMillisecondsSinceEpoch(result.time);
      final resultDate = DateTime(
          resultDateTime.year, resultDateTime.month, resultDateTime.day);
      return resultDate == date;
    }).toList();
  }

  bool addResult(AlignmentWordHighlight highlight, QuranVerse verse) {
    final highlightResultMapId = highlight.word.id;

    final existingResult = resultMap[highlightResultMapId];
    if (existingResult != null) {
      resultMap[highlightResultMapId] = existingResult.copyWith(
        time: DateTime.now().millisecondsSinceEpoch,
        isHidden: false,
      );
      return true;
    }

    final track = this.track;
    if (track == null) return false;

    final trackId = track.id;
    final alignmentType = (track.type == TrackType.readingWithAi)
        ? AlignmentType.readingWithAi
        : AlignmentType.memorizing;

    final word = verse.words[highlight.wordIndex];

    final verseCodeV1 = verse.words.map((word) => word.codeV1).join(',');

    resultMap[highlightResultMapId] = AlignmentWordResult(
      id: const Uuid().v1(),
      trackId: trackId,
      verseId: highlight.verseId,
      verseKey: verse.verseKey,
      wordId: word.id,
      wordIndex: highlight.wordIndex,
      pageNumber: verse.pageId,
      verseCodeV1: verseCodeV1,
      codeV1: word.codeV1,
      type: alignmentType,
      isCorrect: highlight.isCorrect,
      time: DateTime.now().millisecondsSinceEpoch,
    );

    return true;
  }

  AlignmentWordHighlight getNextHighlight(
      AlignmentWordHighlight highlight, QuranVerse verse) {
    final lastWordIndexWithoutVerseNumber = verse.words.length - 2;
    if (highlight.wordIndex < lastWordIndexWithoutVerseNumber) {
      final nextWordIndex = highlight.wordIndex + 1;
      return AlignmentWordHighlight(
        verseId: highlight.verseId,
        wordIndex: nextWordIndex,
        word: verse.words[nextWordIndex],
        pageNumber: verse.pageId,
        isCorrect: true,
        isComplete: false,
      );
    } else {
      final nextVerseId = highlight.verseId + 1;
      final nextVerse = alignmentVerseByIdMap[nextVerseId];

      if (nextVerse == null) {
        return highlight.copyWith(isComplete: true);
      }

      return AlignmentWordHighlight(
        verseId: nextVerseId,
        wordIndex: 0,
        word: nextVerse.words.first,
        pageNumber: nextVerse.pageId,
        isCorrect: true,
        isComplete: false,
      );
    }
  }

  void updateCurrentHighlight(AlignmentWordHighlight? highlight) {
    if (highlight == null) return;
    if (currentHighlight == highlight) return;

    bool isResultUpdated = false;

    if (highlight.isCorrect && highlight.isComplete) {
      final verse = alignmentVerseByIdMap[highlight.verseId];
      if (verse == null) return;

      isResultUpdated = addResult(highlight, verse);

      final nextHighlight = getNextHighlight(highlight, verse);
      currentHighlight = nextHighlight;
    } else {
      currentHighlight = highlight;
    }

    onHighlightChange?.call(currentHighlight, isResultUpdated);
  }

  void revealWord() {
    final highlight = currentHighlight;
    if (highlight == null) return;

    final verse = alignmentVerseByIdMap[highlight.verseId];
    if (verse == null) return;

    final incorrectHighlight = highlight.copyWith(
      isCorrect: false,
      isComplete: true,
    );

    final isResultUpdated = addResult(incorrectHighlight, verse);

    final nextHighlight = getNextHighlight(highlight, verse);
    currentHighlight = nextHighlight;

    onHighlightChange?.call(currentHighlight, isResultUpdated);
    onManualHighlight?.call(currentHighlight);
  }

  void revealVerse() {
    final highlight = currentHighlight;
    if (highlight == null) return;

    final verse = alignmentVerseByIdMap[highlight.verseId];
    if (verse == null) return;

    final lastWordIndexWithoutVerseNumber = verse.words.length - 2;

    bool isResultUpdated = false;
    AlignmentWordHighlight? lastIncorrectHighlight;

    for (int index = highlight.wordIndex;
        index <= lastWordIndexWithoutVerseNumber;
        index++) {
      final incorrectHighlight = AlignmentWordHighlight(
        verseId: highlight.verseId,
        wordIndex: index,
        word: verse.words[index],
        pageNumber: verse.pageId,
        isCorrect: false,
        isComplete: true,
      );

      final resultAdded = addResult(incorrectHighlight, verse);
      if (resultAdded) {
        isResultUpdated = true;
      }

      if (index == lastWordIndexWithoutVerseNumber) {
        lastIncorrectHighlight = incorrectHighlight;
      }
    }

    if (lastIncorrectHighlight == null) return;

    final nextHighlight = getNextHighlight(lastIncorrectHighlight, verse);
    currentHighlight = nextHighlight;

    onHighlightChange?.call(currentHighlight, isResultUpdated);
    onManualHighlight?.call(currentHighlight);
  }

  void restart() {
    if (track != null && resultMap.isNotEmpty) {
      final startTime = resultMap.values
          .reduce((cur, next) => (cur.time < next.time) ? cur : next)
          .time;
      final endTime = resultMap.values
          .reduce((cur, next) => (cur.time > next.time) ? cur : next)
          .time;
      onPreRestart?.call(track!.id, startTime, endTime);
    }

    resultMapBeforeUndo = Map.fromEntries(
        resultMap.entries.map((entry) => MapEntry<int, AlignmentWordResult>(
              entry.key,
              AlignmentWordResult(
                id: entry.value.id,
                trackId: entry.value.trackId,
                verseId: entry.value.verseId,
                verseKey: entry.value.verseKey,
                wordId: entry.value.wordId,
                wordIndex: entry.value.wordIndex,
                pageNumber: entry.value.pageNumber,
                verseCodeV1: entry.value.verseCodeV1,
                codeV1: entry.value.codeV1,
                type: entry.value.type,
                isCorrect: entry.value.isCorrect,
                time: entry.value.time,
                isHidden: entry.value.isHidden,
              ),
            )));
    resultMap = {};

    final firstVerse = alignmentVerseByIdMap.values.first;
    currentHighlight = AlignmentWordHighlight(
      verseId: firstVerse.id,
      wordIndex: 0,
      word: firstVerse.words.first,
      pageNumber: firstVerse.pageId,
      isCorrect: true,
      isComplete: false,
    );

    onHighlightChange?.call(currentHighlight, true);
    onManualHighlight?.call(currentHighlight);
  }

  void undoWord() {
    final lastResultEntry =
        resultMap.entries.lastWhereOrNull((entry) => !entry.value.isHidden);
    if (lastResultEntry == null) return;

    final result = lastResultEntry.value;

    final verse = alignmentVerseByIdMap[result.verseId];
    if (verse == null) return;

    resultMap[lastResultEntry.key] = result.copyWith(isHidden: true);

    currentHighlight = AlignmentWordHighlight(
      verseId: result.verseId,
      wordIndex: result.wordIndex,
      word: verse.words[result.wordIndex],
      pageNumber: verse.pageId,
      isCorrect: true,
      isComplete: false,
    );

    onHighlightChange?.call(currentHighlight, true);
    onManualHighlight?.call(currentHighlight);
  }

  bool undoToVerse(String verseKey) {
    final resultEntryList = resultMap.entries.toList();

    final List<MapEntry<int, AlignmentWordResult>> entryList = [];
    int firstTargetIndex = -1;

    for (final (index, entry) in resultEntryList.indexed) {
      if (entry.value.verseKey == verseKey && !entry.value.isHidden) {
        entryList.add(entry);
        if (firstTargetIndex == -1) {
          firstTargetIndex = index;
        }
      }
    }

    if (entryList.isEmpty || firstTargetIndex == -1) return false;

    final firstTargetResult = entryList.first.value;
    final firstTargetVerse = alignmentVerseByIdMap[firstTargetResult.verseId];
    if (firstTargetVerse == null) return false;

    resultMapBeforeUndo = Map.fromEntries(
        resultMap.entries.map((entry) => MapEntry<int, AlignmentWordResult>(
              entry.key,
              AlignmentWordResult(
                id: entry.value.id,
                trackId: entry.value.trackId,
                verseId: entry.value.verseId,
                verseKey: entry.value.verseKey,
                wordId: entry.value.wordId,
                wordIndex: entry.value.wordIndex,
                pageNumber: entry.value.pageNumber,
                verseCodeV1: entry.value.verseCodeV1,
                codeV1: entry.value.codeV1,
                type: entry.value.type,
                isCorrect: entry.value.isCorrect,
                time: entry.value.time,
                isHidden: entry.value.isHidden,
              ),
            )));

    final lastResultIndex = resultEntryList.length - 1;

    for (int i = firstTargetIndex; i <= lastResultIndex; i++) {
      final entry = resultEntryList[i];
      resultMap[entry.key] = entry.value.copyWith(isHidden: true);
    }

    currentHighlight = AlignmentWordHighlight(
      verseId: firstTargetResult.verseId,
      wordIndex: firstTargetResult.wordIndex,
      word: firstTargetVerse.words[firstTargetResult.wordIndex],
      pageNumber: firstTargetVerse.pageId,
      isCorrect: true,
      isComplete: false,
    );

    onHighlightChange?.call(currentHighlight, true);
    onManualHighlight?.call(currentHighlight);

    return true;
  }

  void revertUndo() {
    resultMap = Map.fromEntries(resultMapBeforeUndo.entries
        .map((entry) => MapEntry<int, AlignmentWordResult>(
              entry.key,
              AlignmentWordResult(
                id: entry.value.id,
                trackId: entry.value.trackId,
                verseId: entry.value.verseId,
                verseKey: entry.value.verseKey,
                wordId: entry.value.wordId,
                wordIndex: entry.value.wordIndex,
                pageNumber: entry.value.pageNumber,
                verseCodeV1: entry.value.verseCodeV1,
                codeV1: entry.value.codeV1,
                type: entry.value.type,
                isCorrect: entry.value.isCorrect,
                time: entry.value.time,
                isHidden: entry.value.isHidden,
              ),
            )));

    final lastShownResult =
        resultMap.values.lastWhereOrNull((result) => !result.isHidden);
    if (lastShownResult == null) return;

    final lastShownVerse = alignmentVerseByIdMap[lastShownResult.verseId];
    if (lastShownVerse == null) return;

    final lastResultHighlight = AlignmentWordHighlight(
      verseId: lastShownResult.verseId,
      wordIndex: lastShownResult.wordIndex,
      word: lastShownVerse.words[lastShownResult.wordIndex],
      pageNumber: lastShownVerse.pageId,
      isCorrect: true,
      isComplete: false,
    );
    final nextHighlight = getNextHighlight(lastResultHighlight, lastShownVerse);
    currentHighlight = nextHighlight;

    onHighlightChange?.call(currentHighlight, true);
    onManualHighlight?.call(currentHighlight);
  }

  bool isInRange(String verseKey) {
    return alignmentVerseByVerseKeyMap.containsKey(verseKey);
  }

  bool isInResult(int wordId) {
    return resultMap.containsKey(wordId);
  }

  void reset() {
    track = null;
    alignmentVerseByIdMap = {};
    alignmentVerseByVerseKeyMap = {};
    resultMap = {};
    currentHighlight = null;
  }

  Map<String, dynamic> toJson() {
    return {
      'track': track?.toJson(),
      'alignmentVerseByIdMap': alignmentVerseByIdMap.map(
        (key, value) => MapEntry(key.toString(), value.toJson()),
      ),
      'alignmentVerseByVerseKeyMap': alignmentVerseByVerseKeyMap.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'resultMap': resultMap.map(
        (key, value) => MapEntry(key.toString(), value.toJson()),
      ),
      'currentHighlight': currentHighlight?.toJson(),
    };
  }

  factory RecitationAlignmentHelper.fromJson(Map<String, dynamic> json) {
    final helper = RecitationAlignmentHelper();

    if (json['track'] != null) {
      helper.track = Track.fromJson(json['track'] as Map<String, dynamic>);
    }

    if (json['alignmentVerseByIdMap'] != null) {
      helper.alignmentVerseByIdMap =
          (json['alignmentVerseByIdMap'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(
          int.parse(key),
          QuranVerse.fromJson(value as Map<String, dynamic>),
        ),
      );
    }

    if (json['alignmentVerseByVerseKeyMap'] != null) {
      helper.alignmentVerseByVerseKeyMap =
          (json['alignmentVerseByVerseKeyMap'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(
          key,
          QuranVerse.fromJson(value as Map<String, dynamic>),
        ),
      );
    }

    if (json['resultMap'] != null) {
      helper.resultMap = (json['resultMap'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(
          int.parse(key),
          AlignmentWordResult.fromJson(value as Map<String, dynamic>),
        ),
      );
    }

    if (json['currentHighlight'] != null) {
      helper.currentHighlight = AlignmentWordHighlight.fromJson(
          json['currentHighlight'] as Map<String, dynamic>);
    }

    return helper;
  }
}
