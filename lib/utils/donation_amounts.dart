import 'package:intl/intl.dart';

/// Static donation amounts optimized for different currencies and regions
///
/// **Source:** <PERSON>'s rough estimates based on general knowledge of purchasing power
/// and psychological pricing principles. These amounts are NOT scientifically researched
/// or tied to actual meal prices.
///
/// Each currency has 5 donation tiers designed to be:
/// - Psychologically appealing (clean round numbers)
/// - Culturally appropriate (roughly reflects local purchasing power)
/// - Accessible (low entry point to encourage participation)
/// - Scalable (reasonable progression from small to generous amounts)
///
/// **Maintenance:** Review annually and update based on user feedback, inflation,
/// or market research. Some currencies marked as "(volatile)" may need more frequent
/// updates due to economic instability. Only includes Stripe-supported currencies.
/// These are starting estimates that should be refined over time.
class DonationAmounts {
  /// Currency-optimized donation amounts with 5 tiers per currency
  static const Map<String, List<int>> currencyAmounts = {
    // Gulf States (High income regions - <PERSON>'s estimates)
    'AED': [10, 25, 50, 100, 200], // UAE Dirham
    'SAR': [10, 20, 50, 100, 200], // Saudi Riyal
    'QAR': [10, 25, 50, 100, 200], // Qatari Riyal
    'KWD': [1, 2, 5, 10, 20], // Kuwaiti Dinar
    'BHD': [1, 2, 5, 10, 20], // Bahraini Dinar
    'OMR': [1, 2, 5, 10, 20], // Omani Rial

    // Middle Income Countries (Claude's estimates)
    'TRY': [100, 200, 500, 1000, 2000], // Turkish Lira
    'EGP': [50, 100, 250, 500, 1000], // Egyptian Pound
    'MAD': [30, 60, 150, 300, 600], // Moroccan Dirham
    'TND': [10, 20, 50, 100, 200], // Tunisian Dinar
    'JOD': [2, 5, 10, 20, 40], // Jordanian Dinar
    'MYR': [10, 20, 50, 100, 200], // Malaysian Ringgit
    'LBP': [
      50000,
      100000,
      250000,
      500000,
      1000000
    ], // Lebanese Pound (volatile)
    'IQD': [5000, 10000, 25000, 50000, 100000], // Iraqi Dinar
    'YER': [1000, 2000, 5000, 10000, 20000], // Yemeni Rial (volatile)
    'DZD': [500, 1000, 2500, 5000, 10000], // Algerian Dinar

    // Asian Muslim-majority countries (Claude's estimates)
    'IDR': [25000, 50000, 125000, 250000, 500000], // Indonesian Rupiah
    'PKR': [300, 600, 1500, 3000, 6000], // Pakistani Rupee
    'BDT': [100, 200, 500, 1000, 2000], // Bangladeshi Taka
    'AFN': [200, 400, 1000, 2000, 4000], // Afghan Afghani (volatile)

    // African countries
    'NGN': [1000, 2000, 5000, 10000, 20000], // Nigerian Naira

    // European countries (Claude's estimates)
    'EUR': [5, 10, 25, 50, 100], // Euro
    'GBP': [5, 10, 25, 50, 100], // British Pound
    'SEK': [50, 100, 250, 500, 1000], // Swedish Krona
    'NOK': [50, 100, 250, 500, 1000], // Norwegian Krone
    'DKK': [30, 60, 150, 300, 600], // Danish Krone
    'CHF': [5, 10, 25, 50, 100], // Swiss Franc

    // Western countries (Claude's estimates)
    'USD': [5, 10, 25, 50, 100], // US Dollar (baseline)
    'CAD': [5, 10, 25, 50, 100], // Canadian Dollar
    'AUD': [5, 10, 25, 50, 100], // Australian Dollar
    'NZD': [5, 10, 25, 50, 100], // New Zealand Dollar

    // Other significant markets (Claude's estimates)
    'CNY': [20, 40, 100, 200, 400], // Chinese Yuan
    'JPY': [500, 1000, 2500, 5000, 10000], // Japanese Yen
    'KRW': [5000, 10000, 25000, 50000, 100000], // South Korean Won
    'SGD': [5, 10, 25, 50, 100], // Singapore Dollar
    'HKD': [25, 50, 125, 250, 500], // Hong Kong Dollar
    'THB': [100, 200, 500, 1000, 2000], // Thai Baht
    'VND': [75000, 150000, 375000, 750000, 1500000], // Vietnamese Dong
    'PHP': [150, 300, 750, 1500, 3000], // Philippine Peso
    'BRL': [15, 30, 75, 150, 300], // Brazilian Real
    'MXN': [50, 100, 250, 500, 1000], // Mexican Peso
    'ZAR': [50, 100, 250, 500, 1000], // South African Rand
    'RUB': [300, 600, 1500, 3000, 6000], // Russian Ruble
  };

  /// Arabic currency symbols (hardcoded due to NumberFormat display issues)
  static const Map<String, String> arabicCurrencySymbols = {
    'AED': 'د.إ', // UAE Dirham
    'SAR': 'ر.س', // Saudi Riyal
    'QAR': 'ر.ق', // Qatari Riyal
    'KWD': 'د.ك', // Kuwaiti Dinar
    'BHD': 'د.ب', // Bahraini Dinar
    'OMR': 'ر.ع', // Omani Rial
    'EGP': 'ج.م', // Egyptian Pound
    'MAD': 'د.م', // Moroccan Dirham
    'TND': 'د.ت', // Tunisian Dinar
    'JOD': 'د.أ', // Jordanian Dinar
    'LBP': 'ل.ل', // Lebanese Pound
    'IQD': 'ع.د', // Iraqi Dinar
    'YER': 'ر.ي', // Yemeni Rial
    'DZD': 'د.ج', // Algerian Dinar
  };

  /// Gets currency symbol (Arabic hardcoded, others from NumberFormat)
  static String getSymbol(String currency) {
    final upperCurrency = currency.toUpperCase();
    return arabicCurrencySymbols[upperCurrency] ?? 
           NumberFormat.simpleCurrency(name: upperCurrency).currencySymbol;
  }

  /// Returns true if currency should be displayed RTL
  static bool isRTL(String currency) {
    return arabicCurrencySymbols.containsKey(currency.toUpperCase());
  }

  /// Gets the localized donation amounts for the specified currency
  ///
  /// Returns a list of 5 donation amounts that represent:
  /// 1. Entry level (accessible to most users)
  /// 2. Small donation
  /// 3. Medium donation
  /// 4. Large donation
  /// 5. Generous donation (for those who can afford more)
  ///
  /// [currency] - The 3-letter currency code (e.g., 'USD', 'EUR', 'AED')
  /// Returns: List of 5 integers representing donation amounts in the local currency,
  ///          or null if the currency is not supported
  ///
  /// Use isCurrencySupported() to check before calling this method
  static List<int>? getAmounts(String currency) {
    return currencyAmounts[currency.toUpperCase()];
  }

  /// Gets all supported currencies
  ///
  /// Returns: List of all currency codes that have localized amounts defined
  static List<String> getSupportedCurrencies() {
    return currencyAmounts.keys.toList()..sort();
  }

  /// Checks if a currency is supported
  ///
  /// [currency] - The 3-letter currency code to check
  /// Returns: true if the currency has localized amounts defined
  static bool isCurrencySupported(String currency) {
    return currencyAmounts.containsKey(currency.toUpperCase());
  }
}
