import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;
import 'package:mushafi/credentials.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/resource/color_resource.dart';

class StripePaymentHandler {
  final String socket_error_code = "socket_error";
  final String timeout_error_code = "timeout_error";

  /// Requests email verification by sending 6-digit code
  ///
  /// This method initiates the email verification process:
  /// 1. Sends a request to the backend with the provided email
  /// 2. Backend generates a 6-digit code and sends it via email
  /// 3. Returns a verification token that must be used in completeVerification
  ///
  /// Errors to handle:
  /// 422 (FastAPI validation) → "Please enter a valid email address"
  /// 400 (EMAIL_SEND_FAILED) → "Unable to send verification email..."
  /// 500 (SERVER_ERROR) → "Something went wrong..."
  ///
  /// [email] - The email address to verify
  /// [context] - BuildContext for localized error messages
  /// [onSuccess] - Callback function when request succeeds, receives verification token
  /// [onError] - Callback function when request fails, receives localized error message
  Future<void> requestVerification({
    required String email,
    required Function(String verificationToken) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    try {
      final response = await http
          .post(
            Uri.parse('$stripeBackendUrl/request-verification'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'email': email,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        onSuccess(data['verification_token']);
      } else if (response.statusCode == 422) {
        // FastAPI automatic validation error (invalid email format)
        onError("INVALID_EMAIL");
      } else {
        // Handle backend error codes with localized messages
        final errorData = json.decode(response.body);
        final errorCode = errorData['detail'] as String;
        FirebaseCrashlytics.instance.log(
            'donation_flow: Email verification request failed - ${errorCode}');

        onError(errorCode);
      }
    } on SocketException catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Email verification network error - Socket exception');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(socket_error_code);
    } on TimeoutException catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Email verification network error - Timeout');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(timeout_error_code);
    } on HttpException catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Email verification HTTP error - ${e.message}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(e.message);
    } catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Email verification unexpected error - ${e.toString()}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError('An error occurred: $e');
    }
  }

  /// Completes email verification and creates/gets customer
  ///
  /// This method finishes the email verification process:
  /// 1. Sends verification token, code, and email to backend
  /// 2. Backend validates the JWT token and verification code
  /// 3. Backend creates or retrieves Stripe customer
  /// 4. Returns customer ID and creation status
  ///
  /// Error codes returned (consistent with other methods):
  /// 422 (FastAPI validation) → 'INVALID_EMAIL'
  /// 400 (Business logic errors) → Backend error codes:
  ///   - 'INVALID_TOKEN_TYPE', 'EMAIL_MISMATCH', 'INVALID_CODE'
  ///   - 'TOKEN_EXPIRED', 'INVALID_TOKEN', 'CUSTOMER_CREATION_FAILED'
  /// 500 (Server errors) → 'SERVER_ERROR'
  /// Network errors → socket_error_code, timeout_error_code
  ///
  /// [verificationToken] - JWT token from requestVerification
  /// [code] - 6-digit verification code entered by user
  /// [email] - Email address (must match token email)
  /// [onSuccess] - Callback when verification succeeds, receives (customerId, wasCreated)
  /// [onError] - Callback when verification fails, receives error code
  Future<void> completeVerification({
    required String verificationToken,
    required String code,
    required String email,
    required Function(String customerId, bool wasCreated) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    FirebaseCrashlytics.instance
        .log('donation_flow: Email verification completion started');
    try {
      final response = await http
          .post(
            Uri.parse('$stripeBackendUrl/complete-verification'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'verification_token': verificationToken,
              'code': code,
              'email': email,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        FirebaseCrashlytics.instance.log(
            'donation_flow: Email verification completed - Customer ${data['was_created'] ? "created" : "found"}');
        onSuccess(data['customer_id'], data['was_created']);
      } else if (response.statusCode == 422) {
        FirebaseCrashlytics.instance.log(
            'donation_flow: Email verification completion failed - Invalid email');
        // FastAPI automatic validation error
        onError('INVALID_EMAIL');
      } else {
        // Handle backend error codes
        final errorData = json.decode(response.body);
        final errorCode = errorData['detail'] as String;
        FirebaseCrashlytics.instance.log(
            'donation_flow: Email verification completion failed - ${errorCode}');
        onError(errorCode);
      }
    } on SocketException catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Email verification completion network error - Socket exception');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(socket_error_code);
    } on TimeoutException catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Email verification completion network error - Timeout');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(timeout_error_code);
    } on HttpException catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Email verification completion HTTP error - ${e.message}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(e.message);
    } catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Email verification completion unexpected error - ${e.toString()}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError('An error occurred: $e');
    }
  }

  /// Processes a one-time payment using Stripe.
  ///
  /// [amount] - The payment amount in dollars (e.g., 10.50 for $10.50).
  /// [currency] - The currency code (e.g., 'usd').
  /// [customerId] - The Stripe customer ID for this payment.
  /// [onSuccess] - Callback function when payment succeeds.
  /// [onError] - Callback function when payment fails, receives an error message.
  /// [context] - The current [BuildContext].
  /// [description] - Optional payment description.
  Future<void> processPayment({
    required double amount,
    required String currency,
    required String customerId,
    required Function() onSuccess,
    required Function(String errorMessage) onError,
    required BuildContext context,
    String? description,
  }) async {
    FirebaseCrashlytics.instance
        .log('donation_flow: One-time payment intent creation started');
    try {
      // Create payment intent on the server
      final response = await http
          .post(
            Uri.parse('$stripeBackendUrl/create-payment-intent'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'amount': amount,
              'currency': currency,
              'payment_method_types': ['card'],
              'description': description ?? "",
              'customer_id': customerId,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode != 200) {
        FirebaseCrashlytics.instance.log(
            'donation_flow: Payment intent creation failed - ${response.statusCode}');
        onError('Failed to create payment: ${response.body}');
        return;
      }

      final paymentIntentData = json.decode(response.body);
      final clientSecret = paymentIntentData['client_secret'];
      FirebaseCrashlytics.instance
          .log('donation_flow: Payment intent created successfully');

      // Initialize and present payment sheet
      FirebaseCrashlytics.instance
          .log('donation_flow: Presenting payment sheet for one-time payment');
      await _initializeAndPresentPaymentSheet(
        clientSecret: clientSecret,
        context: context,
        isSubscription: false,
      );

      // Payment successful
      FirebaseCrashlytics.instance
          .log('donation_flow: Payment sheet completed successfully');
      onSuccess();
    } on StripeException catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Stripe payment error - ${e.error.code ?? "unknown"}: ${e.error.localizedMessage}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(e.error.localizedMessage ?? 'Payment failed');
    } on SocketException catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Payment network error - Socket exception');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(socket_error_code);
    } on TimeoutException catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Payment network error - Timeout');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(timeout_error_code);
    } on HttpException catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Payment HTTP error - ${e.message}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(e.message);
    } catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Payment unexpected error - ${e.toString()}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError('An error occurred: $e');
    }
  }

  Future<void> getPaymentHistory({
    required String customerId,
    required Function(List<Map<String, dynamic>> payments) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$stripeBackendUrl/payment-history/$customerId'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final payments = List<Map<String, dynamic>>.from(data['payments']);
        onSuccess(payments);
      } else {
        onError('Failed to get payment history: ${response.body}');
      }
    } on SocketException {
      onError(socket_error_code);
    } on TimeoutException {
      onError(timeout_error_code);
    } on HttpException catch (e) {
      onError(e.message);
    } catch (e) {
      onError('Failed to load payment history');
    }
  }

  /// Subscribes a user to a recurring Stripe subscription.
  ///
  /// [customerId] - The Stripe customer ID for this subscription.
  /// [amount] - The subscription amount in dollars (e.g., 9.99 for $9.99).
  /// [context] - The current [BuildContext].
  /// [interval] - The billing interval (e.g., 'month', 'year'). Defaults to 'month'.
  /// [metadata] - Optional additional metadata for the subscription.
  /// [onSuccess] - Callback function when subscription succeeds, receives the subscription info.
  /// [onError] - Callback function when subscription fails, receives an error message.
  Future<void> subscribeUser({
    required String customerId,
    required double amount,
    required String currency,
    required BuildContext context,
    String interval = 'month',
    Map<String, String>? metadata,
    required Function(Map<String, dynamic> subscriptionInfo) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    FirebaseCrashlytics.instance
        .log('donation_flow: Subscription creation started');
    try {
      // Create the subscription on the backend
      final response = await http
          .post(
            Uri.parse('$stripeBackendUrl/create-subscription'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'customer_id': customerId,
              'amount': amount,
              'currency': currency,
              'interval': interval,
              'metadata': metadata ?? {},
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode != 200) {
        FirebaseCrashlytics.instance.log(
            'donation_flow: Subscription creation failed - ${response.statusCode}');
        onError('Failed to create subscription: ${response.body}');
        return;
      }

      final subscriptionData = json.decode(response.body);
      FirebaseCrashlytics.instance
          .log('donation_flow: Subscription created on backend');

      // If the subscription requires payment, present the payment sheet
      final clientSecret = subscriptionData['client_secret'];
      if (clientSecret != null && clientSecret.isNotEmpty) {
        FirebaseCrashlytics.instance
            .log('donation_flow: Presenting payment sheet for subscription');
        await _initializeAndPresentPaymentSheet(
          clientSecret: clientSecret,
          context: context,
          isSubscription: true,
        );
        FirebaseCrashlytics.instance
            .log('donation_flow: Subscription payment sheet completed');
      } else {
        FirebaseCrashlytics.instance
            .log('donation_flow: Subscription created without payment sheet');
      }

      // Return all subscription info
      onSuccess(subscriptionData);
    } on StripeException catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Stripe subscription error - ${e.error.code ?? "unknown"}: ${e.error.localizedMessage}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(e.error.localizedMessage ?? 'Subscription creation failed');
    } on SocketException catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Subscription network error - Socket exception');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(socket_error_code);
    } on TimeoutException catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Subscription network error - Timeout');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(timeout_error_code);
    } on HttpException catch (e, st) {
      FirebaseCrashlytics.instance
          .log('donation_flow: Subscription HTTP error - ${e.message}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError(e.message);
    } catch (e, st) {
      FirebaseCrashlytics.instance.log(
          'donation_flow: Subscription unexpected error - ${e.toString()}');
      FirebaseCrashlytics.instance.recordError(e, st, fatal: false);
      onError('An error occurred: $e');
    }
  }

  /// Cancels a subscription at the end of the current billing period.
  ///
  /// This method marks the subscription for cancellation at the end of the current period,
  /// allowing the user to continue using the service until their paid period ends.
  ///
  /// [subscriptionId] - The subscription ID to cancel.
  /// [onSuccess] - Callback function when cancellation succeeds, receives cancellation details
  ///                including the subscription status and when it will be cancelled.
  /// [onError] - Callback function when cancellation fails, receives error message.
  Future<void> cancelSubscription({
    required String subscriptionId,
    required Function(Map<String, dynamic> cancellationInfo) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    try {
      final response = await http
          .post(
            Uri.parse('$stripeBackendUrl/cancel-subscription'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'subscription_id': subscriptionId,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final cancellationData = json.decode(response.body);
        onSuccess(cancellationData);
      } else {
        onError('Failed to cancel subscription: ${response.body}');
      }
    } on SocketException {
      onError(socket_error_code);
    } on TimeoutException {
      onError(timeout_error_code);
    } on HttpException catch (e) {
      onError(e.message);
    } catch (e) {
      onError('An error occurred: $e');
    }
  }

  /// Reactivates a cancelled subscription before it ends.
  ///
  /// This method removes the cancellation from a subscription that was marked to cancel at period end.
  ///
  /// [subscriptionId] - The subscription ID to reactivate.
  /// [onSuccess] - Callback function when reactivation succeeds, receives updated subscription info.
  /// [onError] - Callback function when reactivation fails, receives error message.
  Future<void> reactivateSubscription({
    required String subscriptionId,
    required Function(Map<String, dynamic> subscriptionInfo) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    try {
      final response = await http
          .post(
            Uri.parse('$stripeBackendUrl/reactivate-subscription'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'subscription_id': subscriptionId,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final subscriptionData = json.decode(response.body);
        onSuccess(subscriptionData);
      } else {
        onError('Failed to reactivate subscription: ${response.body}');
      }
    } on SocketException {
      onError(socket_error_code);
    } on TimeoutException {
      onError(timeout_error_code);
    } on HttpException catch (e) {
      onError(e.message);
    } catch (e) {
      onError('An error occurred: $e');
    }
  }

  /// Gets a single subscription by ID.
  ///
  /// [subscriptionId] - The subscription ID to retrieve.
  /// [onSuccess] - Callback function when retrieval succeeds, receives subscription info.
  /// [onError] - Callback function when retrieval fails, receives error message.
  Future<void> getSubscription({
    required String subscriptionId,
    required Function(Map<String, dynamic> subscriptionInfo) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$stripeBackendUrl/subscription/$subscriptionId'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final subscriptionData = json.decode(response.body);
        onSuccess(subscriptionData);
      } else {
        onError('Failed to get subscription: ${response.body}');
      }
    } on SocketException {
      onError(socket_error_code);
    } on TimeoutException {
      onError(timeout_error_code);
    } on HttpException catch (e) {
      onError(e.message);
    } catch (e) {
      onError('Failed to get subscription');
    }
  }

  // Gets all subscriptions for a customer
  //
  // [customerId] - The customer ID to get subscriptions for
  // [onSuccess] - Callback function when retrieval succeeds, receives list of subscriptions
  // [onError] - Callback function when retrieval fails, receives error message
  Future<void> getCustomerSubscriptions({
    required String customerId,
    required Function(List<Map<String, dynamic>> subscriptions) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$stripeBackendUrl/customer-subscriptions/$customerId'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final subscriptions =
            List<Map<String, dynamic>>.from(data['subscriptions']);
        onSuccess(subscriptions);
      } else {
        onError('Failed to get customer subscriptions: ${response.body}');
      }
    } on SocketException {
      onError(socket_error_code);
    } on TimeoutException {
      onError(timeout_error_code);
    } on HttpException catch (e) {
      onError(e.message);
    } catch (e) {
      onError('Failed to get customer subscriptions');
    }
  }

  /// Updates the amount of an existing subscription.
  ///
  /// This method handles updating a subscription's billing amount:
  /// 1. Sends a request to the backend to update the subscription amount.
  /// 2. The backend will create a new price with the updated amount and modify the subscription.
  /// 3. Calls [onSuccess] with the updated subscription info if successful, or [onError] with an error message if it fails.
  ///
  /// [subscriptionId] - The ID of the subscription to update.
  /// [newAmount] - The new subscription amount in dollars (e.g., 19.99 for $19.99).
  /// [prorationBehavior] - How to handle proration ('always_invoice', 'none', 'create_prorations'). Defaults to 'always_invoice'.
  /// [onSuccess] - Callback function when update succeeds, receives the updated subscription info.
  /// [onError] - Callback function when update fails, receives an error message.
  Future<void> updateSubscriptionAmount({
    required String subscriptionId,
    required double newAmount,
    String prorationBehavior = 'always_invoice',
    required Function(Map<String, dynamic> updatedSubscriptionInfo) onSuccess,
    required Function(String errorMessage) onError,
  }) async {
    try {
      final response = await http
          .post(
            Uri.parse('$stripeBackendUrl/update-subscription-amount'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'subscription_id': subscriptionId,
              'new_amount': newAmount,
              'proration_behavior': prorationBehavior,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final updatedSubscriptionData = json.decode(response.body);
        onSuccess(updatedSubscriptionData);
      } else {
        onError('Failed to update subscription amount: ${response.body}');
      }
    } on SocketException {
      onError(socket_error_code);
    } on TimeoutException {
      onError(timeout_error_code);
    } on HttpException catch (e) {
      onError(e.message);
    } catch (e) {
      onError('An error occurred: $e');
    }
  }

  // Private method to initialize and present payment sheet
  Future<void> _initializeAndPresentPaymentSheet({
    required String clientSecret,
    required BuildContext context,
    required bool isSubscription,
  }) async {
    FirebaseCrashlytics.instance.log('donation_flow: Initializing payment sheet - ${isSubscription ? "subscription" : "one-time"}');
    // Configure payment sheet appearance
    var appearance = PaymentSheetAppearance(
      colors: PaymentSheetAppearanceColors(
        background: ColorResource.backgroundDefault(context.isDarkMode),
        primary: ColorResource.primary(context.isDarkMode),
        placeholderText: ColorResource.textGrey(context.isDarkMode),
        componentBackground: ColorResource.backgroundWhite(context.isDarkMode),
        componentBorder: ColorResource.border(context.isDarkMode),
        componentDivider: ColorResource.border(context.isDarkMode),
        componentText: ColorResource.textDefault(context.isDarkMode),
        secondaryText: ColorResource.textDefault(context.isDarkMode),
        icon: ColorResource.textDefault(context.isDarkMode),
        error: ColorResource.danger(context.isDarkMode),
      ),
      primaryButton: PaymentSheetPrimaryButtonAppearance(
        shapes: const PaymentSheetPrimaryButtonShape(blurRadius: 8),
        colors: PaymentSheetPrimaryButtonTheme(
          light: PaymentSheetPrimaryButtonThemeColors(
            background: ColorResource.primary(context.isDarkMode),
            text: ColorResource.onPrimary(context.isDarkMode),
          ),
          dark: PaymentSheetPrimaryButtonThemeColors(
            background: ColorResource.primary(context.isDarkMode),
            text: ColorResource.onPrimary(context.isDarkMode),
          ),
        ),
      ),
    );

    // Initialize payment sheet
    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
        paymentIntentClientSecret: clientSecret,
        merchantDisplayName: context.tr("app_name"),
        appearance: appearance,
      ),
    );
    FirebaseCrashlytics.instance.log('donation_flow: Payment sheet initialized successfully');

    // Present payment sheet
    await Stripe.instance.presentPaymentSheet();
    FirebaseCrashlytics.instance.log('donation_flow: Payment sheet presented and completed');
  }
}
