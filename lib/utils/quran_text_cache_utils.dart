import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:mushafi/extension/integer_extension.dart';
import 'package:mushafi/extension/rect_extension.dart';
import 'package:mushafi/extension/verse_key_parser.dart';
import 'package:mushafi/presentation/model/quran/mushaf_code.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/quran_line.dart';
import 'package:mushafi/presentation/model/quran/quran_mode.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/presentation/model/quran_canvas/cached_quran_text.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_page_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_image_asset.dart';
import 'package:mushafi/presentation/model/quran_canvas/serializable_quran_canvas_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/serializable_quran_canvas_page_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/quran_image_helper.dart';
import 'package:mushafi/utils/recitation_alignment_helper.dart';
import 'package:mushafi/utils/word_image_render_helper.dart';

class QuranTextCacheUtils {
  QuranTextCacheUtils._();

  static FlutterIsolate? currentProcess;
  static final receivePort = ReceivePort();
  static final broadcastStream = receivePort.asBroadcastStream();
  static SendPort? sendPort;
  static StreamSubscription? sendPortSubscription;
  static StreamSubscription? cacheSubscription;

  // NOTE: FOR ISOLATE USE ONLY. DO NOT USE THIS IN THE UI/MAIN THREAD
  static QuranImageAsset? _isolateTextCacheImageAsset;

  static Future<void> init() async {
    sendPortSubscription = broadcastStream.listen((message) async {
      if (message is SendPort) {
        sendPort = message;
        sendPortSubscription?.cancel();
      }
    });

    currentProcess ??= await FlutterIsolate.spawn(
        prepareCacheComputation, receivePort.sendPort);
  }

  static Future<ui.Image> parseImagePixels(
      Uint8List pixels, int width, int height) async {
    final Completer<ui.Image> completer = Completer();
    ui.decodeImageFromPixels(
        pixels, width, height, ui.PixelFormat.rgba8888, completer.complete);
    return completer.future;
  }

  static void setCallback(
      Function(
        int pageNumber,
        ui.Image? image,
        ui.Image? lineBreakImage,
        Map<QuranWord, Rect>? wordToHighlightRectMap,
        Map<QuranWord, Rect>? wordToHighlightLineBreakRectMap,
      ) onCached) {
    cacheSubscription?.cancel();
    cacheSubscription = broadcastStream.listen((message) async {
      if (message is Map<String, dynamic>) {
        final pageNumber = message['pageNumber'] as int;
        final result = (message['result'] as Map<String, dynamic>?) ?? {};
        if (result.isEmpty) return;

        final imageWidth = result['imageWidth'] as int;
        final imageHeight = result['imageHeight'] as int;

        final imagePixels = result['imagePixels'] as Uint8List?;
        final image = (imagePixels != null)
            ? await parseImagePixels(imagePixels, imageWidth, imageHeight)
            : null;

        final lineBreakImagePixels =
            result['lineBreakImagePixels'] as Uint8List?;
        final lineBreakImage = (lineBreakImagePixels != null)
            ? await parseImagePixels(
                lineBreakImagePixels, imageWidth, imageHeight)
            : null;

        final wordToHighlightRects =
            result['wordToHighlightRects'] as List<Map<String, dynamic>>?;
        final wordToHighlightRectMap = (wordToHighlightRects != null)
            ? Map.fromIterable(
                wordToHighlightRects,
                key: (data) {
                  final parsedData = WordHighlightData.fromJson(data);
                  return parsedData.word;
                },
                value: (data) {
                  final parsedData = WordHighlightData.fromJson(data);
                  return parsedData.rect.toRect();
                },
              )
            : null;

        final wordToHighlightLineBreakRects =
            result['wordToHighlightLineBreakRects']
                as List<Map<String, dynamic>>?;
        final wordToHighlightLineBreakRectMap =
            (wordToHighlightLineBreakRects != null)
                ? Map.fromIterable(
                    wordToHighlightLineBreakRects,
                    key: (data) {
                      final parsedData = WordHighlightData.fromJson(data);
                      return parsedData.word;
                    },
                    value: (data) {
                      final parsedData = WordHighlightData.fromJson(data);
                      return parsedData.rect.toRect();
                    },
                  )
                : null;

        onCached(
          pageNumber,
          image,
          lineBreakImage,
          wordToHighlightRectMap,
          wordToHighlightLineBreakRectMap,
        );
      }
    });
  }

  static void disposeCacheCallback() {
    cacheSubscription?.cancel();
    cacheSubscription = null;
  }

  @pragma('vm:entry-point')
  static void prepareCacheComputation(SendPort sendPort) {
    final receivePort = ReceivePort();

    sendPort.send(receivePort.sendPort);

    receivePort.listen((message) async {
      final typedMessage = message as Map<String, dynamic>;

      final pageNumber = typedMessage['pageNumber'] as int;
      final params = typedMessage['params'] as Map<String, dynamic>;

      final result = await startCompute(params);
      final sendMessage = {'pageNumber': pageNumber, 'result': result};
      sendPort.send(sendMessage);
    });
  }

  static Future<Map<String, dynamic>?> startCompute(
      Map<String, dynamic> params) async {
    final canvasSize =
        Size(params['width'] as double, params['height'] as double);

    final dataJson = params['data'] as Map<String, dynamic>;
    final isDarkMode = dataJson['isDarkMode'] as bool;
    final mushafDesign = MushafDesign.fromJson(dataJson['imageAssetDesign']);
    if (_isolateTextCacheImageAsset == null ||
        _isolateTextCacheImageAsset?.design != mushafDesign) {
      _isolateTextCacheImageAsset = await QuranImageHelper.getQuranThemeAsset(
        mushafDesign,
        isDarkMode,
        loadFrameImage: false,
      );
    }

    final serializableData = await SerializableQuranCanvasData.fromJson(
      dataJson,
      _isolateTextCacheImageAsset!,
    );
    final data = QuranCanvasData.fromSerializedData(serializableData);

    final serializablePageData = SerializableQuranCanvasPageData.fromJson(
        params['pageData'] as Map<String, dynamic>);
    final pageData =
        QuranCanvasPageData.fromSerializedData(serializablePageData);

    final quranMode =
        QuranMode.fromJson(params['quranMode'] as Map<String, dynamic>);
    final recitationAlignmentHelper = RecitationAlignmentHelper.fromJson(
      params['recitationAlignmentHelper'] as Map<String, dynamic>,
    );

    if (data.zoomState != ZoomState.zoomedInLevel2) {
      return computeCacheQuranText(
        canvasSize: canvasSize,
        data: data,
        pageData: pageData,
        quranMode: quranMode,
        recitationAlignmentHelper: recitationAlignmentHelper,
      );
    } else {
      return computeCacheLineBreakQuranText(
        canvasSize: canvasSize,
        data: data,
        pageData: pageData,
        quranMode: quranMode,
      );
    }
  }

  static Future<Map<String, dynamic>?> computeCacheQuranText({
    required Size canvasSize,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
    required QuranMode quranMode,
    required RecitationAlignmentHelper recitationAlignmentHelper,
  }) async {
    final quranPage = pageData.quranPage;
    if (quranPage != null) {
      final fontByteList = quranPage.fontByteList;
      await loadFontFromList(
        Uint8List.fromList(fontByteList),
        fontFamily: quranPage.pageNumber.threeDigitsFormat(),
      );
    }

    final cachedQuranText = await cacheQuranTextAsync(
      canvasSize: canvasSize,
      data: data,
      pageData: pageData,
      quranMode: quranMode,
      recitationAlignmentHelper: recitationAlignmentHelper,
    );
    final image = cachedQuranText.image;
    final wordHighlightRects = cachedQuranText.wordHighlightRects;

    final byteData = await image.toByteData(format: ui.ImageByteFormat.rawRgba);
    if (byteData == null) return null;

    final result = {
      'wordToHighlightRects':
          wordHighlightRects.map((data) => data.toJson()).toList(),
      'imagePixels': byteData.buffer.asUint8List(),
      'imageWidth': image.width,
      'imageHeight': image.height,
    };

    return result;
  }

  static CachedQuranText cacheQuranText({
    required Size canvasSize,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
    required QuranMode quranMode,
    required RecitationAlignmentHelper recitationAlignmentHelper,
  }) {
    final width = canvasSize.width;
    final height = canvasSize.height;

    ui.PictureRecorder recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    canvas.scale(data.quranTextRecorderImageScale);

    final wordHighlightRects = drawQuranText(
      canvas: canvas,
      canvasSize: canvasSize,
      data: data,
      pageData: pageData,
      quranMode: quranMode,
      recitationAlignmentHelper: recitationAlignmentHelper,
    );

    final image = recorder.endRecording().toImageSync(
          (width * data.quranTextRecorderImageScale).toInt(),
          (height * data.quranTextRecorderImageScale).toInt(),
        );

    return CachedQuranText(wordHighlightRects, image);
  }

  static Future<CachedQuranText> cacheQuranTextAsync({
    required Size canvasSize,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
    required QuranMode quranMode,
    required RecitationAlignmentHelper recitationAlignmentHelper,
  }) async {
    final width = canvasSize.width;
    final height = canvasSize.height;

    ui.PictureRecorder recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    canvas.scale(data.quranTextRecorderImageScale);

    final wordHighlightRects = drawQuranText(
      canvas: canvas,
      canvasSize: canvasSize,
      data: data,
      pageData: pageData,
      quranMode: quranMode,
      recitationAlignmentHelper: recitationAlignmentHelper,
    );

    final image = await recorder.endRecording().toImage(
          (width * data.quranTextRecorderImageScale).toInt(),
          (height * data.quranTextRecorderImageScale).toInt(),
        );

    return CachedQuranText(wordHighlightRects, image);
  }

  static Future<void> cacheText(
    Size canvasSize,
    QuranCanvasData data,
    QuranCanvasPageData pageData,
    QuranMode quranMode,
    RecitationAlignmentHelper recitationAlignmentHelper,
  ) async {
    if (data.zoomState != ZoomState.zoomedInLevel2 &&
        pageData.textImage != null) return;
    if (data.zoomState == ZoomState.zoomedInLevel2 &&
        pageData.lineBreakTextImage != null) return;
    if (pageData.quranPage == null) return;

    final params = {
      'width': canvasSize.width,
      'height': canvasSize.height,
      'data': data.toSerializableData().toJson(),
      'pageData': pageData.toSerializableData().toJson(),
      'quranMode': quranMode.toJson(),
      'recitationAlignmentHelper': recitationAlignmentHelper.toJson(),
    };

    final pageNumber = pageData.quranPage!.pageNumber;

    final message = {
      'pageNumber': pageNumber,
      'params': params,
    };
    sendPort?.send(message);
  }

  static List<WordHighlightData> drawQuranText({
    required Canvas canvas,
    required Size canvasSize,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
    required QuranMode quranMode,
    required RecitationAlignmentHelper recitationAlignmentHelper,
  }) {
    final lineList = pageData.quranPage?.lineList;
    if (lineList == null) {
      return [];
    }

    final List<WordHighlightData> wordHighlightRects = [];

    canvas.save();

    if (data.isSpread()) {
      final pageWidth = data.getPageWidth();
      canvas.translate(-pageWidth / 2, 0);
    }

    final textWidth = pageData.textDimension.width;
    final textHeight = pageData.textDimension.height;
    final initialLeft =
        canvasSize.width / 2 - textWidth * pageData.textScale / 2;
    double initialTop =
        canvasSize.height / 2 - textHeight * pageData.textScale / 2;

    if (quranMode is! DefaultQuranMode) {
      initialTop -= data.borderWidth * data.frameScale;
    }

    // position it first on the scale start (x,y), then we will scale afterward
    canvas.translate(initialLeft, initialTop);
    canvas.scale(pageData.textScale, pageData.textScale);

    TextPainter textPainter;
    double lineHeight = 0;
    double scaledLineHeight = 0;
    double lineLeft = 0;
    double lineTop = 0;
    double spacePerGap = 0;
    int gapCount = 0;
    double totalSpace = 0;
    double curLineWidth = 0;
    double currentLineTop = 0;
    double wordWidth = 0;
    double dotWidth = 0;

    for (final line in lineList) {
      canvas.save();

      curLineWidth = 0;
      spacePerGap = line.additionalSpacePerGap ?? 0;
      gapCount = line.words.length - 1;
      totalSpace = spacePerGap * gapCount;
      lineLeft = textWidth / 2 - (line.width + totalSpace) / 2;
      canvas.translate(lineLeft, 0);

      for (final (wordIndex, word) in line.words.reversed.indexed) {
        if (line.type == LineType.quran ||
            (line.type == LineType.bismillah && word.isGlobalFont)) {
          if (quranMode is DefaultQuranMode) {
            textPainter = pageData.quranTextPainter
              ..text = TextSpan(
                text: word.getText(),
                style: pageData.quranTextStyle,
              )
              ..layout();
          } else {
            TextStyle quranTextStyle = pageData.quranTextStyle;

            final result = recitationAlignmentHelper.resultMap[word.id];
            if (result != null && !result.isCorrect) {
              quranTextStyle = quranTextStyle.copyWith(
                color: Colors.red,
              );
            }

            textPainter = pageData.quranTextPainter
              ..text = TextSpan(
                text: word.getText(),
                style: quranTextStyle,
              )
              ..layout();
          }
        } else if (line.type == LineType.bismillah) {
          textPainter = pageData.bismillahTextPainter;
        } else {
          textPainter = pageData.surahTextPainter
            ..text = TextSpan(
              text: line.text,
              style: pageData.surahTextStyle,
            )
            ..layout();
        }

        if (word.isVerseNumber) {
          drawVerseNumber(
            canvas: canvas,
            canvasSize: canvasSize,
            word: word,
            data: data,
            pageData: pageData,
          );
        } else if (line.type == LineType.surahHeader) {
          drawSurahHeader(
            canvas: canvas,
            canvasSize: canvasSize,
            lineWidth: line.width,
            lineHeight: line.height,
            data: data,
            pageData: pageData,
          );
        } else {
          lineHeight = textPainter.height;
          scaledLineHeight = line.height;
          lineTop = scaledLineHeight / 2 - lineHeight / 2;

          switch (quranMode) {
            case DefaultQuranMode():
            case RecitationCheckerQuranMode():
              if (pageData.quranPage?.pageNumber == 2 &&
                  line.type == LineType.bismillah) {
                canvas.save();

                final centerHorizontal = textPainter.width / 2;
                final centerVertical = line.height / 2;

                canvas.translate(centerHorizontal, centerVertical);
                canvas.scale(.75, .75);
                canvas.translate(-centerHorizontal, -centerVertical);

                textPainter.paint(
                  canvas,
                  Offset(0, lineTop),
                );

                canvas.restore();
              } else {
                if (WordImageRenderHelper.preRenderedWordIds
                        .contains(word.id) &&
                    Platform.isIOS) {
                  final path = 'assets/images/${word.id}.png';
                  final wordImage = WordImageRenderHelper.getWord(path);
                  if (wordImage != null) {
                    canvas.save();
                    canvas.scale(
                      textPainter.width / wordImage.width,
                      textPainter.height / wordImage.height,
                    );
                    textPainter.text?.style?.color;
                    canvas.drawImage(
                      wordImage,
                      Offset(0, lineTop),
                      Paint()
                        ..colorFilter = ColorFilter.mode(
                          textPainter.text?.style?.color ??
                              ColorResource.textDefault(data.isDarkMode),
                          BlendMode.srcATop,
                        ),
                    );
                    canvas.restore();
                  } else {
                    textPainter.paint(
                      canvas,
                      Offset(0, lineTop),
                    );
                  }
                } else {
                  textPainter.paint(
                    canvas,
                    Offset(0, lineTop),
                  );
                }
              }
            case MemorizationQuranMode():
              drawMemorizationWord(
                canvas: canvas,
                recitationAlignmentHelper: recitationAlignmentHelper,
                quranMode: quranMode,
                line: line,
                word: word,
                wordIndex: wordIndex,
                lineTop: lineTop,
                textPainter: textPainter,
                data: data,
                pageData: pageData,
              );
          }
        }

        wordWidth = (word.isVerseNumber && word.verseNumberSize != null)
            ? (word.verseNumberSize ?? textPainter.width)
            : textPainter.width;

        final newWordTapRect = Rect.fromLTWH(
          initialLeft / pageData.textScale + lineLeft + curLineWidth,
          initialTop / pageData.textScale + currentLineTop,
          (wordIndex == line.words.reversed.length - 1)
              ? wordWidth
              : wordWidth + spacePerGap,
          scaledLineHeight,
        ).toScaledRect(pageData.textScale);
        wordHighlightRects.add(
          WordHighlightData(
            word: word,
            rect: RectData.fromRect(newWordTapRect),
          ),
        );

        curLineWidth += wordWidth + spacePerGap; // for tap rect

        canvas.translate(wordWidth + spacePerGap, 0);
      }
      canvas.restore();

      // for tap rect
      currentLineTop += line.height;

      canvas.translate(0, line.height);
    }

    canvas.restore();

    return wordHighlightRects;
  }

  static Future<Map<String, dynamic>?> computeCacheLineBreakQuranText({
    required Size canvasSize,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
    required QuranMode quranMode,
  }) async {
    final quranPage = pageData.quranPage;
    if (quranPage != null) {
      final fontByteList = quranPage.fontByteList;
      await loadFontFromList(
        Uint8List.fromList(fontByteList),
        fontFamily: quranPage.pageNumber.threeDigitsFormat(),
      );
    }

    final cachedQuranText = await cacheLineBreakQuranTextAsync(
      canvasSize: canvasSize,
      data: data,
      pageData: pageData,
      quranMode: quranMode,
    );
    final image = cachedQuranText.image;
    final wordHighlightRects = cachedQuranText.wordHighlightRects;

    final byteData = await image.toByteData(format: ui.ImageByteFormat.rawRgba);
    if (byteData == null) return null;

    final result = {
      'wordToHighlightLineBreakRects':
          wordHighlightRects.map((data) => data.toJson()).toList(),
      'lineBreakImagePixels': byteData.buffer.asUint8List(),
      'imageWidth': image.width,
      'imageHeight': image.height,
    };

    return result;
  }

  static CachedQuranText cacheLineBreakQuranText({
    required Size canvasSize,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
    required QuranMode quranMode,
  }) {
    final zoomedTextHeight = pageData.getZoomedTextHeight(data.zoomState);
    final width = canvasSize.width;
    final height = data.getPageHeight(zoomedTextHeight);

    ui.PictureRecorder recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    canvas.scale(data.quranTextRecorderImageScale);

    final wordHighlightRects = drawLineBreakQuranText(
      canvas: canvas,
      canvasSize: canvasSize,
      data: data,
      pageData: pageData,
      quranMode: quranMode,
    );

    final image = recorder.endRecording().toImageSync(
          (width * data.quranTextRecorderImageScale).toInt(),
          (height * data.quranTextRecorderImageScale).toInt(),
        );

    return CachedQuranText(wordHighlightRects, image);
  }

  static Future<CachedQuranText> cacheLineBreakQuranTextAsync({
    required Size canvasSize,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
    required QuranMode quranMode,
  }) async {
    final zoomedTextHeight = pageData.getZoomedTextHeight(data.zoomState);
    final width = canvasSize.width;
    final height = data.getPageHeight(zoomedTextHeight);

    ui.PictureRecorder recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    canvas.scale(data.quranTextRecorderImageScale);

    final wordHighlightRects = drawLineBreakQuranText(
      canvas: canvas,
      canvasSize: canvasSize,
      data: data,
      pageData: pageData,
      quranMode: quranMode,
    );

    final image = await recorder.endRecording().toImage(
          (width * data.quranTextRecorderImageScale).toInt(),
          (height * data.quranTextRecorderImageScale).toInt(),
        );

    return CachedQuranText(wordHighlightRects, image);
  }

  static List<WordHighlightData> drawLineBreakQuranText({
    required Canvas canvas,
    required Size canvasSize,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
    required QuranMode quranMode,
  }) {
    final lineBreakList = pageData.quranPage?.lineBreakList;
    if (lineBreakList == null) {
      return [];
    }

    final List<WordHighlightData> wordHighlightRects = [];

    canvas.save();

    final initialTop = data.navigationHeight / data.rightZoomScale;
    canvas.translate(0, initialTop);
    canvas.scale(pageData.textScale, pageData.textScale);

    final paddedCanvasWidth =
        canvasSize.width * data.lineBreakWidthPaddingScale;
    final paddingWidth = (canvasSize.width - paddedCanvasWidth) / 2;
    final rightPaddedCanvasWidth = paddedCanvasWidth + paddingWidth;
    final initialTextRight =
        rightPaddedCanvasWidth / pageData.textScale / data.rightZoomScale;
    final totalCurrentScale = data.rightZoomScale * pageData.textScale;

    double currentWordLeft = 0;
    double currentWordWidth = 0;
    double currentLineTop = 0;

    TextPainter textPainter;

    for (final line in lineBreakList) {
      canvas.save();

      // surah header is not considered as word
      if (line.type == LineType.surahHeader) {
        canvas.translate(paddingWidth / totalCurrentScale, 0);
      } else {
        canvas.translate(initialTextRight, 0);
        currentWordLeft = initialTextRight;
      }
      for (final word in line.words) {
        if (line.type == LineType.quran ||
            (line.type == LineType.bismillah && word.isGlobalFont)) {
          textPainter = pageData.quranTextPainter
            ..text = TextSpan(
              text: word.getText(),
              style: pageData.quranTextStyle,
            )
            ..layout();
        } else if (line.type == LineType.bismillah) {
          textPainter = pageData.bismillahTextPainter;
        } else {
          textPainter = pageData.surahTextPainter
            ..text = TextSpan(
              text: line.text,
              style: pageData.surahTextStyle,
            )
            ..layout();
        }

        if (word.isVerseNumber) {
          final wordWidth = (word.isVerseNumber && word.verseNumberSize != null)
              ? (word.verseNumberSize ?? pageData.quranTextPainter.width)
              : pageData.quranTextPainter.width;

          canvas.translate(-wordWidth, 0);
          drawVerseNumber(
            canvas: canvas,
            canvasSize: canvasSize,
            word: word,
            data: data,
            pageData: pageData,
          );

          currentWordLeft -= wordWidth;
          currentWordWidth = wordWidth;
        } else if (line.type == LineType.surahHeader) {
          final lineWidthBeforeScale = line.width / totalCurrentScale;
          final lineHeightBeforeScale = line.height / totalCurrentScale;
          drawSurahHeader(
            canvas: canvas,
            canvasSize: canvasSize,
            lineWidth: lineWidthBeforeScale,
            lineHeight: lineHeightBeforeScale,
            data: data,
            pageData: pageData,
          );
        } else {
          canvas.translate(-textPainter.width, 0);

          if (pageData.quranPage?.pageNumber == 2 &&
              line.type == LineType.bismillah) {
            canvas.save();

            final centerVertical = line.height / 2;

            canvas.translate(textPainter.width, centerVertical);
            canvas.scale(.65, .65);
            canvas.translate(-textPainter.width, -centerVertical);

            textPainter.paint(
              canvas,
              const Offset(0, 0),
            );

            canvas.restore();
          } else {
            if (WordImageRenderHelper.preRenderedWordIds.contains(word.id) &&
                Platform.isIOS) {
              final path = 'assets/images/${word.id}.png';
              final wordImage = WordImageRenderHelper.getWord(path);
              if (wordImage != null) {
                canvas.save();
                canvas.scale(
                  textPainter.width / wordImage.width,
                  textPainter.height / wordImage.height,
                );
                textPainter.text?.style?.color;
                canvas.drawImage(
                  wordImage,
                  const Offset(0, 0),
                  Paint()
                    ..colorFilter = ColorFilter.mode(
                      textPainter.text?.style?.color ??
                          ColorResource.textDefault(data.isDarkMode),
                      BlendMode.srcATop,
                    ),
                );
                canvas.restore();
              } else {
                textPainter.paint(
                  canvas,
                  const Offset(0, 0),
                );
              }
            } else {
              textPainter.paint(
                canvas,
                const Offset(0, 0),
              );
            }
          }

          currentWordLeft -= textPainter.width;
          currentWordWidth = textPainter.width;
        }

        final newWordTapRect = Rect.fromLTWH(
          currentWordLeft,
          initialTop / pageData.textScale + currentLineTop,
          currentWordWidth,
          line.height,
        ).toScaledRect(pageData.textScale);
        wordHighlightRects.add(
          WordHighlightData(
            word: word,
            rect: RectData.fromRect(newWordTapRect),
          ),
        );
      }

      canvas.restore();

      canvas.translate(0, line.height);
      currentLineTop += line.height;
    }

    canvas.restore();

    return wordHighlightRects;
  }

  static void drawVerseNumber({
    required Canvas canvas,
    required Size canvasSize,
    required QuranWord word,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
  }) {
    drawVerseNumberImage(
        canvas: canvas, word: word, data: data, pageData: pageData);
    drawVerseNumberText(canvas, word, pageData);
  }

  static void drawVerseNumberImage({
    required Canvas canvas,
    required QuranWord word,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
  }) {
    canvas.save();

    final textWidth = word.verseNumberSize ?? pageData.quranTextPainter.width;
    final textHeight = pageData.quranTextPainter.height;

    final designCode = data.imageAsset.design.code;
    final additionalScale = (designCode == MushafCode.qaloon ||
            designCode == MushafCode.shubah ||
            designCode == MushafCode.warsh)
        ? .75
        : 1;
    final imageScale =
        textWidth * additionalScale / data.imageAsset.verseNumberImage.width;
    canvas.scale(imageScale, imageScale);

    // divide by scale to get the line height without scaling
    final textVerticalCenter = textHeight * pageData.lineScale / imageScale / 2;
    final imageVerticalCenter = data.imageAsset.verseNumberImage.height / 2;
    final imageCenterVertical = textVerticalCenter - imageVerticalCenter;

    final textHorizontalCenter = textWidth / imageScale / 2;
    final imageHorizontalCenter = data.imageAsset.verseNumberImage.width / 2;
    final imageCenterHorizontal = textHorizontalCenter - imageHorizontalCenter;

    final paint = Paint();
    if (data.isDarkMode) {
      paint.color = paint.color.withOpacity(.5);
    }

    canvas.drawImage(
      data.imageAsset.verseNumberImage,
      Offset(imageCenterHorizontal, imageCenterVertical),
      paint,
    );

    canvas.restore();
  }

  static void drawVerseNumberText(
      Canvas canvas, QuranWord word, QuranCanvasPageData pageData) {
    canvas.save();

    final width = word.verseNumberSize ?? pageData.quranTextPainter.width;
    final height = pageData.quranTextPainter.height;

    const textScale = .5;
    canvas.scale(textScale, textScale);

    pageData.verseNumberTextPainter
      ..text = TextSpan(
        text:
            NumberFormat('#', 'ar_EG').format(word.verseKey.parseVerseNumber()),
        style: pageData.verseNumberTextStyle,
      )
      ..layout();

    final textCenterHorizontal =
        width / textScale / 2 - pageData.verseNumberTextPainter.width / 2;
    final textCenterVertical = height * pageData.lineScale / textScale / 2 -
        pageData.verseNumberTextPainter.height / 2;

    pageData.verseNumberTextPainter.paint(
      canvas,
      Offset(textCenterHorizontal, textCenterVertical),
    );

    canvas.restore();
  }

  static void drawSurahHeader({
    required Canvas canvas,
    required Size canvasSize,
    required double lineWidth,
    required double lineHeight,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
  }) {
    final imageWidth = data.imageAsset.surahImage.width.toDouble();
    final imageHeight = imageWidth * .1;

    final imageScale = lineWidth / imageWidth;
    canvas.scale(imageScale, imageScale);

    paintImage(
      canvas: canvas,
      rect: Rect.fromLTWH(0, 0, imageWidth, imageHeight),
      fit: BoxFit.fill,
      image: data.imageAsset.surahImage,
      opacity: data.isDarkMode ? .5 : 1,
    );

    final exactLineHeight = lineHeight / imageScale;
    final textScale = exactLineHeight /
        pageData.surahTextPainter.height *
        data.surahHeaderTextScale;
    canvas.scale(textScale, textScale);

    final textLeft =
        (lineWidth / textScale / imageScale - pageData.surahTextPainter.width) /
            2;
    final textTop = (lineHeight / textScale / imageScale -
            pageData.surahTextPainter.height) /
        2;
    pageData.surahTextPainter.paint(canvas, Offset(textLeft, textTop));
  }

  static void drawMemorizationWord({
    required Canvas canvas,
    required RecitationAlignmentHelper recitationAlignmentHelper,
    required QuranMode quranMode,
    required QuranLine line,
    required QuranWord word,
    required int wordIndex,
    required double lineTop,
    required TextPainter textPainter,
    required QuranCanvasData data,
    required QuranCanvasPageData pageData,
  }) {
    final isQuranLineType = line.type == LineType.quran;
    final isInRange = recitationAlignmentHelper.isInRange(word.verseKey);
    final result = recitationAlignmentHelper.resultMap[word.id];
    final hideResult = (result == null || result.isHidden);
    final reveal =
        (quranMode is MemorizationQuranMode) ? quranMode.reveal : false;

    if (isQuranLineType && isInRange && hideResult && !reveal) {
      final wordWidth = textPainter.width;
      final dotWidth = pageData.dotTextPainter.width;

      if (result?.isCorrect == false) {
        pageData.dotTextPainter.text = TextSpan(
          text: '.',
          style: pageData.latinTextStyle.copyWith(color: Colors.red),
        );
      } else {
        pageData.dotTextPainter.text = TextSpan(
          text: '.',
          style: pageData.latinTextStyle,
        );
      }

      pageData.dotTextPainter.paint(
        canvas,
        Offset(wordWidth / 2 - dotWidth / 2, lineTop),
      );
    } else {
      if (WordImageRenderHelper.preRenderedWordIds.contains(word.id) &&
          Platform.isIOS) {
        final path = 'assets/images/${word.id}.png';
        final wordImage = WordImageRenderHelper.getWord(path);
        if (wordImage != null) {
          canvas.save();
          canvas.scale(
            textPainter.width / wordImage.width,
            textPainter.height / wordImage.height,
          );
          textPainter.text?.style?.color;
          canvas.drawImage(
            wordImage,
            Offset(0, lineTop),
            Paint()
              ..colorFilter = ColorFilter.mode(
                textPainter.text?.style?.color ??
                    ColorResource.textDefault(data.isDarkMode),
                BlendMode.srcATop,
              ),
          );
          canvas.restore();
        } else {
          textPainter.paint(
            canvas,
            Offset(0, lineTop),
          );
        }
      } else {
        textPainter.paint(
          canvas,
          Offset(0, lineTop),
        );
      }
    }
  }
}
