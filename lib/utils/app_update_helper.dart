import 'package:flutter/foundation.dart';
import 'package:upgrader/upgrader.dart';

class AppUpdateHelper {
  AppUpdateHelper._();

  static bool? _updateAvailable;

  static Future<bool> isUpdateAvailable() async {
    if (_updateAvailable != null) return _updateAvailable!;

    final upgrader = Upgrader(
      debugLogging: kDebugMode,
    );
    await upgrader.initialize();

    _updateAvailable = upgrader.isUpdateAvailable();
    return _updateAvailable!;
  }
}
