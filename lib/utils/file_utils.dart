import 'dart:io';

import 'package:path_provider/path_provider.dart';

class FileUtils {
  FileUtils._();

  static Future<File> moveFile(File sourceFile, String newPath) async {
    try {
      // prefer using rename as it is probably faster
      return await sourceFile.rename(newPath);
    } on FileSystemException catch (e) {
      // if rename fails, copy the source file and then delete it
      final newFile = await sourceFile.copy(newPath);
      await sourceFile.delete();
      return newFile;
    }
  }

  static Future<int> getDirSize(Directory dir) async {
    if (!await dir.exists()) return 0;

    var files = await dir.list(recursive: true).toList();
    var dirSize = files.fold(0, (int sum, file) => sum + file.statSync().size);
    return dirSize;
  }

  static int getFileSize(File file) {
    return file.statSync().size;
  }

  static Future<void> createDocumentDirectoryIfNotExists(String path) async {
    final dataDirectory = await getApplicationDocumentsDirectory();
    final directory = Directory('${dataDirectory.path}/$path');
    final directoryExists = await directory.exists();
    if (!directoryExists) {
      await directory.create(recursive: true);
    }
  }
}
