import 'dart:ui' as ui;

import 'package:flutter_svg/flutter_svg.dart';
import 'package:image/image.dart' as img;
import 'package:mushafi/exception/byte_data_conversion_exception.dart';
import 'package:mushafi/presentation/model/quran/mushaf_code.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran_canvas/frame_image.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_image_asset.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/image_color_mapper.dart';

class QuranImageHelper {
  const QuranImageHelper._();

  static Future<ui.Image> getFlutterImage(String assetPath,
      {ColorMapper? colorMapper}) async {
    final pictureInfo = await vg.loadPicture(
        SvgAssetLoader(assetPath, colorMapper: colorMapper), null);
    final flutterImage = await pictureInfo.picture.toImage(
      pictureInfo.size.width.round(),
      pictureInfo.size.height.round(),
    );

    return flutterImage;
  }

  static Future<FrameImage> getFrameImage(
    MushafCode designCode,
    String asset,
    bool isDark,
  ) async {
    final colorMapper = switch (designCode) {
      MushafCode.defaultCode => DefaultFrameColorMapper(
          ColorResource.defaultBorder1(isDark),
          ColorResource.defaultBorder2(isDark),
        ),
      MushafCode.medina => MedinaFrameColorMapper(
          ColorResource.medinaBorder1(isDark),
          ColorResource.medinaBorder2(isDark),
          ColorResource.medinaBorder3(isDark),
          ColorResource.medinaBorder4(isDark),
          ColorResource.medinaBorder5(isDark),
          ColorResource.medinaBorder6(isDark),
          ColorResource.medinaBorder7(isDark),
          ColorResource.medinaBorder8(isDark),
          ColorResource.medinaBorder9(isDark),
          ColorResource.medinaBorder10(isDark),
          ColorResource.medinaBorder11(isDark),
          ColorResource.medinaBorder12(isDark),
          ColorResource.medinaBorder13(isDark),
        ),
      MushafCode.oldMedina => OldMedinaFrameColorMapper(
          ColorResource.oldMedinaBorder1(isDark),
          ColorResource.oldMedinaBorder2(isDark),
          ColorResource.oldMedinaBorder3(isDark),
          ColorResource.oldMedinaBorder4(isDark),
          ColorResource.oldMedinaBorder5(isDark),
        ),
      MushafCode.qaloon => QaloonFrameColorMapper(
          ColorResource.qaloonBorder1(isDark),
          ColorResource.qaloonBorder2(isDark),
          ColorResource.qaloonBorder3(isDark),
          ColorResource.qaloonBorder4(isDark),
          ColorResource.qaloonBorder5(isDark),
          ColorResource.qaloonBorder6(isDark),
        ),
      MushafCode.mediumMedina => MediumMedinaFrameColorMapper(
          ColorResource.mediumMedinaBorder1(isDark),
          ColorResource.mediumMedinaBorder2(isDark),
          ColorResource.mediumMedinaBorder3(isDark),
          ColorResource.mediumMedinaBorder4(isDark),
          ColorResource.mediumMedinaBorder5(isDark),
        ),
      MushafCode.shubah => ShubahFrameColorMapper(
          ColorResource.shubahBorder1(isDark),
          ColorResource.shubahBorder2(isDark),
          ColorResource.shubahBorder3(isDark),
          ColorResource.shubahBorder4(isDark),
          ColorResource.shubahBorder5(isDark),
          ColorResource.shubahBorder6(isDark),
        ),
      MushafCode.warsh => WarshFrameColorMapper(
          ColorResource.warshBorder1(isDark),
          ColorResource.warshBorder2(isDark),
          ColorResource.warshBorder3(isDark),
          ColorResource.warshBorder4(isDark),
          ColorResource.warshBorder5(isDark),
          ColorResource.warshBorder6(isDark),
          ColorResource.warshBorder7(isDark),
          ColorResource.warshBorder8(isDark),
          ColorResource.warshBorder9(isDark),
          ColorResource.warshBorder10(isDark),
          ColorResource.warshBorder11(isDark),
          ColorResource.warshBorder12(isDark),
          ColorResource.warshBorder13(isDark),
          ColorResource.warshBorder14(isDark),
        ),
    };

    final flutterImage = await getFlutterImage(asset, colorMapper: colorMapper);
    final imageByteData =
        await flutterImage.toByteData(format: ui.ImageByteFormat.rawRgba);

    // another alternative is to use sealed class with 3 states: loading, success, error
    // stream is needed with this approach instead of future
    // future can be used instead of stream if the loading state omitted
    // the returned value will be something like this: Stream<DataState<FrameImage>>
    if (imageByteData == null) {
      throw ByteDataConversionException("Failed to convert image to byte data");
    }

    final libraryImage = img.Image.fromBytes(
      width: flutterImage.width,
      height: flutterImage.height,
      bytes: imageByteData.buffer,
    );

    return FrameImage(flutterImage, libraryImage);
  }

  static Future<ui.Image> getVerseNumberImage(
    MushafCode designCode,
    String assetPath,
    bool isDark,
  ) async {
    final colorMapper = switch (designCode) {
      MushafCode.defaultCode => DefaultVerseColorMapper(
          ColorResource.defaultVerse1(isDark),
          ColorResource.defaultVerse2(isDark),
          ColorResource.defaultVerse3(isDark),
        ),
      MushafCode.medina => MedinaVerseColorMapper(
          ColorResource.medinaVerse1(isDark),
          ColorResource.medinaVerse2(isDark),
          ColorResource.medinaVerse3(isDark),
        ),
      MushafCode.oldMedina => OldMedinaVerseColorMapper(
          ColorResource.oldMedinaVerse1(isDark),
          ColorResource.oldMedinaVerse2(isDark),
          ColorResource.oldMedinaVerse3(isDark),
        ),
      MushafCode.qaloon => QaloonVerseColorMapper(
          ColorResource.qaloonVerse1(isDark),
          ColorResource.qaloonVerse2(isDark),
          ColorResource.qaloonVerse3(isDark),
          ColorResource.qaloonVerse4(isDark),
        ),
      MushafCode.mediumMedina => MediumMedinaVerseColorMapper(
          ColorResource.mediumMedinaVerse1(isDark),
          ColorResource.mediumMedinaVerse2(isDark),
          ColorResource.mediumMedinaVerse3(isDark),
        ),
      MushafCode.shubah => ShubahVerseColorMapper(
          ColorResource.shubahVerse1(isDark),
          ColorResource.shubahVerse2(isDark),
          ColorResource.shubahVerse3(isDark),
        ),
      MushafCode.warsh => WarshVerseColorMapper(
          ColorResource.warshVerse1(isDark),
          ColorResource.warshVerse2(isDark),
          ColorResource.warshVerse3(isDark),
          ColorResource.warshVerse4(isDark),
          ColorResource.warshVerse5(isDark),
          ColorResource.warshVerse6(isDark),
        ),
    };

    final flutterImage =
        await getFlutterImage(assetPath, colorMapper: colorMapper);
    return flutterImage;
  }

  static Future<ui.Image> getSurahImage(
    MushafCode designCode,
    String assetPath,
    bool isDark,
  ) async {
    final colorMapper = switch (designCode) {
      MushafCode.defaultCode => DefaultSurahColorMapper(
          ColorResource.defaultSurah1(isDark),
          ColorResource.defaultSurah2(isDark),
          ColorResource.defaultSurah3(isDark),
        ),
      MushafCode.medina => MedinaSurahColorMapper(
          ColorResource.medinaSurah1(isDark),
          ColorResource.medinaSurah2(isDark),
          ColorResource.medinaSurah3(isDark),
          ColorResource.medinaSurah4(isDark),
          ColorResource.medinaSurah5(isDark),
          ColorResource.medinaSurah6(isDark),
        ),
      MushafCode.oldMedina => OldMedinaSurahColorMapper(
          ColorResource.oldMedinaSurah1(false),
          ColorResource.oldMedinaSurah2(false),
          ColorResource.oldMedinaSurah3(false),
          ColorResource.oldMedinaSurah4(false),
          ColorResource.oldMedinaSurah5(false),
          ColorResource.oldMedinaSurah6(false),
          ColorResource.oldMedinaSurah7(false),
          ColorResource.oldMedinaSurah8(false),
          ColorResource.oldMedinaSurah9(isDark),
        ),
      MushafCode.qaloon => QaloonSurahColorMapper(
          ColorResource.qaloonSurah1(isDark),
          ColorResource.qaloonSurah2(isDark),
          ColorResource.qaloonSurah3(isDark),
          ColorResource.qaloonSurah4(isDark),
          ColorResource.qaloonSurah5(isDark),
          ColorResource.qaloonSurah6(isDark),
          ColorResource.qaloonSurah7(isDark),
          ColorResource.qaloonSurah8(isDark),
          ColorResource.qaloonSurah9(isDark),
          ColorResource.qaloonSurah10(isDark),
          ColorResource.qaloonSurah11(isDark),
          ColorResource.qaloonSurah12(isDark),
          ColorResource.qaloonSurah13(isDark),
          ColorResource.qaloonSurah14(isDark),
          ColorResource.qaloonSurah15(isDark),
          ColorResource.qaloonSurah16(isDark),
          ColorResource.qaloonSurah17(isDark),
        ),
      MushafCode.mediumMedina => MediumMedinaSurahColorMapper(
          ColorResource.mediumMedinaSurah1(isDark),
          ColorResource.mediumMedinaSurah2(isDark),
          ColorResource.mediumMedinaSurah3(false),
          ColorResource.mediumMedinaSurah4(isDark),
          ColorResource.mediumMedinaSurah5(false),
        ),
      MushafCode.shubah => ShubahSurahColorMapper(
          ColorResource.shubahSurah1(isDark),
          ColorResource.shubahSurah2(isDark),
          ColorResource.shubahSurah3(isDark),
          ColorResource.shubahSurah4(isDark),
          ColorResource.shubahSurah5(isDark),
          ColorResource.shubahSurah6(isDark),
        ),
      MushafCode.warsh => WarshSurahColorMapper(
          ColorResource.warshSurah1(isDark),
          ColorResource.warshSurah2(isDark),
          ColorResource.warshSurah3(isDark),
          ColorResource.warshSurah4(isDark),
          ColorResource.warshSurah5(isDark),
          ColorResource.warshSurah6(isDark),
          ColorResource.warshSurah7(isDark),
          ColorResource.warshSurah8(isDark),
          ColorResource.warshSurah9(isDark),
          ColorResource.warshSurah10(isDark),
          ColorResource.warshSurah11(isDark),
          ColorResource.warshSurah12(isDark),
          ColorResource.warshSurah13(isDark),
          ColorResource.warshSurah14(isDark),
          ColorResource.warshSurah15(isDark),
          ColorResource.warshSurah16(isDark),
          ColorResource.warshSurah17(isDark),
        ),
    };

    final flutterImage =
        await getFlutterImage(assetPath, colorMapper: colorMapper);
    return flutterImage;
  }

  static Future<QuranImageAsset> getQuranThemeAsset(
    MushafDesign design,
    bool isDarkMode, {
    bool loadFrameImage = true,
  }) async {
    final frameImage = (loadFrameImage)
        ? await getFrameImage(design.code, design.frameAsset, isDarkMode)
        : null;

    final verseNumberImage =
        await getVerseNumberImage(design.code, design.verseAsset, isDarkMode);
    final surahImage =
        await getSurahImage(design.code, design.surahAsset, isDarkMode);

    return QuranImageAsset(design, frameImage, verseNumberImage, surahImage);
  }
}
