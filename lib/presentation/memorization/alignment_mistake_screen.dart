import 'dart:ui' as ui;

import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart' as intl;
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/int_extension.dart';
import 'package:mushafi/extension/integer_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/quran/alignment_word_result.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/track/track_range.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/common/verse_key_card.dart';

class AlignmentMistakeScreen extends ConsumerStatefulWidget {
  final int trackId;
  final TrackRange range;

  const AlignmentMistakeScreen(
      {super.key, required this.trackId, required this.range});

  @override
  ConsumerState<AlignmentMistakeScreen> createState() =>
      _AlignmentMistakeScreenState();
}

class _AlignmentMistakeScreenState
    extends ConsumerState<AlignmentMistakeScreen> {
  List<Surah> surahList = [];
  List<AlignmentWordResult> mistakeResultList = [];
  Map<String, List<AlignmentWordResult>> mistakesPerVerseKey = {};
  int mistakeCount = 0;

  DateTime currentDateFilter = DateTime.now();
  late DateTime minDateFilter = DateTime(
    currentDateFilter.year,
    currentDateFilter.month,
    currentDateFilter.day,
  );
  late DateTime maxDateFilter = DateTime(
    currentDateFilter.year,
    currentDateFilter.month,
    currentDateFilter.day,
  );

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final jsonDataRepository = ref.read(jsonDataRepositoryProvider);
      surahList = await jsonDataRepository.getSurahList();

      final alignmentRepository = ref.read(alignmentRepositoryProvider);
      final alignmentResultList =
          await alignmentRepository.getAlignmentWordResults(widget.trackId);
      mistakeResultList =
          alignmentResultList.where((result) => !result.isCorrect).toList();

      for (final result in mistakeResultList) {
        final resultDateTime = DateTime.fromMillisecondsSinceEpoch(result.time);
        final resultDate = DateTime(
            resultDateTime.year, resultDateTime.month, resultDateTime.day);

        if (resultDate.isBefore(minDateFilter)) {
          minDateFilter = resultDate;
        }

        if (resultDate.isAfter(maxDateFilter)) {
          maxDateFilter = resultDate;
        }
      }

      filterByDate(DateTime.now());
    });

    super.initState();
  }

  void filterByDate(DateTime filterDate) {
    currentDateFilter =
        DateTime(filterDate.year, filterDate.month, filterDate.day);

    final filteredIterable = mistakeResultList.where((result) {
      final resultDateTime = DateTime.fromMillisecondsSinceEpoch(result.time);
      final resultDate = DateTime(
          resultDateTime.year, resultDateTime.month, resultDateTime.day);
      return resultDate == currentDateFilter && !result.isCorrect;
    });
    mistakeCount = filteredIterable.length;

    setState(() {
      mistakesPerVerseKey = filteredIterable.groupListsBy((result) {
        return result.verseKey;
      });
    });
  }

  void addDateFilterDay() {
    final filterDate = currentDateFilter.add(const Duration(days: 1));
    filterByDate(filterDate);
  }

  void subtractDateFilterDay() {
    final filterDate = currentDateFilter.subtract(const Duration(days: 1));
    filterByDate(filterDate);
  }

  String getDateFilterText() {
    final dateTimeNow = DateTime.now();
    final today =
        DateTime(dateTimeNow.year, dateTimeNow.month, dateTimeNow.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final tomorrow = today.add(const Duration(days: 1));

    if (currentDateFilter == today) {
      return context.tr('date.today');
    }

    if (currentDateFilter == yesterday) {
      return context.tr('date.yesterday');
    }

    if (currentDateFilter == tomorrow) {
      return context.tr('date.tomorrow');
    }

    final dateFormat = intl.DateFormat("EEE, MMM d yyyy");
    return dateFormat.format(currentDateFilter);
  }

  bool canSubtractDateFilter() {
    return currentDateFilter.isAfter(minDateFilter);
  }

  bool canAddDateFilter() {
    return currentDateFilter.isBefore(maxDateFilter);
  }

  @override
  Widget build(BuildContext context) {
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final design = mushafDesignAsync.value?.design;
    final drawerColor = design?.drawer1Color ??
        MushafDesign.getDefault(context.isDarkMode).drawer1Color;
    final backgroundColor = design?.backgroundColor ??
        MushafDesign.getDefault(context.isDarkMode).backgroundColor;

    return Scaffold(
      backgroundColor: drawerColor,
      appBar: buildAppBar(context, drawerColor),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            children: [
              buildDateChooser(),
              const SizedBox(height: 16),
              buildInfoHeader(backgroundColor),
              const SizedBox(height: 8),
              if (mistakesPerVerseKey.isEmpty)
                const SizedBox()
              else
                buildMistakeList(backgroundColor),
            ],
          ),
        ),
      ),
    );
  }

  AppBar buildAppBar(BuildContext context, Color drawerColor) {
    return AppBar(
      backgroundColor: drawerColor,
      leading: IconButton(
        onPressed: () {
          Navigator.pop(context);
        },
        icon: SvgPicture.asset(
          Assets.svgsIcCloseCircle,
          width: 24,
          height: 24,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
      ),
      title: Text(
        context.tr('memorization.your_mistakes_title'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }

  Widget buildDateChooser() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return Row(
      children: [
        InkWell(
          onTap: () {
            if (canSubtractDateFilter()) {
              subtractDateFilterDay();
            }
          },
          child: Opacity(
            opacity: canSubtractDateFilter() ? 1 : .5,
            child: SvgPicture.asset(
              (isRtl)
                  ? Assets.svgsIcChevronRight24px
                  : Assets.svgsIcChevronLeft24px,
              width: 24,
              height: 24,
              fit: BoxFit.fill,
            ),
          ),
        ),
        Expanded(
          child: Text(
            textAlign: TextAlign.center,
            getDateFilterText(),
            style: TextStyle(
                fontSize: 16,
                color: ColorResource.textDefault(context.isDarkMode)),
          ),
        ),
        InkWell(
          onTap: () {
            if (canAddDateFilter()) {
              addDateFilterDay();
            }
          },
          child: Opacity(
            opacity: canAddDateFilter() ? 1 : .5,
            child: SvgPicture.asset(
              (isRtl)
                  ? Assets.svgsIcChevronLeft24px
                  : Assets.svgsIcChevronRight24px,
              width: 24,
              height: 24,
              fit: BoxFit.none,
            ),
          ),
        ),
      ],
    );
  }

  Widget buildInfoHeader(Color backgroundColor) {
    final startSurahName = (context.isArabic)
        ? surahList
            .elementAtOrNull(widget.range.startSurahNumber - 1)
            ?.nameArabic
        : surahList
            .elementAtOrNull(widget.range.startSurahNumber - 1)
            ?.nameComplex;
    final endSurahName = (context.isArabic)
        ? surahList.elementAtOrNull(widget.range.endSurahNumber - 1)?.nameArabic
        : surahList
            .elementAtOrNull(widget.range.endSurahNumber - 1)
            ?.nameComplex;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: backgroundColor,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('memorization.start'),
                        style: TextStyle(
                            color: ColorResource.textGrey(context.isDarkMode)),
                      ),
                      Text(
                        context.tr(
                          'memorization.surah_verse',
                          args: [
                            startSurahName ?? '',
                            widget.range.startVerseNumber
                                .toLocaleString(context.locale),
                          ],
                        ),
                        style: TextStyle(
                          color: ColorResource.textDefault(context.isDarkMode),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('memorization.end'),
                        style: TextStyle(
                            color: ColorResource.textGrey(context.isDarkMode)),
                      ),
                      Text(
                        context.tr(
                          'memorization.surah_verse',
                          args: [
                            endSurahName ?? '',
                            widget.range.endVerseNumber
                                .toLocaleString(context.locale),
                          ],
                        ),
                        style: TextStyle(
                          color: ColorResource.textDefault(context.isDarkMode),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
            const SizedBox(height: 16),
            Text(
              context.tr('memorization.your_mistake'),
              style:
                  TextStyle(color: ColorResource.textGrey(context.isDarkMode)),
            ),
            Text(
              (mistakeCount > 1)
                  ? context.tr(
                      'memorization.mistake_count_plural',
                      args: [mistakeCount.toLocaleString(context.locale)],
                    )
                  : context.tr(
                      'memorization.mistake_count',
                      args: [mistakeCount.toLocaleString(context.locale)],
                    ),
              style: TextStyle(
                color: ColorResource.danger(context.isDarkMode),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildMistakeList(Color backgroundColor) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: backgroundColor,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: ListView.separated(
          shrinkWrap: true,
          itemCount: mistakesPerVerseKey.length,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final entry = mistakesPerVerseKey.entries.toList()[index];
            return buildMistakeItem(entry);
          },
          separatorBuilder: (BuildContext context, int index) {
            return const Divider(thickness: .2);
          },
        ),
      ),
    );
  }

  Widget buildMistakeItem(MapEntry<String, List<AlignmentWordResult>> entry) {
    final mistakeValuesLength = entry.value.length;
    final mistakeCodeV1Iterable = entry.value.map((value) => value.codeV1);
    final firstResult = entry.value.first;
    final splitVerseCodeV1 = firstResult.verseCodeV1.split(',');
    final latestTime = maxBy(entry.value, (result) => result.time);
    final latestDateTime =
        DateTime.fromMillisecondsSinceEpoch(latestTime!.time);
    final latestDateFormat = intl.DateFormat("EEE MMM d yyyy, HH:mm:ss");

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            VerseKeyCard(verseKey: entry.key),
            const SizedBox(width: 16),
            Text(
              (mistakeValuesLength > 1)
                  ? context.tr('memorization.mistake_count_plural', args: [
                      mistakeValuesLength.toLocaleString(context.locale)
                    ])
                  : context.tr(
                      'memorization.mistake_count',
                      args: [
                        mistakeValuesLength.toLocaleString(context.locale)
                      ],
                    ),
              style: TextStyle(
                  fontSize: 12,
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            const Spacer(),
            Text(
              latestDateFormat.format(latestDateTime),
              style: TextStyle(
                  fontSize: 12,
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
          ],
        ),
        RichText(
          textDirection: ui.TextDirection.rtl,
          text: TextSpan(
            children: List.generate(splitVerseCodeV1.length, (index) {
              final codeV1 = splitVerseCodeV1[index];
              final color = (mistakeCodeV1Iterable.contains(codeV1))
                  ? ColorResource.danger(context.isDarkMode)
                  : ColorResource.textDefault(context.isDarkMode);
              return TextSpan(
                text: codeV1,
                style: TextStyle(
                  fontSize: 24,
                  fontFamily: firstResult.pageNumber.threeDigitsFormat(),
                  color: color,
                ),
              );
            }),
          ),
        ),
      ],
    );
  }
}
