import 'dart:io';
import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/integer_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/ai/asr_model_type.dart';
import 'package:mushafi/presentation/model/ai/cond_model_type.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/inference_init_manager.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:mushafi/utils/model_fetch_manager.dart';
import 'package:mushafi/utils/word_image_render_helper.dart';
import 'package:mushafi/widget/common/verse_key_card.dart';
import 'package:permission_handler/permission_handler.dart';

class VoiceSearchScreen extends ConsumerStatefulWidget {
  const VoiceSearchScreen({super.key});

  @override
  ConsumerState<VoiceSearchScreen> createState() => _VoiceSearchScreenState();
}

class _VoiceSearchScreenState extends ConsumerState<VoiceSearchScreen>
    with SingleTickerProviderStateMixin {
  InferencePipeline get inferencePipeline => InferencePipeline.getInstance();
  late final AnimationController micAnimationController;

  List<Surah> surahList = [];
  String title = "";
  String transcription = "";
  bool isSpeech = false;
  List<QuranVerse> verseList = [];
  bool isFetchingModels = false;

  Future<void> fetchModels(InferenceModel model) async {
    isFetchingModels = true;
    if (mounted) {
      setState(() {});
    }

    if (ModelFetchManager.instance.isFetching) {
      await ModelFetchManager.instance.waitForFetchOperation();
    } else {
      await ModelFetchManager.instance.runFetchOperation(model);
    }

    isFetchingModels = false;
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> setupInferencePipeline() async {
    if (!inferencePipeline.isInitialized && !inferencePipeline.isInitializing) {
      if (mounted) {
        final modelVersion = PreferenceStorage.getLatestTestedModelVersion();
        if (modelVersion == null) return;

        final model = InferenceModel.fromType(
          AsrModelType.quantized,
          CondModelType.quantized,
          PreferenceStorage.getSelectedModel(),
          modelVersion,
        );

        final modelsExist =
            await ref.read(minioDataRepositoryProvider).doModelsExist(model);
        if (!modelsExist) {
          await fetchModels(model);
        }

        final success = (InferenceInitManager.instance.isInit)
            ? await InferenceInitManager.instance.waitForInitOperation()
            : await InferenceInitManager.instance.runInitOperation(model, () {
                return fetchModels(model);
              });

        if (!success && mounted) {
          CommonUtils.showToast('Failed to initialize inference pipeline');
        }

        if (mounted) {
          setState(() {});
        }
      }
    }
  }

  Future<void> resetInferencePipeline() async {
    InferencePipeline.getInstance()
      ..stop()
      ..delete();

    await Future.delayed(const Duration(seconds: 1));

    await setupInferencePipeline();
  }

  @override
  void initState() {
    FirebaseAnalytics.instance.logScreenView(screenName: "voice_search");

    micAnimationController = AnimationController(vsync: this);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        title = context.tr('voice_search.hold_microphone');
      });

      final jsonDataRepository = ref.read(jsonDataRepositoryProvider);
      surahList = await jsonDataRepository.getSurahList();
    });

    Permission.microphone.request().then((status) async {
      if (status.isGranted) {
        setupInferencePipeline();
      } else if (status.isDenied || status.isPermanentlyDenied) {
        showNeedMicrophonePermissionToast();
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    micAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (!Platform.isAndroid)
            Text(
              (isFetchingModels)
                  ? context.tr('voice_search.downloading')
                  : (inferencePipeline.isInitializing)
                      ? context.tr('voice_search.initializing')
                      : title,
              style: TextStyle(
                  fontSize: 14,
                  color: ColorResource.textGrey(context.isDarkMode)),
              textAlign: TextAlign.center,
            ),
          Text(
            transcription,
            style: TextStyle(
              fontSize: 32,
              color: ColorResource.textDefault(context.isDarkMode),
              fontFamily: 'NotoNaskhArabic',
            ),
            textDirection: ui.TextDirection.rtl,
            textAlign: TextAlign.center,
          ),
          Expanded(
            child: (verseList.isNotEmpty)
                ? buildVerseResultList()
                : const SizedBox(),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                if (!isFetchingModels && !inferencePipeline.isInitializing) ...[
                  buildMicButton(context),
                  const SizedBox(height: 8),
                ],
                if (Platform.isAndroid)
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      context.tr('voice_search.back_to_home'),
                      style: TextStyle(
                          color: ColorResource.textGrey(context.isDarkMode)),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  AppBar buildAppBar() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      leading: (Platform.isAndroid)
          ? Container()
          : IconButton(
              icon: SvgPicture.asset(
                (isRtl)
                    ? Assets.svgsIcChevronRight24px
                    : Assets.svgsIcChevronLeft24px,
                colorFilter: ColorFilter.mode(
                  ColorResource.textDefault(context.isDarkMode),
                  BlendMode.srcIn,
                ),
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
      title: Text(
        (Platform.isAndroid)
            ? (isFetchingModels)
                ? context.tr('voice_search.downloading')
                : (inferencePipeline.isInitializing)
                    ? context.tr('voice_search.initializing')
                    : title
            : context.tr('voice_search.title'),
        style: TextStyle(
            fontSize: 14, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
      surfaceTintColor: Colors.transparent,
      scrolledUnderElevation: 0,
      forceMaterialTransparency: true,
    );
  }

  Widget buildVerseResultItem(
      QuranVerse verse, String wordsCodeV1, Surah surah) {
    return InkWell(
      onTap: () {
        Navigator.pop(context, verse);
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25.0),
                color: ColorResource.defaultHighlightFaded(context.isDarkMode),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      (context.isArabic) ? surah.nameArabic : surah.nameComplex,
                      style: TextStyle(
                          color: ColorResource.textDefault(context.isDarkMode)),
                    ),
                    const SizedBox(width: 4),
                    VerseKeyCard(
                        verseKey: verse.verseKey, size: VerseKeySize.small),
                  ],
                ),
              ),
            ),
            RichText(
              textDirection: ui.TextDirection.rtl,
              text: TextSpan(
                style: TextStyle(
                  fontSize: 24,
                  fontFamily: verse.pageId.threeDigitsFormat(),
                  color: ColorResource.textDefault(context.isDarkMode),
                ),
                children: verse.words.map((word) {
                  final preRenderedWordIds =
                      WordImageRenderHelper.preRenderedWordIds;
                  if (preRenderedWordIds.contains(word.id) && Platform.isIOS) {
                    final quranTextStyle = TextStyle(
                      fontSize: 24,
                      color: ColorResource.textDefault(context.isDarkMode),
                      fontFamily: verse.pageId.threeDigitsFormat(),
                    );

                    final quranTextPainter = TextPainter(
                      textDirection: ui.TextDirection.ltr,
                      text: TextSpan(text: word.codeV1, style: quranTextStyle),
                    )..layout();

                    final path = 'assets/images/${word.id}.png';

                    return WidgetSpan(
                      child: Image.asset(
                        path,
                        width: quranTextPainter.width,
                        height: quranTextPainter.height,
                        color: ColorResource.textDefault(context.isDarkMode),
                        colorBlendMode: BlendMode.srcATop,
                      ),
                    );
                  }

                  return TextSpan(
                    text: word.codeV1,
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildVerseResultList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: verseList.length,
        itemBuilder: (context, index) {
          final verse = verseList[index];
          final wordsCodeV1 = verse.words.map((word) => word.codeV1).join();
          final surah = surahList.elementAtOrNull(verse.surahId - 1);

          return (surah != null)
              ? buildVerseResultItem(verse, wordsCodeV1, surah)
              : Container();
        },
      ),
    );
  }

  bool requestCancellingInference = false;

  void stopInference() {
    inferencePipeline.reset();
    inferencePipeline.stop();

    title = context.tr('voice_search.hold_microphone');

    if (mounted) {
      setState(() {});
    }
  }

  Widget buildMicButton(BuildContext context) {
    return GestureDetector(
      onTapDown: (details) {
        requestCancellingInference = false;

        setState(() {
          title = context.tr('voice_search.loading');
          transcription = '';
          verseList = [];
        });

        WidgetsBinding.instance.addPostFrameCallback((_) async {
          final permissionStatus = await Permission.microphone.status;
          if (permissionStatus.isGranted) {
            await setupInferencePipeline();

            if (context.mounted) {
              if (requestCancellingInference) {
                stopInference();
              } else {
                bool success = false;
                success = runVerseRecognition();

                if (!success) {
                  int retryCount = 1;
                  while (retryCount < 3) {
                    await resetInferencePipeline();

                    if (requestCancellingInference) {
                      stopInference();
                      setState(() {});
                      return;
                    }

                    success = runVerseRecognition();
                    if (success) break;
                    retryCount++;
                  }
                }

                if (requestCancellingInference) {
                  stopInference();
                  setState(() {});
                  return;
                }

                if (!success) {
                  if (context.mounted) {
                    title = context.tr('voice_search.hold_microphone');
                  }
                  showAlignmentFailedToast();
                  setState(() {});
                  return;
                }

                if (context.mounted) {
                  title = context.tr('voice_search.begin_reciting');
                }
                HapticFeedback.vibrate();
              }

              setState(() {});
            }
          } else if (permissionStatus.isDenied ||
              permissionStatus.isPermanentlyDenied) {
            showNeedMicrophonePermissionToast();

            if (context.mounted) {
              title = context.tr('voice_search.hold_microphone');
            }
          }
        });
      },
      onTapUp: (details) {
        stopInference();
        requestCancellingInference = true;
      },
      child: Container(
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.green,
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 0),
              blurRadius: 8,
              spreadRadius: 1,
              color: Colors.black26,
            ),
          ],
        ),
        child: (inferencePipeline.isRunning)
            ? ColorFiltered(
                colorFilter: ColorFilter.mode(
                  ColorResource.onPrimary(context.isDarkMode),
                  BlendMode.srcATop,
                ),
                child: Lottie.asset(
                  width: 80,
                  height: 80,
                  Assets.jsonWaveAnimation,
                  controller: micAnimationController,
                  onLoaded: (composition) {
                    micAnimationController.duration = composition.duration;
                  },
                ),
              )
            : Padding(
                padding: const EdgeInsets.all(24.0),
                child: SvgPicture.asset(
                  Assets.svgsIcMicrophone,
                  width: 32,
                  height: 32,
                  colorFilter: ColorFilter.mode(
                    ColorResource.onPrimary(context.isDarkMode),
                    BlendMode.srcIn,
                  ),
                ),
              ),
      ),
    );
  }

  bool runVerseRecognition() {
    inferencePipeline.stop();
    return inferencePipeline.recognizeVerse(
      onListening: (isListening) {},
      onSpeech: (isSpeech) {
        if (isSpeech != this.isSpeech) {
          this.isSpeech = isSpeech;
          if (mounted) {
            if (isSpeech) {
              micAnimationController.forward();
            } else {
              micAnimationController.reset();
            }
          }
        }
      },
      onTranscription: (transcription) {
        this.transcription = transcription.replaceAll('@', '.');
        if (mounted) {
          setState(() {});
        }
        talker.debug("onTranscription: $transcription");
      },
      onMatchedVerses: (matchedVerses) async {
        if (mounted) {
          final quranRepository = ref.read(quranRepositoryProvider);
          final verseList =
              (await quranRepository.getQuranVerseByIdList(matchedVerses)) ??
                  [];
          this.verseList = verseList;
          if (mounted) {
            setState(() {});
          }
        }
        talker.debug("onMatchedVerses: $matchedVerses");
      },
    );
  }

  void showNeedMicrophonePermissionToast() {
    CommonUtils.showToast(
        context.tr('voice_search.microphone_permission_required'));
  }

  void showAlignmentFailedToast() {
    CommonUtils.showToast(context.tr('voice_search.alignment_failed'));
  }
}
