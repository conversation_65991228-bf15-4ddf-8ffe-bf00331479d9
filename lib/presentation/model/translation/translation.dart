class Translation {
  final String fileName;
  final String name;
  final String nativeTranslatedName;
  final String language;
  final double size;

  Translation({
    required this.fileName,
    required this.name,
    required this.nativeTranslatedName,
    required this.language,
    required this.size,
  });

  late final String key = fileName.replaceAll('.fb', '');
  late final String langCode = key.split("_")[0];

  factory Translation.fromCsvRow(List<dynamic> row) {
    return Translation(
      fileName: row[0],
      name: row[3],
      nativeTranslatedName: row[8],
      language: row[5],
      size: double.parse(row[9].toString()),
    );
  }

  factory Translation.fromJson(Map<String, dynamic> json) {
    return Translation(
      fileName: json['fileName'],
      name: json['name'],
      nativeTranslatedName: json['nativeTranslatedName'],
      language: json['language'],
      size: json['size'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'name': name,
      'nativeTranslatedName': nativeTranslatedName,
      'language': language,
      'size': size,
    };
  }

  Translation copyWith({
    String? fileName,
    String? name,
    String? nativeTranslatedName,
    String? language,
    double? size,
    bool? isDownloading,
    double? downloadProgress,
  }) {
    return Translation(
      fileName: fileName ?? this.fileName,
      name: name ?? this.name,
      nativeTranslatedName: nativeTranslatedName ?? this.nativeTranslatedName,
      language: language ?? this.language,
      size: size ?? this.size,
    );
  }
}
