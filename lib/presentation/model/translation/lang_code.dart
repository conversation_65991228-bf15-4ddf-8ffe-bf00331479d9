import 'package:collection/collection.dart';
import 'package:mushafi/generated/assets.dart';

enum LangCode {
  english("en", Assets.svgsIcFlagIreland),
  malay("ms", Assets.svgsIcFlagMalaysia),
  indonesian("id", Assets.svgsIcFlagIndonesia),
  arabic("ar", Assets.svgsIcFlagPalestine),
  uzbek("uz", Assets.svgsIcFlagUzbekistan),
  uyghur("ug", Assets.svgsIcFlagChina),
  turkish("tr", Assets.svgsIcFlagTurkey),
  thai("th", Assets.svgsIcFlagThailand),
  albanian("sq", Assets.svgsIcFlagAlbania),
  portuguese("pt", Assets.svgsIcFlagPortugal),
  dutch("nl", Assets.svgsIcFlagNetherlands),
  kurdish("ku", Assets.svgsIcFlagIraq),
  italian("it", Assets.svgsIcFlagItaly),
  french("fr", Assets.svgsIcFlagFrance),
  bengali("bn", Assets.svgsIcFlagBangladesh),
  azerbaijani("az", Assets.svgsIcFlagAzerbaijan),
  hindi("hi", Assets.svgsIcFlagIndia),
  somali("so", Assets.svgsIcFlagSomalia),
  finnish("fi", Assets.svgsIcFlagFinland),
  german("de", Assets.svgsIcFlagGermany),
  spanish("es", Assets.svgsIcFlagSpain),
  romanian("ro", Assets.svgsIcFlagRomania),
  persian("fa", Assets.svgsIcFlagIran),
  amharic("am", Assets.svgsIcFlagEthiopia),
  azeri("az", Assets.svgsIcFlagAzerbaijan),
  bosnian("bs", Assets.svgsIcFlagBosniaAndHerzegovina),
  czech("cs", Assets.svgsIcFlagCzechRepublic),
  divehi("dv", Assets.svgsIcFlagMaldives),
  japanese("ja", Assets.svgsIcFlagJapan),
  kannada("kn", Assets.svgsIcFlagIndia),
  korean("ko", Assets.svgsIcFlagSouthKorea),
  malayalam("ml", Assets.svgsIcFlagIndia),
  maranao("mn", Assets.svgsIcFlagPhilippines),
  norwegian("nb", Assets.svgsIcFlagNorway),
  polish("pl", Assets.svgsIcFlagPoland),
  russian("ru", Assets.svgsIcFlagRussianFederation),
  swahili("sw", Assets.svgsIcFlagKenya),
  swedish("sv", Assets.svgsIcFlagSweden),
  tajik("tg", Assets.svgsIcFlagTajikistan),
  tamil("ta", Assets.svgsIcFlagIndia),
  tatar("tt", Assets.svgsIcFlagRussianFederation),
  urdu("ur", Assets.svgsIcFlagPakistan);

  final String id;
  final String flagAsset;

  const LangCode(this.id, this.flagAsset);

  static LangCode? fromId(String id) {
    return LangCode.values.firstWhereOrNull((langCode) => langCode.id == id);
  }

  static LangCode? fromName(String name) {
    return LangCode.values.firstWhereOrNull((langCode) {
      final langCodeNameLowerCase = langCode.name.toLowerCase();
      final nameLowerCase = name.toLowerCase();
      return langCodeNameLowerCase == nameLowerCase ||
          nameLowerCase.split(', ').contains(langCodeNameLowerCase);
    });
  }
}
