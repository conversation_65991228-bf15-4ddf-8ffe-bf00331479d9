import 'dart:convert';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';

class TextDimension {
  double width = 0.0;
  double height = 0.0;
  double lineBreakWidth = 0.0;
  double lineBreakHeight = 0.0;

  TextDimension copyFrom(TextDimension other) {
    width = other.width;
    height = other.height;
    lineBreakWidth = other.lineBreakWidth;
    lineBreakHeight = other.lineBreakHeight;

    return this;
  }

  double getZoomedWidth(ZoomState zoomState) {
    if (zoomState == ZoomState.zoomedInLevel2) {
      return lineBreakWidth;
    } else {
      return width;
    }
  }

  double getZoomedHeight(ZoomState zoomState) {
    if (zoomState == ZoomState.zoomedInLevel2) {
      return lineBreakHeight;
    } else {
      return height;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
      'lineBreakWidth': lineBreakWidth,
      'lineBreakHeight': lineBreakHeight,
    };
  }

  static TextDimension fromJson(Map<String, dynamic> json) {
    return TextDimension()
      ..width = json['width']
      ..height = json['height']
      ..lineBreakWidth = json['lineBreakWidth']
      ..lineBreakHeight = json['lineBreakHeight'];
  }
}
