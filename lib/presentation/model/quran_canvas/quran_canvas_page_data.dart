import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:mushafi/extension/integer_extension.dart';
import 'package:mushafi/extension/juz_number_formatter.dart';
import 'package:mushafi/presentation/model/quran/juz_name_script.dart';
import 'package:mushafi/presentation/model/quran/quran_line.dart';
import 'package:mushafi/presentation/model/quran/quran_page.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/quran_canvas/serializable_quran_canvas_page_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/text_dimension.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/constants.dart';

// todo extend QuranCanvasData?
class QuranCanvasPageData {
  TextPainter? surahTextNavigationPainter;

  TextPainter? juzTextNavigationPainter;

  TextStyle surahTextStyle = const TextStyle(
    fontFamily: 'SurahNames',
    fontSize: quranTextSize,
    color: Colors.black,
  );

  final surahTextPainter = TextPainter(
    textDirection: ui.TextDirection.ltr,
  );

  TextStyle bismillahTextStyle = const TextStyle(
    fontFamily: 'QuranCalligraphy',
    fontSize: quranTextSize,
    color: Colors.black,
  );

  late final bismillahTextPainter = TextPainter(
    text: TextSpan(
      text: "#\"!",
      style: bismillahTextStyle,
    ),
    textDirection: ui.TextDirection.ltr,
  )..layout();

  TextStyle quranTextStyle = const TextStyle(
    fontSize: quranTextSize,
    color: Colors.black,
  );

  final quranTextPainter = TextPainter(
    textDirection: ui.TextDirection.ltr,
  );

  TextStyle verseNumberTextStyle = const TextStyle(
    fontFamily: 'product_sans_regular.ttf',
    fontSize: quranTextSize,
    color: Colors.black,
  );

  final verseNumberTextPainter = TextPainter(
    textDirection: ui.TextDirection.ltr,
  );

  TextStyle latinTextStyle = const TextStyle(
    fontFamily: 'Product Sans',
    fontSize: quranTextSize,
    color: Colors.black,
  );

  final latinTextPainter = TextPainter(
    textDirection: ui.TextDirection.ltr,
  );

  late final dotTextPainter = TextPainter(
    text: TextSpan(
      text: '.',
      style: latinTextStyle,
    ),
    textDirection: ui.TextDirection.ltr,
  )..layout();

  final globalFontVerseNumberScale = .75;
  double lineScale = .85;
  QuranPage? quranPage;
  ui.Picture? pagePicture;
  ui.Image? textImage;
  ui.Image? lineBreakTextImage;
  bool useCachedText = true;
  bool useCachedPage = true;
  double zoomInLevel1Scale = 1;

  final textDimension = TextDimension();
  double navigationTextScale = 1;
  double textScale = 1;

  Map<QuranWord, Rect> wordToHighlightRectMap = {};
  Map<QuranWord, Rect> wordToHighlightLineBreakRectMap = {};
  Rect? surahNavigationRect;
  Rect? juzNavigationRect;
  Rect? pageNumberRect;

  QuranCanvasPageData();

  factory QuranCanvasPageData.fromSerializedData(
      SerializableQuranCanvasPageData data) {
    final instance = QuranCanvasPageData();
    instance.initializeFromSerializedData(data);
    return instance;
  }

  Map<QuranWord, Rect> getWordRectMap(bool isLineBreak) =>
      (isLineBreak) ? wordToHighlightLineBreakRectMap : wordToHighlightRectMap;

  Rect? getWordRect(QuranWord? word, bool isLineBreak) => (isLineBreak)
      ? wordToHighlightLineBreakRectMap[word]
      : wordToHighlightRectMap[word];

  Rect? getFirstWordRectByVerseKey(String verseKey, bool isLineBreak) {
    Map<QuranWord, Rect> highlightRectMap = {};
    if (isLineBreak) {
      highlightRectMap = wordToHighlightLineBreakRectMap;
    } else {
      highlightRectMap = wordToHighlightRectMap;
    }

    for (final entry in highlightRectMap.entries.toList()) {
      if (entry.key.verseKey == verseKey) {
        return entry.value;
      }
    }

    return null;
  }

  void setQuranPage(
    QuranPage? quranPage,
    JuzNameScript juzNameScript,
    List<Surah> surahList,
    bool isDarkMode,
  ) {
    this.quranPage = quranPage;
    if (quranPage == null) {
      return;
    }

    lineScale = (quranPage.isGlobalFont) ? 1 : .85;

    pagePicture = null;

    if (textImage == null) {
      wordToHighlightRectMap.clear();
    }
    if (lineBreakTextImage == null) {
      wordToHighlightLineBreakRectMap.clear();
    }

    surahTextStyle = surahTextStyle.copyWith(
      color: ColorResource.textDefault(isDarkMode),
    );

    bismillahTextStyle = bismillahTextStyle.copyWith(
      color: ColorResource.textDefault(isDarkMode),
    );

    quranTextStyle = quranTextStyle.copyWith(
      fontFamily: quranPage.fontName,
      color: ColorResource.textDefault(isDarkMode),
    );

    verseNumberTextStyle = verseNumberTextStyle.copyWith(
      color: ColorResource.textDefault(isDarkMode),
    );

    latinTextStyle = latinTextStyle.copyWith(
      color: ColorResource.textDefault(isDarkMode),
    );

    final surahIndex = quranPage.surahNumber - 1;
    final surahText = switch (juzNameScript) {
      JuzNameScript.latin =>
        surahList.elementAtOrNull(surahIndex)?.nameComplex ?? '',
      JuzNameScript.arabic => quranPage.surahNumber.threeDigitsFormat(),
    };

    final surahFontFamily = switch (juzNameScript) {
      JuzNameScript.latin => null,
      JuzNameScript.arabic => 'SurahNames',
    };

    surahTextNavigationPainter = TextPainter(
      text: TextSpan(
        text: surahText,
        style: TextStyle(
          fontFamily: surahFontFamily,
          fontSize: quranTextSize,
          color: ColorResource.textDefault(isDarkMode),
          decoration: TextDecoration.underline,
          decorationStyle: TextDecorationStyle.dashed,
        ),
      ),
      textDirection: ui.TextDirection.ltr,
    )..layout();

    final juzText = switch (juzNameScript) {
      JuzNameScript.latin => 'Juz ${quranPage.juzNumber.toString()}',
      JuzNameScript.arabic => quranPage.juzNumber.toJuzNumberCharCode(),
    };

    final juzFontFamily = switch (juzNameScript) {
      JuzNameScript.latin => null,
      JuzNameScript.arabic => 'QuranCalligraphy',
    };

    juzTextNavigationPainter = TextPainter(
      text: TextSpan(
        text: juzText,
        style: TextStyle(
          fontFamily: juzFontFamily,
          fontSize: quranTextSize,
          color: ColorResource.textDefault(isDarkMode),
          decoration: TextDecoration.underline,
          decorationStyle: TextDecorationStyle.dashed,
        ),
      ),
      textDirection: ui.TextDirection.ltr,
    )..layout();
  }

  void updateNavigationTextScale({
    required double navigationHeight,
    required double navigationTextHeightScale,
  }) {
    final surahTextNavigationPainter = this.surahTextNavigationPainter;
    if (surahTextNavigationPainter == null) {
      return;
    }

    navigationTextScale = navigationHeight *
        navigationTextHeightScale /
        surahTextNavigationPainter.height;
  }

  void measureTextDimension(double lineScale, double surahHeaderHeightScale) {
    final quranPage = this.quranPage;
    if (quranPage == null) {
      return;
    }

    var textWidth = 0.0;
    var textHeight = 0.0;

    // measure width only
    for (final line in quranPage.lineList) {
      if (quranPage.isGlobalFont) {
        double totalWidth = 0;

        for (final (index, word) in line.words.indexed) {
          final textPainter = switch (line.type) {
            LineType.quran || LineType.bismillah => quranTextPainter
              ..text = TextSpan(
                text: word.getText(),
                style: quranTextStyle,
              )
              ..layout(),
            LineType.surahHeader => surahTextPainter
              ..text = TextSpan(text: word.getText(), style: surahTextStyle)
              ..layout(),
          };

          if (word.isVerseNumber) {
            final wordWidth =
                textPainter.height * lineScale * globalFontVerseNumberScale;
            line.words[index] = word.copyWith(verseNumberSize: wordWidth);
            totalWidth += wordWidth;
          } else {
            totalWidth += textPainter.width;
          }
        }

        // set the line width for the surah header later when measuring the height for efficiency
        if (line.type != LineType.surahHeader) {
          line.width = totalWidth;
        }

        textWidth = max(textWidth, totalWidth);
      } else {
        final textPainter = switch (line.type) {
          LineType.quran => quranTextPainter
            ..text = TextSpan(
              text: line.text,
              style: quranTextStyle,
            )
            ..layout(),
          LineType.bismillah => bismillahTextPainter,
          LineType.surahHeader => surahTextPainter
            ..text = TextSpan(text: line.text, style: surahTextStyle)
            ..layout(),
        };

        // set the line width for the surah header later when measuring the height for efficiency
        if (line.type != LineType.surahHeader) {
          line.width = textPainter.width;
        }

        textWidth = max(textWidth, textPainter.width);
      }
    }

    // input additional space to justify the text for the global font
    // exclude it from the page 1 and 2
    if (quranPage.isGlobalFont && !{1, 2}.contains(quranPage.pageNumber)) {
      for (final line in quranPage.lineList) {
        if (line.type == LineType.quran) {
          final remainingSpace = textWidth - line.width;
          final gaps = line.words.length - 1;
          line.additionalSpacePerGap = (gaps > 0) ? remainingSpace / gaps : 0;
        }
      }
    }

    // measure height and line dimension
    for (final line in quranPage.lineList) {
      switch (line.type) {
        case LineType.quran:
          quranTextPainter.text = TextSpan(
            text: line.text,
            style: quranTextStyle,
          );
          quranTextPainter.layout();

          line.height = quranTextPainter.height * lineScale;
        case LineType.bismillah:
          if (quranPage.isGlobalFont) {
            quranTextPainter.text = TextSpan(
              text: line.text,
              style: quranTextStyle,
            );
            quranTextPainter.layout();

            line.height = quranTextPainter.height * lineScale;
          } else {
            line.height = bismillahTextPainter.height * lineScale;
          }
        case LineType.surahHeader:
          line.width = textWidth;
          line.height = textWidth * surahHeaderHeightScale;
      }

      textHeight += line.height;
    }

    textDimension.width = textWidth;
    textDimension.height = textHeight;
  }

  void measureLineBreakTextDimension({
    required int frameHeight,
    required double frameScale,
    required double widthPaddingScale,
    required double lineBreakWidthPaddingScale,
    required double surahHeaderHeightScale,
    required double canvasWidth,
    required double lineScale,
  }) {
    final quranPage = this.quranPage;
    if (quranPage == null) {
      return;
    }

    final zoomInLevel2Scale =
        getZoomInLevel2Scale(canvasWidth, frameHeight, frameScale);
    final paddedCanvasWidth = canvasWidth * lineBreakWidthPaddingScale;
    final rightPaddedCanvasWidth =
        paddedCanvasWidth + (canvasWidth - paddedCanvasWidth) / 2;

    // tolerance the left padding so that the line will fit better
    final maxWidth = rightPaddedCanvasWidth;
    final totalScale = textScale * zoomInLevel2Scale;
    var totalWidth = 0.0;
    var textHeight = 0.0;
    double curWidth = 0;
    double curHeight = 0;

    final List<QuranLine> lineBreakList = [];
    String lineText = "";
    List<QuranWord> wordsInLine = [];
    LineType? lastLineType;
    var prevLineHeight = 0.0;

    for (final line in quranPage.lineList) {
      for (final word in line.words) {
        switch (line.type) {
          case LineType.quran:
            quranTextPainter.text = TextSpan(
              text: word.getText(),
              style: quranTextStyle,
            );
            quranTextPainter.layout();

            if (word.isVerseNumber && quranPage.isGlobalFont) {
              curWidth =
                  (word.verseNumberSize ?? quranTextPainter.width) * totalScale;
            } else {
              curWidth = quranTextPainter.width * totalScale;
            }
            curHeight = quranTextPainter.height * lineScale * totalScale;
          case LineType.bismillah:
            if (quranPage.isGlobalFont) {
              quranTextPainter.text = TextSpan(
                text: word.getText(),
                style: quranTextStyle,
              );
              quranTextPainter.layout();

              curWidth = quranTextPainter.width * totalScale;
              curHeight = quranTextPainter.height * lineScale * totalScale;
            } else {
              curWidth = bismillahTextPainter.width * totalScale;
              curHeight = bismillahTextPainter.height * lineScale * totalScale;
            }
          case LineType.surahHeader:
            curWidth = paddedCanvasWidth;
            curHeight = paddedCanvasWidth * surahHeaderHeightScale * totalScale;
        }

        if (textHeight == 0.0) {
          textHeight += curHeight;
        }

        if (totalWidth + curWidth > maxWidth ||
            (lastLineType != null && lastLineType != LineType.quran)) {
          // add the previous loop line
          final line = QuranLine(
            words: wordsInLine,
            text: lineText,
            type: lastLineType ?? LineType.quran,
          )
            ..width = totalWidth
            ..height = prevLineHeight / totalScale;
          lineBreakList.add(line);

          totalWidth = 0;
          textHeight += curHeight;

          lineText = "";
          wordsInLine = [];
        }

        totalWidth += curWidth;

        lineText +=
            line.type == LineType.surahHeader ? line.text : word.getText();
        wordsInLine.add(word);

        prevLineHeight = curHeight;
        lastLineType = line.type;
      }
    }

    if (lineText.isNotEmpty && wordsInLine.isNotEmpty) {
      final line = QuranLine(
        words: wordsInLine,
        text: lineText,
        type: lastLineType ?? LineType.quran,
      )
        ..width = totalWidth
        ..height = prevLineHeight / totalScale;
      lineBreakList.add(line);
    }

    textDimension.lineBreakWidth = maxWidth;
    textDimension.lineBreakHeight = textHeight;
    quranPage.lineBreakList = lineBreakList;
  }

  void calculateTextScale({
    required double widthPaddingScale,
    required double canvasWidth,
    required int frameWidth,
    required int frameHeight,
    required int borderWidth,
    required double frameScale,
  }) {
    final frameInsideWidth = frameWidth - (borderWidth * 2);
    final scaledFrameInsideWidth = frameInsideWidth * frameScale;
    final minWidth = min(canvasWidth, scaledFrameInsideWidth);

    final xScale = minWidth * widthPaddingScale / textDimension.width;
    final yScale =
        (frameHeight - borderWidth * 2) * frameScale / textDimension.height;

    textScale = min(xScale, yScale);
  }

  void measureDimension({
    required double widthPaddingScale,
    required double lineBreakWidthPaddingScale,
    required double surahHeaderHeightScale,
    required double canvasWidth,
    required int frameWidth,
    required int frameHeight,
    required int borderWidth,
    required double frameScale,
    required bool isMobile,
  }) {
    measureTextDimension(lineScale, surahHeaderHeightScale);
    calculateTextScale(
      widthPaddingScale: widthPaddingScale,
      canvasWidth: canvasWidth,
      frameWidth: frameWidth,
      frameHeight: frameHeight,
      borderWidth: borderWidth,
      frameScale: frameScale,
    );
    if (isMobile) {
      measureLineBreakTextDimension(
        frameHeight: frameHeight,
        frameScale: frameScale,
        widthPaddingScale: widthPaddingScale,
        lineBreakWidthPaddingScale: lineBreakWidthPaddingScale,
        surahHeaderHeightScale: surahHeaderHeightScale,
        canvasWidth: canvasWidth,
        lineScale: lineScale,
      );
    }
  }

  double getZoomedTextWidth(ZoomState zoomState) {
    return textDimension.getZoomedWidth(zoomState);
  }

  double getZoomedTextHeight(ZoomState zoomState) {
    return textDimension.getZoomedHeight(zoomState);
  }

  double getZoomInLevel1Scale(
      double canvasWidth, int frameHeight, double frameScale) {
    final textWidth = textDimension.width;
    final textHeight = textDimension.height;

    final scaledTextWidth = textWidth * textScale;
    final scaledTextHeight = textHeight * textScale;
    final scaledFrameHeight = frameHeight * frameScale;

    final newZoomScaleFromWidth = canvasWidth * .98 / scaledTextWidth;
    final newZoomScaleFromHeight = scaledFrameHeight / scaledTextHeight;

    return min(newZoomScaleFromWidth, newZoomScaleFromHeight);
  }

  double getZoomInLevel2Scale(
      double canvasWidth, int frameHeight, double frameScale) {
    final zoomInLevel1Scale =
        getZoomInLevel1Scale(canvasWidth, frameHeight, frameScale);
    return zoomInLevel1Scale * 1.5;
  }

  void updateZoomInLevel1Scale(
      double canvasWidth, int frameHeight, double frameScale) {
    zoomInLevel1Scale =
        getZoomInLevel1Scale(canvasWidth, frameHeight, frameScale);
  }

  SerializableQuranCanvasPageData toSerializableData() {
    return SerializableQuranCanvasPageData(
      lineScale: lineScale,
      quranPage: quranPage,
      useCachedText: useCachedText,
      useCachedPage: useCachedPage,
      textDimension: textDimension,
      navigationTextScale: navigationTextScale,
      textScale: textScale,
      textStyles: {
        'surah': TextStyleData.fromTextStyle(surahTextStyle),
        'bismillah': TextStyleData.fromTextStyle(bismillahTextStyle),
        'quran': TextStyleData.fromTextStyle(quranTextStyle),
        'verseNumber': TextStyleData.fromTextStyle(verseNumberTextStyle),
        'latin': TextStyleData.fromTextStyle(latinTextStyle),
      },
      navigationRects: {},
      // not used in text caching
      wordHighlightRects: {},
      // not used in text caching
      wordToHighlightLineBreakRects: {}, // not used in text caching
    );
  }

  void initializeFromSerializedData(SerializableQuranCanvasPageData data) {
    lineScale = data.lineScale;
    quranPage = data.quranPage;
    useCachedText = data.useCachedText;
    useCachedPage = data.useCachedPage;
    textDimension.copyFrom(data.textDimension);
    navigationTextScale = data.navigationTextScale;
    textScale = data.textScale;

    // Recreate TextStyles
    if (data.textStyles.containsKey('surah')) {
      surahTextStyle = data.textStyles['surah']!.toTextStyle();
    }
    if (data.textStyles.containsKey('bismillah')) {
      bismillahTextStyle = data.textStyles['bismillah']!.toTextStyle();
    }
    if (data.textStyles.containsKey('quran')) {
      quranTextStyle = data.textStyles['quran']!.toTextStyle();
    }
    if (data.textStyles.containsKey('verseNumber')) {
      verseNumberTextStyle = data.textStyles['verseNumber']!.toTextStyle();
    }
    if (data.textStyles.containsKey('latin')) {
      latinTextStyle = data.textStyles['latin']!.toTextStyle();
    }

    // Recreate navigation Rects
    surahNavigationRect = data.navigationRects['surah']?.toRect();
    juzNavigationRect = data.navigationRects['juz']?.toRect();

    // Clear and recreate word highlight rects
    if (data.wordHighlightRects.isNotEmpty) {
      wordToHighlightRectMap.clear();
      for (final entry in data.wordHighlightRects.entries) {
        wordToHighlightRectMap[entry.value.word] = entry.value.rect.toRect();
      }
    }

    if (data.wordToHighlightLineBreakRects.isNotEmpty) {
      wordToHighlightLineBreakRectMap.clear();
      for (final entry in data.wordToHighlightLineBreakRects.entries) {
        wordToHighlightLineBreakRectMap[entry.value.word] =
            entry.value.rect.toRect();
      }
    }

    // Recreate TextPainters as needed
    _initializeTextPainters();
  }

  void _initializeTextPainters() {
    // Initialize TextPainters with the current styles
    surahTextNavigationPainter = TextPainter(
      textDirection: ui.TextDirection.ltr,
      text: TextSpan(style: surahTextStyle),
    );

    juzTextNavigationPainter = TextPainter(
      textDirection: ui.TextDirection.ltr,
      text: TextSpan(style: surahTextStyle),
    );

    surahTextPainter.text = TextSpan(style: surahTextStyle);
    quranTextPainter.text = TextSpan(style: quranTextStyle);
    verseNumberTextPainter.text = TextSpan(style: verseNumberTextStyle);
    latinTextPainter.text = TextSpan(style: latinTextStyle);
  }
}
