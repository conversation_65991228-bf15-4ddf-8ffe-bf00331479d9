import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mushafi/presentation/model/quran/juz_name_script.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/presentation/model/quran/verse_tap_type.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_image_asset.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/presentation/model/scale_state.dart';

class SerializableQuranCanvasData {
  final QuranImageAsset imageAsset;
  final bool isMobile;
  final bool isDarkMode;
  final double canvasWidth;
  final double canvasHeight;
  final double navigationHeight;
  final double frameScale;
  final int borderWidth;
  final double rightZoomScale;
  final double leftZoomScale;
  final double rightFrameAlpha;
  final double leftFrameAlpha;
  final ZoomState previousZoomState;
  final ZoomState zoomState;
  final ScaleState scaleState;
  final double startScrollY;
  final double scrollY;
  final double currentPositionOffset;
  final Orientation? orientation;
  final JuzNameScript juzNameScript;
  final double navigationTextHeightScale;
  final double? positionOffsetUpperLimit;
  final double positionOffsetLowerLimit;
  final Color verseHighlightColor;
  final Color wordHighlightColor;
  final VerseTapType? highlightedTapType;
  final String? highlightedVerseKey;
  final QuranWord? highlightedWord;
  final double devicePixelRatio;

  // without surahList and spreadMap
  SerializableQuranCanvasData({
    required this.imageAsset,
    required this.isMobile,
    required this.isDarkMode,
    required this.canvasWidth,
    required this.canvasHeight,
    required this.navigationHeight,
    required this.frameScale,
    required this.borderWidth,
    required this.rightZoomScale,
    required this.leftZoomScale,
    required this.rightFrameAlpha,
    required this.leftFrameAlpha,
    required this.previousZoomState,
    required this.zoomState,
    required this.scaleState,
    required this.startScrollY,
    required this.scrollY,
    required this.currentPositionOffset,
    this.orientation,
    required this.juzNameScript,
    required this.navigationTextHeightScale,
    required this.positionOffsetUpperLimit,
    required this.positionOffsetLowerLimit,
    required this.verseHighlightColor,
    required this.wordHighlightColor,
    this.highlightedTapType,
    this.highlightedVerseKey,
    this.highlightedWord,
    required this.devicePixelRatio,
  });

  Map<String, dynamic> toJson() {
    return {
      'imageAssetDesign': imageAsset.design.toJson(),
      'isMobile': isMobile,
      'isDarkMode': isDarkMode,
      'canvasWidth': canvasWidth,
      'canvasHeight': canvasHeight,
      'navigationHeight': navigationHeight,
      'frameScale': frameScale,
      'borderWidth': borderWidth,
      'rightZoomScale': rightZoomScale,
      'leftZoomScale': leftZoomScale,
      'rightFrameAlpha': rightFrameAlpha,
      'leftFrameAlpha': leftFrameAlpha,
      'previousZoomState': previousZoomState.index,
      'zoomState': zoomState.index,
      'scaleState': scaleState.index,
      'startScrollY': startScrollY,
      'scrollY': scrollY,
      'currentPositionOffset': currentPositionOffset,
      'orientation': orientation?.index,
      'juzNameScript': juzNameScript.index,
      'navigationTextHeightScale': navigationTextHeightScale,
      'positionOffsetUpperLimit': positionOffsetUpperLimit,
      'positionOffsetLowerLimit': positionOffsetLowerLimit,
      'verseHighlightColor': verseHighlightColor.value,
      'wordHighlightColor': wordHighlightColor.value,
      'highlightedTapType': highlightedTapType?.index,
      'highlightedVerseKey': highlightedVerseKey,
      'highlightedWord': highlightedWord?.toJson(),
      'devicePixelRatio': devicePixelRatio,
    };
  }

  static Future<SerializableQuranCanvasData> fromJson(
    Map<String, dynamic> json,
    QuranImageAsset imageAsset,
  ) async {
    return SerializableQuranCanvasData(
      imageAsset: imageAsset,
      isMobile: json['isMobile'] as bool,
      isDarkMode: json['isDarkMode'] as bool,
      canvasWidth: json['canvasWidth'] as double,
      canvasHeight: json['canvasHeight'] as double,
      navigationHeight: json['navigationHeight'] as double,
      frameScale: json['frameScale'] as double,
      borderWidth: json['borderWidth'] as int,
      rightZoomScale: json['rightZoomScale'] as double,
      leftZoomScale: json['leftZoomScale'] as double,
      rightFrameAlpha: json['rightFrameAlpha'] as double,
      leftFrameAlpha: json['leftFrameAlpha'] as double,
      previousZoomState: ZoomState.values[json['previousZoomState'] as int],
      zoomState: ZoomState.values[json['zoomState'] as int],
      scaleState: ScaleState.values[json['scaleState'] as int],
      startScrollY: json['startScrollY'] as double,
      scrollY: json['scrollY'] as double,
      currentPositionOffset: json['currentPositionOffset'] as double,
      orientation: json['orientation'] != null
          ? Orientation.values[json['orientation'] as int]
          : null,
      juzNameScript: JuzNameScript.values[json['juzNameScript'] as int],
      navigationTextHeightScale: json['navigationTextHeightScale'] as double,
      positionOffsetUpperLimit: json['positionOffsetUpperLimit'] as double?,
      positionOffsetLowerLimit: json['positionOffsetLowerLimit'] as double,
      verseHighlightColor: Color(json['verseHighlightColor'] as int),
      wordHighlightColor: Color(json['wordHighlightColor'] as int),
      highlightedTapType: json['highlightedTapType'] != null
          ? VerseTapType.values[json['highlightedTapType'] as int]
          : null,
      highlightedVerseKey: json['highlightedVerseKey'] as String?,
      highlightedWord: json['highlightedWord'] != null
          ? QuranWord.fromJson(json['highlightedWord'] as Map<String, dynamic>)
          : null,
      devicePixelRatio: json['devicePixelRatio'] as double,
    );
  }

  String serialize() {
    return jsonEncode(toJson());
  }

  static Future<SerializableQuranCanvasData> deserialize(
    String jsonStr,
    QuranImageAsset imageAsset,
  ) {
    final json = jsonDecode(jsonStr) as Map<String, dynamic>;
    return SerializableQuranCanvasData.fromJson(json, imageAsset);
  }
}
