import 'dart:math';

import 'package:flutter/material.dart';
import 'package:mushafi/presentation/model/quran/juz_name_script.dart';
import 'package:mushafi/presentation/model/quran/quran_mode.dart';
import 'package:mushafi/presentation/model/quran/quran_page.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/quran/verse_tap_type.dart';
import 'package:mushafi/presentation/model/quran_canvas/page_position.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_page_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_spread_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_image_asset.dart';
import 'package:mushafi/presentation/model/quran_canvas/serializable_quran_canvas_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/spread_position.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/presentation/model/scale_state.dart';
import 'package:mushafi/utils/constants.dart';

class QuranCanvasData {
  late QuranImageAsset imageAsset;
  late bool isMobile;
  late bool isDarkMode;

  final double textWidthPaddingScale = .98;
  final double lineBreakWidthPaddingScale = .95;
  final double surahHeaderHeightScale = .1;
  final double surahHeaderTextScale = .8;

  // the total must be 40
  final bookHingeMargin = 16.0;
  final bookHingeWidth = 24.0;

  // the total must be 40
  final bookEdgeMargin = 28.0;
  final bookEdgeWidth = 12.0;

  // bookEdgeWidth / bookEdgePartWidth must be 6 to get 3 edges
  final double bookEdgePartWidth = 2;

  double canvasWidth = 0;
  double canvasHeight = 0;

  double navigationHeight = 0;
  double frameScale = 1;
  int borderWidth = 0;
  double rightZoomScale = 1; // todo separate zoom scale for whole page and text
  double leftZoomScale = 1;
  double rightFrameAlpha = 1;
  double leftFrameAlpha = 1;
  ZoomState previousZoomState = ZoomState.defaultZoom;
  ZoomState zoomState = ZoomState.defaultZoom;
  ScaleState scaleState = ScaleState.none;
  double startScrollY = 0;
  double scrollY = 0;
  double currentPositionOffset = 0;
  Orientation? orientation;
  JuzNameScript juzNameScript = JuzNameScript.arabic;
  double navigationTextHeightScale = .75; // 75% of navigation height
  double devicePixelRatio = 1;
  bool renderMushaf = true;
  bool cacheQuranTextDirectly = false;

  double? positionOffsetUpperLimit;
  double positionOffsetLowerLimit = 1;

  Paint verseHighlightPaint = Paint();
  Paint wordHighlightPaint = Paint();
  VerseTapType? highlightedTapType;
  String? highlightedVerseKey;
  QuranWord? highlightedWord;

  List<Surah> surahList = [];

  // todo experiment:
  // when swiping, render next/previous data and current data
  // when idle, render only current data
  final spreadMap = {
    SpreadPosition.previous: QuranCanvasSpreadData(),
    SpreadPosition.current: QuranCanvasSpreadData(),
    SpreadPosition.next: QuranCanvasSpreadData(),
  };

  QuranCanvasSpreadData get previousSpreadData =>
      spreadMap[SpreadPosition.previous]!;

  QuranCanvasSpreadData get currentSpreadData =>
      spreadMap[SpreadPosition.current]!;

  QuranCanvasSpreadData get nextSpreadData => spreadMap[SpreadPosition.next]!;

  int? get currentPagerNumber =>
      isSpread() ? currentSpreadNumber : currentPageNumber;

  int? get currentPageNumber =>
      currentSpreadData.currentSinglePageData.quranPage?.pageNumber;

  int? get currentSpreadNumber => currentSpreadData.spreadNumber;

  double get maxPositionOffset => (isSpread()) ? 302 : 604;

  double get quranTextRecorderImageScale =>
      (orientation == Orientation.landscape)
          ? devicePixelRatio + rightZoomScale
          : devicePixelRatio;

  int get frameWidth => imageAsset.frameImage?.flutterImage.width ?? 0;

  int get frameHeight => imageAsset.frameImage?.flutterImage.height ?? 0;

  QuranCanvasData();

  factory QuranCanvasData.fromSerializedData(SerializableQuranCanvasData data) {
    final instance = QuranCanvasData();
    instance.initializeFromSerializedData(data);
    return instance;
  }

  bool isForegroundCacheQuranText(
      QuranMode quranMode, QuranCanvasPageData pageData) {
    return pageData.textImage == null ||
        cacheQuranTextDirectly ||
        quranMode is! DefaultQuranMode ||
        orientation == Orientation.landscape;
  }

  // without surahList and spreadMap
  void initializeFromSerializedData(SerializableQuranCanvasData data) {
    imageAsset = data.imageAsset;
    isMobile = data.isMobile;
    isDarkMode = data.isDarkMode;
    canvasWidth = data.canvasWidth;
    canvasHeight = data.canvasHeight;
    navigationHeight = data.navigationHeight;
    frameScale = data.frameScale;
    borderWidth = data.borderWidth;
    rightZoomScale = data.rightZoomScale;
    leftZoomScale = data.leftZoomScale;
    rightFrameAlpha = data.rightFrameAlpha;
    leftFrameAlpha = data.leftFrameAlpha;
    previousZoomState = data.previousZoomState;
    zoomState = data.zoomState;
    scaleState = data.scaleState;
    startScrollY = data.startScrollY;
    scrollY = data.scrollY;
    currentPositionOffset = data.currentPositionOffset;
    orientation = data.orientation;
    juzNameScript = data.juzNameScript;
    navigationTextHeightScale = data.navigationTextHeightScale;
    positionOffsetUpperLimit = data.positionOffsetUpperLimit;
    positionOffsetLowerLimit = data.positionOffsetLowerLimit;
    verseHighlightPaint.color = data.verseHighlightColor;
    wordHighlightPaint.color = data.wordHighlightColor;
    highlightedTapType = data.highlightedTapType;
    highlightedVerseKey = data.highlightedVerseKey;
    highlightedWord = data.highlightedWord;
    devicePixelRatio = data.devicePixelRatio;
  }

  List<QuranVerse> getCurrentSpreadVerseList() {
    final currentSpread = currentSpreadData;
    final rightVerseList =
        currentSpread.rightPageData.quranPage?.verseList ?? [];
    final leftVerseList = currentSpread.leftPageData.quranPage?.verseList ?? [];
    return rightVerseList + leftVerseList;
  }

  List<QuranVerse> getNextSpreadVerseList() {
    final nextSpread = nextSpreadData;
    final rightVerseList = nextSpread.rightPageData.quranPage?.verseList ?? [];
    final leftVerseList = nextSpread.leftPageData.quranPage?.verseList ?? [];
    return rightVerseList + leftVerseList;
  }

  List<Rect> getCurrentSpreadSurahNavigationRectList() {
    final currentSpread = currentSpreadData;
    final rightRect = currentSpread.rightPageData.surahNavigationRect;
    final leftRect = currentSpread.leftPageData.surahNavigationRect;
    return [rightRect, leftRect].nonNulls.toList();
  }

  List<Rect> getCurrentSpreadJuzNavigationRectList() {
    final currentSpread = currentSpreadData;
    final rightRect = currentSpread.rightPageData.juzNavigationRect;
    final leftRect = currentSpread.leftPageData.juzNavigationRect;
    return [rightRect, leftRect].nonNulls.toList();
  }

  List<Rect> getCurrentSpreadPageNumberRectList() {
    final currentSpread = currentSpreadData;
    final rightRect = currentSpread.rightPageData.pageNumberRect;
    final leftRect = currentSpread.leftPageData.pageNumberRect;
    return [rightRect, leftRect].nonNulls.toList();
  }

  void setJuzNameScript(JuzNameScript juzNameScript) {
    this.juzNameScript = juzNameScript;
    navigationTextHeightScale = switch (juzNameScript) {
      JuzNameScript.latin => .5,
      JuzNameScript.arabic => .75,
    };
  }

  double getCurrentPageNumberTextSize() {
    final currentNavigationTextScale =
        currentSpreadData.currentSinglePageData.navigationTextScale;
    final additionalScale = switch (juzNameScript) {
      JuzNameScript.latin => .75,
      JuzNameScript.arabic => .5,
    };
    final zoomScale = (scaleWholePage()) ? rightZoomScale : 1;

    return quranTextSize *
        (currentNavigationTextScale * additionalScale) *
        zoomScale;
  }

  void setIsMobile(bool isMobile) {
    this.isMobile = isMobile;
  }

  void updateImageAsset(QuranImageAsset asset) {
    imageAsset = asset;
    applyThemeHighlightColor();
    calculateFrameBorder();
  }

  void setDarkMode(bool isDarkMode) {
    this.isDarkMode = isDarkMode;
  }

  Rect? getCurrentSpreadWordRect(QuranWord? quranWord) {
    if (quranWord == null) return null;

    final isLineBreak = zoomState == ZoomState.zoomedInLevel2;
    final rightWordRect =
        currentSpreadData.rightPageData.getWordRect(quranWord, isLineBreak);
    final leftWordRect =
        currentSpreadData.leftPageData.getWordRect(quranWord, isLineBreak);
    final wordRect = rightWordRect ?? leftWordRect;

    return wordRect;
  }

  Rect? getCurrentFirstWordRectByVerseKey(String? verseKey) {
    if (verseKey == null) return null;

    final pageData = currentSpreadData.currentSinglePageData;
    final wordRect = pageData.getFirstWordRectByVerseKey(
        verseKey, zoomState == ZoomState.zoomedInLevel2);
    return wordRect;
  }

  Rect? getFirstWordRectByVerseKey(String? verseKey) {
    if (verseKey == null) return null;

    Rect? wordRect = currentSpreadData.currentSinglePageData
        .getFirstWordRectByVerseKey(
            verseKey, zoomState == ZoomState.zoomedInLevel2);

    wordRect ??= nextSpreadData.currentSinglePageData
        .getFirstWordRectByVerseKey(
            verseKey, zoomState == ZoomState.zoomedInLevel2);

    wordRect ??= previousSpreadData.currentSinglePageData
        .getFirstWordRectByVerseKey(
            verseKey, zoomState == ZoomState.zoomedInLevel2);

    return wordRect;
  }

  Map<QuranWord, Rect> getCurrentWordRectMap() {
    final rightPageData = currentSpreadData.rightPageData;
    final leftPageData = currentSpreadData.leftPageData;

    final Map<QuranWord, Rect> dataMap = {};
    if (zoomState == ZoomState.zoomedInLevel2) {
      dataMap.addAll(rightPageData.wordToHighlightLineBreakRectMap);
      dataMap.addAll(leftPageData.wordToHighlightLineBreakRectMap);
    } else {
      dataMap.addAll(rightPageData.wordToHighlightRectMap);
      dataMap.addAll(leftPageData.wordToHighlightRectMap);
    }

    return dataMap;
  }

  void applyThemeHighlightColor() {
    verseHighlightPaint.color = imageAsset.design.highLightFadedColor;
    wordHighlightPaint.color = imageAsset.design.highLightFullColor;
  }

  void setHighlightColor(Color verseColor, Color wordColor) {
    verseHighlightPaint.color = verseColor;
    wordHighlightPaint.color = wordColor;
  }

  void setPositionOffsetLimit(
    double pageLowerLimit,
    double pageUpperLimit,
    Orientation orientation,
  ) {
    positionOffsetLowerLimit =
        (isSpread(orientation)) ? pageLowerLimit / 2 : pageLowerLimit;
    positionOffsetUpperLimit =
        (isSpread(orientation)) ? pageUpperLimit / 2 : pageUpperLimit;
  }

  void resetPositionOffsetLimit() {
    positionOffsetUpperLimit = maxPositionOffset;
    positionOffsetLowerLimit = 1;
  }

  void setCanvasSize(double canvasWidth, double canvasHeight) {
    this.canvasWidth = canvasWidth;
    this.canvasHeight = canvasHeight;
  }

  bool isSpread([Orientation? orientation]) {
    return (orientation ?? this.orientation) == Orientation.landscape &&
        !isMobile;
  }

  double getZoomedTextWidth(
      SpreadPosition spreadPosition, PagePosition pagePosition) {
    return spreadMap[spreadPosition]!
        .pageMap[pagePosition]!
        .getZoomedTextWidth(zoomState);
  }

  double getZoomedTextHeight(
      SpreadPosition spreadPosition, PagePosition pagePosition) {
    return spreadMap[spreadPosition]!
        .pageMap[pagePosition]!
        .getZoomedTextHeight(zoomState);
  }

  double getTextWidth(
      SpreadPosition spreadPosition, PagePosition pagePosition) {
    return spreadMap[spreadPosition]!
        .pageMap[pagePosition]!
        .textDimension
        .width;
  }

  double getTextHeight(
      SpreadPosition spreadPosition, PagePosition pagePosition) {
    return spreadMap[spreadPosition]!
        .pageMap[pagePosition]!
        .textDimension
        .height;
  }

  double getPageWidth() {
    // for tablet landscape mode
    if (isSpread()) {
      return canvasWidth / 2;
    }

    final scaledFrameWidth = frameWidth * frameScale;
    final pageWidth = scaledFrameWidth +
        bookHingeWidth +
        bookHingeMargin +
        bookEdgeWidth +
        bookEdgeMargin;
    return max(pageWidth, canvasWidth);
  }

  double getPageHeight(double zoomedTextHeight) {
    if (zoomState == ZoomState.zoomedInLevel2) {
      final scaledBorderWidth = borderWidth * frameScale * rightFrameAlpha;
      return navigationHeight * 2 + scaledBorderWidth * 2 + zoomedTextHeight;
    } else {
      final scaledFrameHeight = frameHeight * frameScale;
      final pageHeight = scaledFrameHeight + (navigationHeight * 2);
      return orientation == Orientation.portrait
          ? pageHeight
          : pageHeight * rightZoomScale;
    }
  }

  bool isZoomedOut() {
    return zoomState == ZoomState.zoomedOut;
  }

  bool isDefaultZoom() {
    return zoomState == ZoomState.defaultZoom;
  }

  bool isZoomedInLevel1() {
    return zoomState == ZoomState.zoomedInLevel1;
  }

  bool isZoomedInLevel2() {
    return zoomState == ZoomState.zoomedInLevel2;
  }

  bool isScaleNone() {
    return scaleState == ScaleState.none;
  }

  bool isScaleIn() {
    return scaleState == ScaleState.scaleIn;
  }

  bool isScaleOut() {
    return scaleState == ScaleState.scaleOut;
  }

  bool scaleWholePage() {
    final isScaleInToDefaultZoom = isDefaultZoom() && isScaleIn();
    return isScaleNone() || isZoomedOut() || isScaleInToDefaultZoom;
  }

  // todo move zoomedInLevel2 to scaleWholePage()
  bool scaleTextOnly() {
    final isScaleOutToDefaultZoom = isDefaultZoom() && isScaleOut();
    return isZoomedInLevel2() || isZoomedInLevel1() || isScaleOutToDefaultZoom;
  }

  bool scaleTextCenterPivot() {
    return zoomState != ZoomState.zoomedInLevel2;
  }

  bool isRightPageExists(int pageNumber) {
    final previousPageNumber =
        previousSpreadData.rightPageData.quranPage?.pageNumber;
    final currentPageNumber =
        currentSpreadData.rightPageData.quranPage?.pageNumber;
    final nextPageNumber = nextSpreadData.rightPageData.quranPage?.pageNumber;

    return previousPageNumber == pageNumber ||
        currentPageNumber == pageNumber ||
        nextPageNumber == pageNumber;
  }

  bool isLeftPageExists(int pageNumber) {
    final previousPageNumber =
        previousSpreadData.leftPageData.quranPage?.pageNumber;
    final currentPageNumber =
        currentSpreadData.leftPageData.quranPage?.pageNumber;
    final nextPageNumber = nextSpreadData.leftPageData.quranPage?.pageNumber;

    return previousPageNumber == pageNumber ||
        currentPageNumber == pageNumber ||
        nextPageNumber == pageNumber;
  }

  QuranCanvasPageData? getRightPageData(int pageNumber) {
    final previousPageNumber =
        previousSpreadData.rightPageData.quranPage?.pageNumber;
    final currentPageNumber =
        currentSpreadData.rightPageData.quranPage?.pageNumber;
    final nextPageNumber = nextSpreadData.rightPageData.quranPage?.pageNumber;

    if (previousPageNumber == pageNumber) {
      return previousSpreadData.rightPageData;
    } else if (currentPageNumber == pageNumber) {
      return currentSpreadData.rightPageData;
    } else if (nextPageNumber == pageNumber) {
      return nextSpreadData.rightPageData;
    } else {
      return null;
    }
  }

  QuranCanvasPageData? getLeftPageData(int pageNumber) {
    final previousPageNumber =
        previousSpreadData.leftPageData.quranPage?.pageNumber;
    final currentPageNumber =
        currentSpreadData.leftPageData.quranPage?.pageNumber;
    final nextPageNumber = nextSpreadData.leftPageData.quranPage?.pageNumber;

    if (previousPageNumber == pageNumber) {
      return previousSpreadData.leftPageData;
    } else if (currentPageNumber == pageNumber) {
      return currentSpreadData.leftPageData;
    } else if (nextPageNumber == pageNumber) {
      return nextSpreadData.leftPageData;
    } else {
      return null;
    }
  }

  QuranCanvasPageData? getPageData(int pageNumber) {
    return getRightPageData(pageNumber) ?? getLeftPageData(pageNumber);
  }

  void setUseCachedText(bool useCachedText) {
    for (var spread in spreadMap.values) {
      for (var page in spread.pageMap.values) {
        page.useCachedText = useCachedText;
      }
    }
  }

  void setUseCachedPage(bool useCachedPage) {
    for (var spread in spreadMap.values) {
      for (var page in spread.pageMap.values) {
        page.useCachedPage = useCachedPage;
      }
    }
  }

  void resetCachedText() {
    for (var spread in spreadMap.values) {
      for (var page in spread.pageMap.values) {
        page.textImage = null;
        page.lineBreakTextImage = null;
      }
    }
  }

  void resetCachedPage() {
    for (var spread in spreadMap.values) {
      for (var page in spread.pageMap.values) {
        page.pagePicture = null;
      }
    }
  }

  void setPreviousData({
    QuranPage? rightQuranPage,
    QuranPage? leftQuranPage,
  }) {
    if (isSpread()) {
      setSpreadData(
        spreadData: previousSpreadData,
        rightQuranPage: rightQuranPage,
        leftQuranPage: leftQuranPage,
      );
    } else {
      setSinglePageData(previousSpreadData, rightQuranPage);
    }
  }

  void setCurrentData({
    QuranPage? rightQuranPage,
    QuranPage? leftQuranPage,
  }) {
    if (isSpread()) {
      setSpreadData(
        spreadData: currentSpreadData,
        rightQuranPage: rightQuranPage,
        leftQuranPage: leftQuranPage,
      );
    } else {
      setSinglePageData(currentSpreadData, rightQuranPage);
    }
  }

  void setNextData({
    QuranPage? rightQuranPage,
    QuranPage? leftQuranPage,
  }) {
    if (isSpread()) {
      setSpreadData(
        spreadData: nextSpreadData,
        rightQuranPage: rightQuranPage,
        leftQuranPage: leftQuranPage,
      );
    } else {
      setSinglePageData(nextSpreadData, rightQuranPage);
    }
  }

  void setSinglePageData(
      QuranCanvasSpreadData spreadData, QuranPage? quranPage) {
    spreadData.updateSinglePageData(
      quranPage: quranPage,
      juzNameScript: juzNameScript,
      surahList: surahList,
      canvasWidth: canvasWidth,
      navigationHeight: navigationHeight,
      navigationTextHeightScale: navigationTextHeightScale,
      textWidthPaddingScale: textWidthPaddingScale,
      lineBreakWidthPaddingScale: lineBreakWidthPaddingScale,
      surahHeaderHeightScale: surahHeaderHeightScale,
      frameWidth: frameWidth,
      frameHeight: frameHeight,
      borderWidth: borderWidth,
      frameScale: frameScale,
      isMobile: isMobile,
      isDarkMode: isDarkMode,
    );
  }

  void setSpreadData({
    required QuranCanvasSpreadData spreadData,
    QuranPage? rightQuranPage,
    QuranPage? leftQuranPage,
  }) {
    spreadData.updateSpreadData(
      rightQuranPage: rightQuranPage,
      leftQuranPage: leftQuranPage,
      juzNameScript: juzNameScript,
      surahList: surahList,
      canvasWidth: canvasWidth,
      navigationHeight: navigationHeight,
      navigationTextHeightScale: navigationTextHeightScale,
      textWidthPaddingScale: textWidthPaddingScale,
      lineBreakWidthPaddingScale: lineBreakWidthPaddingScale,
      surahHeaderHeightScale: surahHeaderHeightScale,
      frameWidth: frameWidth,
      frameHeight: frameHeight,
      borderWidth: borderWidth,
      frameScale: frameScale,
      isMobile: isMobile,
      isDarkMode: isDarkMode,
    );
  }

  void shiftDataToNext({
    QuranPage? nextRightQuranPage,
    QuranPage? nextLeftQuranPage,
  }) {
    if (isSpread()) {
      shiftDataToNextSpread(
        nextRightQuranPage: nextRightQuranPage,
        nextLeftQuranPage: nextLeftQuranPage,
      );
    } else {
      shiftDataToNextPage(nextRightQuranPage);
    }
  }

  void shiftDataToPrevious({
    QuranPage? previousRightQuranPage,
    QuranPage? previousLeftQuranPage,
  }) {
    if (isSpread()) {
      shiftDataToPreviousSpread(
        previousRightQuranPage: previousRightQuranPage,
        previousLeftQuranPage: previousLeftQuranPage,
      );
    } else {
      shiftDataToPreviousPage(previousRightQuranPage);
    }
  }

  void shiftDataToNextPage(QuranPage? nextQuranPage) {
    previousSpreadData
        .updateCurrentSinglePageData(currentSpreadData.currentSinglePageData);
    currentSpreadData
        .updateCurrentSinglePageData(nextSpreadData.currentSinglePageData);
    nextSpreadData.updateCurrentSinglePageData(QuranCanvasPageData());
    setSinglePageData(nextSpreadData, nextQuranPage);
  }

  void shiftDataToPreviousPage(QuranPage? previousQuranPage) {
    nextSpreadData
        .updateCurrentSinglePageData(currentSpreadData.currentSinglePageData);
    currentSpreadData
        .updateCurrentSinglePageData(previousSpreadData.currentSinglePageData);
    previousSpreadData.updateCurrentSinglePageData(QuranCanvasPageData());
    setSinglePageData(previousSpreadData, previousQuranPage);
  }

  void shiftDataToNextSpread({
    QuranPage? nextRightQuranPage,
    QuranPage? nextLeftQuranPage,
  }) {
    spreadMap[SpreadPosition.previous] = currentSpreadData;
    spreadMap[SpreadPosition.current] = nextSpreadData;
    spreadMap[SpreadPosition.next] = QuranCanvasSpreadData();
    setSpreadData(
      spreadData: nextSpreadData,
      rightQuranPage: nextRightQuranPage,
      leftQuranPage: nextLeftQuranPage,
    );
  }

  void shiftDataToPreviousSpread({
    QuranPage? previousRightQuranPage,
    QuranPage? previousLeftQuranPage,
  }) {
    spreadMap[SpreadPosition.next] = currentSpreadData;
    spreadMap[SpreadPosition.current] = previousSpreadData;
    spreadMap[SpreadPosition.previous] = QuranCanvasSpreadData();
    setSpreadData(
      spreadData: previousSpreadData,
      rightQuranPage: previousRightQuranPage,
      leftQuranPage: previousLeftQuranPage,
    );
  }

  void calculateDimension(
    Function(double navigationHeight) onNavigationHeightCalculated,
  ) {
    final frameFlutterImage = imageAsset.frameImage?.flutterImage;
    if (frameFlutterImage == null) return;

    frameScale = canvasHeight / frameFlutterImage.height;
    navigationHeight = (frameFlutterImage.height / 15) * frameScale;
    frameScale =
        (canvasHeight - (navigationHeight * 2)) / frameFlutterImage.height;

    onNavigationHeightCalculated(navigationHeight);
  }

  void calculateFrameBorder() {
    final frameLibraryImage = imageAsset.frameImage?.libraryImage;
    if (frameLibraryImage == null) return;

    var width = 0;
    var startY = 0;
    var blankSum = 0;

    verticalLoop:
    for (int y = 0; y < frameLibraryImage.height; y++) {
      for (int x = 0; x < frameLibraryImage.width; x++) {
        final pixel = frameLibraryImage.getPixel(x, y);
        if (pixel.r != 0 && pixel.g != 0 && pixel.b != 0) {
          startY = y;
          break verticalLoop;
        }
      }
    }

    for (int y = startY; y < frameLibraryImage.height; y++) {
      final x = frameLibraryImage.width ~/ 2;
      final pixel = frameLibraryImage.getPixel(x, y);

      if (pixel.r != 0 && pixel.g != 0 && pixel.b != 0) {
        blankSum = 0;
      } else {
        blankSum += 1;
      }

      width += 1;

      // indicates that it is not the part of the border anymore
      if (blankSum >= 100) {
        width -= blankSum;
        break; // to prevent too much loop
      }
    }

    borderWidth = width;
  }

  double getZoomOutScale() {
    final pageWidth = getPageWidth();
    return canvasWidth / pageWidth;
  }

  double getZoomInLevel1Scale(SpreadPosition spreadPosition) {
    return spreadMap[spreadPosition]!
        .currentSinglePageData
        .getZoomInLevel1Scale(canvasWidth, frameHeight, frameScale);
  }

  double getZoomInLevel2Scale(SpreadPosition spreadPosition) {
    if (isMobile) {
      return spreadMap[spreadPosition]!
          .currentSinglePageData
          .getZoomInLevel2Scale(canvasWidth, frameHeight, frameScale);
    }

    return 0;
  }

  double getPhoneLandscapeScale() {
    final scaledFrameWidth = frameWidth * frameScale;
    return canvasWidth / scaledFrameWidth;
  }

  void updateZoomInLevel1Scale() {
    for (final spreadData in spreadMap.values) {
      for (final pageData in spreadData.pageMap.values) {
        pageData.updateZoomInLevel1Scale(canvasWidth, frameHeight, frameScale);
      }
    }
  }

  SerializableQuranCanvasData toSerializableData() {
    return SerializableQuranCanvasData(
      imageAsset: imageAsset,
      isMobile: isMobile,
      isDarkMode: isDarkMode,
      canvasWidth: canvasWidth,
      canvasHeight: canvasHeight,
      navigationHeight: navigationHeight,
      frameScale: frameScale,
      borderWidth: borderWidth,
      rightZoomScale: rightZoomScale,
      leftZoomScale: leftZoomScale,
      rightFrameAlpha: rightFrameAlpha,
      leftFrameAlpha: leftFrameAlpha,
      previousZoomState: previousZoomState,
      zoomState: zoomState,
      scaleState: scaleState,
      startScrollY: startScrollY,
      scrollY: scrollY,
      currentPositionOffset: currentPositionOffset,
      orientation: orientation,
      juzNameScript: juzNameScript,
      navigationTextHeightScale: navigationTextHeightScale,
      positionOffsetUpperLimit: positionOffsetUpperLimit,
      positionOffsetLowerLimit: positionOffsetLowerLimit,
      verseHighlightColor: verseHighlightPaint.color,
      wordHighlightColor: wordHighlightPaint.color,
      highlightedTapType: highlightedTapType,
      highlightedVerseKey: highlightedVerseKey,
      highlightedWord: highlightedWord,
      devicePixelRatio: devicePixelRatio,
    );
  }
}
