import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mushafi/presentation/model/quran/quran_page.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/presentation/model/quran_canvas/text_dimension.dart';

class SerializableQuranCanvasPageData {
  final double lineScale;
  final QuranPage? quranPage;
  final bool useCachedText;
  final bool useCachedPage;
  final TextDimension textDimension;
  final double navigationTextScale;
  final double textScale;

  // Serialized values from TextStyle
  final Map<String, TextStyleData> textStyles;

  // Serialized values from Rect
  final Map<String, RectData> navigationRects;

  // Serialized values from wordToHighlightRectMap
  final Map<int, WordHighlightData> wordHighlightRects;

  // Serialized values from wordToHighlightLineBreakRectMap
  final Map<int, WordHighlightData> wordToHighlightLineBreakRects;

  SerializableQuranCanvasPageData({
    this.lineScale = 0.85,
    this.quranPage,
    this.useCachedText = true,
    this.useCachedPage = true,
    required this.textDimension,
    this.navigationTextScale = 1,
    this.textScale = 1,
    required this.textStyles,
    required this.navigationRects,
    required this.wordHighlightRects,
    required this.wordToHighlightLineBreakRects,
  });

  Map<String, dynamic> toJson() {
    return {
      'lineScale': lineScale,
      'quranPage': quranPage?.toJson(),
      'useCachedText': useCachedText,
      'useCachedPage': useCachedPage,
      'textDimension': textDimension.toJson(),
      'navigationTextScale': navigationTextScale,
      'textScale': textScale,
      'textStyles':
          textStyles.map((key, value) => MapEntry(key, value.toJson())),
      'navigationRects':
          navigationRects.map((key, value) => MapEntry(key, value.toJson())),
      'wordHighlightRects': wordHighlightRects.map(
        (key, value) => MapEntry(key.toString(), value.toJson()),
      ),
      'wordToHighlightLineBreakRects': wordToHighlightLineBreakRects.map(
        (key, value) => MapEntry(key.toString(), value.toJson()),
      ),
    };
  }

  factory SerializableQuranCanvasPageData.fromJson(Map<String, dynamic> json) {
    return SerializableQuranCanvasPageData(
      lineScale: json['lineScale'] as double,
      quranPage: json['quranPage'] != null
          ? QuranPage.fromJson(json['quranPage'])
          : null,
      useCachedText: json['useCachedText'] as bool,
      useCachedPage: json['useCachedPage'] as bool,
      textDimension: TextDimension.fromJson(json['textDimension']),
      navigationTextScale: json['navigationTextScale'] as double,
      textScale: json['textScale'] as double,
      textStyles: (json['textStyles'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(key, TextStyleData.fromJson(value)),
      ),
      navigationRects: (json['navigationRects'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(key, RectData.fromJson(value)),
      ),
      wordHighlightRects:
          (json['wordHighlightRects'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(
          int.parse(key),
          WordHighlightData.fromJson(value),
        ),
      ),
      wordToHighlightLineBreakRects:
          (json['wordToHighlightLineBreakRects'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(
          int.parse(key),
          WordHighlightData.fromJson(value),
        ),
      ),
    );
  }

  String serialize() {
    return jsonEncode(toJson());
  }

  static SerializableQuranCanvasPageData deserialize(String jsonStr) {
    final json = jsonDecode(jsonStr) as Map<String, dynamic>;
    return SerializableQuranCanvasPageData.fromJson(json);
  }
}

class TextStyleData {
  final String fontFamily;
  final double fontSize;
  final Color color;

  TextStyleData({
    required this.fontFamily,
    required this.fontSize,
    required this.color,
  });

  TextStyle toTextStyle() {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize,
      color: color,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fontFamily': fontFamily,
      'fontSize': fontSize,
      'color': color.value,
    };
  }

  factory TextStyleData.fromJson(Map<String, dynamic> json) {
    return TextStyleData(
      fontFamily: json['fontFamily'] as String,
      fontSize: json['fontSize'] as double,
      color: Color(json['color'] as int),
    );
  }

  factory TextStyleData.fromTextStyle(TextStyle style) {
    return TextStyleData(
      fontFamily: style.fontFamily ?? '',
      fontSize: style.fontSize ?? 0,
      color: style.color ?? Colors.black,
    );
  }
}

class RectData {
  final double left;
  final double top;
  final double right;
  final double bottom;

  RectData({
    required this.left,
    required this.top,
    required this.right,
    required this.bottom,
  });

  Rect toRect() {
    return Rect.fromLTRB(left, top, right, bottom);
  }

  Map<String, dynamic> toJson() {
    return {
      'left': left,
      'top': top,
      'right': right,
      'bottom': bottom,
    };
  }

  factory RectData.fromJson(Map<String, dynamic> json) {
    return RectData(
      left: json['left'] as double,
      top: json['top'] as double,
      right: json['right'] as double,
      bottom: json['bottom'] as double,
    );
  }

  factory RectData.fromRect(Rect rect) {
    return RectData(
      left: rect.left,
      top: rect.top,
      right: rect.right,
      bottom: rect.bottom,
    );
  }
}

class WordHighlightData {
  final QuranWord word;
  final RectData rect;

  WordHighlightData({
    required this.word,
    required this.rect,
  });

  Map<String, dynamic> toJson() {
    return {
      'word': word.toJson(),
      'rect': rect.toJson(),
    };
  }

  factory WordHighlightData.fromJson(Map<String, dynamic> json) {
    return WordHighlightData(
      word: QuranWord.fromJson(json['word']),
      rect: RectData.fromJson(json['rect']),
    );
  }
}
