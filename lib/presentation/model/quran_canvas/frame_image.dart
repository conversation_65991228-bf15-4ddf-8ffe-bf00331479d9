import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:image/image.dart' as image;

class FrameImage {
  final ui.Image flutterImage;
  final image.Image libraryImage;

  FrameImage(this.flutterImage, this.libraryImage);

  Future<Map<String, dynamic>> toJson() async {
    final ByteData? byteData =
        await flutterImage.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List? flutterBytes = byteData?.buffer.asUint8List();
    final Uint8List libraryBytes =
        Uint8List.fromList(image.encodePng(libraryImage));

    return {
      'flutterImage': flutterBytes?.toList(),
      'libraryImage': libraryBytes.toList(),
      'width': flutterImage.width,
      'height': flutterImage.height,
    };
  }

  static Future<FrameImage> fromJson(Map<String, dynamic> json) async {
    final List<int>? flutterList = (json['flutterImage'] != null)
        ? List<int>.from(json['flutterImage'])
        : null;
    final List<int> libraryList = List<int>.from(json['libraryImage']);
    final int width = json['width'] as int;
    final int height = json['height'] as int;

    final ui.Codec codec = await ui.instantiateImageCodec(
      Uint8List.fromList(flutterList ?? libraryList),
      targetWidth: width,
      targetHeight: height,
    );
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image flutterImage = frameInfo.image;

    final image.Image libraryImage =
        image.decodePng(Uint8List.fromList(libraryList))!;

    return FrameImage(flutterImage, libraryImage);
  }
}
