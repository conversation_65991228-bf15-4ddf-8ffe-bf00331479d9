import 'package:collection/collection.dart';

enum ZoomState {
  zoomedOut('ZoomedOut'),
  defaultZoom('Default'),
  zoomedInLevel1('ZoomedInLevel1'), // without frame
  zoomedInLevel2('ZoomedInLevel2'); // without frame and line breaks

  final String name;

  const ZoomState(this.name);

  static ZoomState? fromName(String name) {
    return ZoomState.values.firstWhereOrNull((state) => state.name == name);
  }
}
