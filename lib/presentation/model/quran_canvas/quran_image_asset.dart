import 'dart:typed_data';
import 'dart:ui';

import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran_canvas/frame_image.dart';

class QuranImageAsset {
  final MushafDesign design;
  final FrameImage? frameImage;
  final Image verseNumberImage;
  final Image surahImage;

  QuranImageAsset(
      this.design, this.frameImage, this.verseNumberImage, this.surahImage);

  Future<Map<String, dynamic>> toJson() async {
    final verseNumberData =
        await verseNumberImage.toByteData(format: ImageByteFormat.png);
    final surahData = await surahImage.toByteData(format: ImageByteFormat.png);

    return {
      'design': design.toJson(),
      'frameImage': await frameImage?.toJson(),
      'verseNumberImage': verseNumberData?.buffer.asUint8List().toList(),
      'surahImage': surahData?.buffer.asUint8List().toList(),
      'verseNumberWidth': verseNumberImage.width,
      'verseNumberHeight': verseNumberImage.height,
      'surahWidth': surahImage.width,
      'surahHeight': surahImage.height,
    };
  }

  static Future<QuranImageAsset> fromJson(Map<String, dynamic> json) async {
    final design =
        MushafDesign.fromJson(json['design'] as Map<String, dynamic>);
    final frameImage =
        await FrameImage.fromJson(json['frameImage'] as Map<String, dynamic>);

    final List<int>? verseNumberList = (json['verseNumberImage'] != null)
        ? List<int>.from(json['verseNumberImage'])
        : null;
    final List<int>? surahList = (json['surahImage'] != null)
        ? List<int>.from(json['surahImage'])
        : null;

    final verseNumberCodec = await instantiateImageCodec(
      Uint8List.fromList(verseNumberList ?? []),
      targetWidth: json['verseNumberWidth'] as int,
      targetHeight: json['verseNumberHeight'] as int,
    );
    final verseNumberFrame = await verseNumberCodec.getNextFrame();
    final verseNumberImage = verseNumberFrame.image;

    final surahCodec = await instantiateImageCodec(
      Uint8List.fromList(surahList ?? []),
      targetWidth: json['surahWidth'] as int,
      targetHeight: json['surahHeight'] as int,
    );
    final surahFrame = await surahCodec.getNextFrame();
    final surahImage = surahFrame.image;

    return QuranImageAsset(design, frameImage, verseNumberImage, surahImage);
  }
}
