import 'package:mushafi/presentation/model/quran/quran_line.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';

class LineData {
  List<QuranLine> lineList = [];
  List<QuranLine> lineBreakList = [];

  List<QuranLine> getZoomedLineList(ZoomState zoomState) {
    if (zoomState == ZoomState.zoomedInLevel2) {
      return lineBreakList;
    } else {
      return lineList;
    }
  }
}
