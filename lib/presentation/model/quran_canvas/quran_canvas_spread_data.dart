import 'package:mushafi/presentation/model/quran/juz_name_script.dart';
import 'package:mushafi/presentation/model/quran/quran_page.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/quran_canvas/page_position.dart';
import 'package:mushafi/presentation/model/quran_canvas/serializable_quran_canvas_page_data.dart';

import 'quran_canvas_page_data.dart';

class QuranCanvasSpreadData {
  final pageMap = {
    PagePosition.right: QuranCanvasPageData(),
    PagePosition.left: QuranCanvasPageData(),
  };

  QuranCanvasPageData get leftPageData {
    return pageMap[PagePosition.left]!;
  }

  QuranCanvasPageData get rightPageData {
    return pageMap[PagePosition.right]!;
  }

  QuranCanvasPageData get currentSinglePageData {
    return pageMap[PagePosition.right]!;
  }

  int? get spreadNumber => (leftPageData.quranPage?.pageNumber != null)
      ? leftPageData.quranPage!.pageNumber ~/ 2
      : (rightPageData.quranPage?.pageNumber != null)
          ? (rightPageData.quranPage!.pageNumber / 2).round()
          : null;

  void updateCurrentSinglePageData(QuranCanvasPageData pageData) {
    pageMap[PagePosition.right] = pageData;
  }

  void updateSinglePageData({
    QuranPage? quranPage,
    required JuzNameScript juzNameScript,
    required List<Surah> surahList,
    required double canvasWidth,
    required double navigationHeight,
    required double navigationTextHeightScale,
    required double textWidthPaddingScale,
    required double lineBreakWidthPaddingScale,
    required double surahHeaderHeightScale,
    required int frameWidth,
    required int frameHeight,
    required int borderWidth,
    required double frameScale,
    required bool isMobile,
    required bool isDarkMode,
  }) {
    pageMap[PagePosition.left] = QuranCanvasPageData();
    currentSinglePageData.setQuranPage(
        quranPage, juzNameScript, surahList, isDarkMode);
    currentSinglePageData.updateNavigationTextScale(
      navigationHeight: navigationHeight,
      navigationTextHeightScale: navigationTextHeightScale,
    );
    currentSinglePageData.measureDimension(
      widthPaddingScale: textWidthPaddingScale,
      lineBreakWidthPaddingScale: lineBreakWidthPaddingScale,
      surahHeaderHeightScale: surahHeaderHeightScale,
      canvasWidth: canvasWidth,
      frameWidth: frameWidth,
      frameHeight: frameHeight,
      borderWidth: borderWidth,
      frameScale: frameScale,
      isMobile: isMobile,
    );
  }

  void updateSpreadData({
    QuranPage? rightQuranPage,
    QuranPage? leftQuranPage,
    required JuzNameScript juzNameScript,
    required List<Surah> surahList,
    required double canvasWidth,
    required double navigationHeight,
    required double navigationTextHeightScale,
    required double textWidthPaddingScale,
    required double lineBreakWidthPaddingScale,
    required double surahHeaderHeightScale,
    required int frameWidth,
    required int frameHeight,
    required int borderWidth,
    required double frameScale,
    required bool isMobile,
    required bool isDarkMode,
  }) {
    rightPageData.setQuranPage(
        rightQuranPage, juzNameScript, surahList, isDarkMode);
    rightPageData.updateNavigationTextScale(
      navigationHeight: navigationHeight,
      navigationTextHeightScale: navigationTextHeightScale,
    );
    rightPageData.measureDimension(
      widthPaddingScale: textWidthPaddingScale,
      lineBreakWidthPaddingScale: lineBreakWidthPaddingScale,
      surahHeaderHeightScale: surahHeaderHeightScale,
      canvasWidth: canvasWidth,
      frameWidth: frameWidth,
      frameHeight: frameHeight,
      borderWidth: borderWidth,
      frameScale: frameScale,
      isMobile: isMobile,
    );

    leftPageData.setQuranPage(
        leftQuranPage, juzNameScript, surahList, isDarkMode);
    leftPageData.updateNavigationTextScale(
      navigationHeight: navigationHeight,
      navigationTextHeightScale: navigationTextHeightScale,
    );
    leftPageData.measureDimension(
      widthPaddingScale: textWidthPaddingScale,
      lineBreakWidthPaddingScale: lineBreakWidthPaddingScale,
      surahHeaderHeightScale: surahHeaderHeightScale,
      canvasWidth: canvasWidth,
      frameWidth: frameWidth,
      frameHeight: frameHeight,
      borderWidth: borderWidth,
      frameScale: frameScale,
      isMobile: isMobile,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> pageMapJson = {};
    for (final entry in pageMap.entries) {
      pageMapJson[entry.key.index.toString()] =
          entry.value.toSerializableData().toJson();
    }

    return {
      'pageMap': pageMapJson,
    };
  }

  static Future<QuranCanvasSpreadData> fromJson(
      Map<String, dynamic> json) async {
    final spreadData = QuranCanvasSpreadData();
    final pageMapJson = json['pageMap'] as Map<String, dynamic>;

    for (final entry in pageMapJson.entries) {
      final position = PagePosition.values[int.parse(entry.key)];
      final serializable = SerializableQuranCanvasPageData.fromJson(
          entry.value as Map<String, dynamic>);
      spreadData.pageMap[position] =
          QuranCanvasPageData.fromSerializedData(serializable);
    }

    return spreadData;
  }
}
