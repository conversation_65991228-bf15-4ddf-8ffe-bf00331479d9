import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'last_recited_word.g.dart';

@JsonSerializable()
class LastRecitedWord extends Equatable {
  final int? index;
  final int? time;

  const LastRecitedWord({
    this.index,
    this.time,
  });

  factory LastRecitedWord.fromJson(Map<String, dynamic> json) =>
      _$LastRecitedWordFromJson(json);

  Map<String, dynamic> toJson() => _$LastRecitedWordToJson(this);

  @override
  List<Object?> get props => [index, time];
}
