import 'package:equatable/equatable.dart';

class Reciter extends Equatable {
  final String name;
  final String shortName;
  final String arabicName;
  final String arabicShortName;
  final String url;
  final String audioSegmentFileName;

  Reciter({
    required this.name,
    required this.shortName,
    required this.arabicName,
    required this.arabicShortName,
    required this.url,
    required this.audioSegmentFileName,
  });

  late final String key = audioSegmentFileName.replaceAll('.fb', '');

  factory Reciter.fromCsvRow(List<dynamic> row) {
    return Reciter(
      name: row[0],
      shortName: row[1],
      arabicName: row[2],
      arabicShortName: row[3],
      url: row[5],
      audioSegmentFileName: row[7],
    );
  }

  factory Reciter.fromJson(Map<String, dynamic> json) {
    return Reciter(
      name: json['name'],
      shortName: json['shortName'],
      arabicName: json['arabicName'],
      arabicShortName: json['arabicShortName'],
      url: json['url'],
      audioSegmentFileName: json['audioSegmentFileName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'shortName': shortName,
      'arabicName': arabicName,
      'arabicShortName': arabicShortName,
      'url': url,
      'audioSegmentFileName': audioSegmentFileName,
    };
  }

  @override
  List<Object?> get props => [
        name,
        shortName,
        arabicName,
        arabicShortName,
        url,
        audioSegmentFileName,
      ];
}
