import 'dart:ui';

import 'package:equatable/equatable.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/quran/mushaf_code.dart';
import 'package:mushafi/resource/color_resource.dart';

class MushafDesign extends Equatable {
  final MushafCode code;
  final String sampleAsset;
  final String surahAsset;
  final String zoomedInSurahAsset;
  final String verseAsset;
  final String frameAsset;
  final int hizbFullRes;
  final int? hizbHalfRes;
  final Color drawer1Color;
  final Color drawer2Color;
  final Color highLightFullColor;
  final Color highLightFadedColor;
  final Color backgroundColor;
  final Color activityCardBackgroundColor;
  final Color activityCardBorderColor;

  const MushafDesign({
    required this.code,
    required this.sampleAsset,
    required this.surahAsset,
    required this.zoomedInSurahAsset,
    required this.verseAsset,
    required this.frameAsset,
    required this.hizbFullRes,
    this.hizbHalfRes,
    required this.drawer1Color,
    required this.drawer2Color,
    required this.highLightFullColor,
    required this.highLightFadedColor,
    required this.backgroundColor,
    required this.activityCardBackgroundColor,
    required this.activityCardBorderColor,
  });

  static MushafDesign getDefault(bool isDarkMode) {
    return MushafDesign(
      code: MushafCode.defaultCode,
      sampleAsset: Assets.svgsDefaultSample,
      surahAsset: Assets.svgsDefaultSurah,
      zoomedInSurahAsset: Assets.svgsDefaultSurah,
      verseAsset: Assets.svgsDefaultVerse,
      frameAsset: Assets.svgsDefaultFrame,
      hizbFullRes: 0,
      hizbHalfRes: 0,
      drawer1Color: ColorResource.defaultDrawer1(isDarkMode),
      drawer2Color: ColorResource.defaultDrawer2(isDarkMode),
      highLightFullColor: ColorResource.defaultHighlightFull(isDarkMode),
      highLightFadedColor: ColorResource.defaultHighlightFaded(isDarkMode),
      backgroundColor: ColorResource.defaultBackground(isDarkMode),
      activityCardBackgroundColor:
          ColorResource.defaultActivityCardBackground(isDarkMode),
      activityCardBorderColor:
          ColorResource.defaultActivityCardBorder(isDarkMode),
    );
  }

  static List<MushafDesign> getList(bool isDarkMode,
      {bool includeDefault = false}) {
    final list = <MushafDesign>[];

    if (includeDefault) {
      list.add(getDefault(isDarkMode));
    }

    list.add(
      MushafDesign(
        code: MushafCode.medina,
        sampleAsset: Assets.svgsMedinaSample,
        surahAsset: Assets.svgsMedinaSurah,
        zoomedInSurahAsset: Assets.svgsMedinaSurah,
        verseAsset: Assets.svgsMedinaVerse,
        frameAsset: Assets.svgsMedinaFrame,
        hizbFullRes: 0,
        hizbHalfRes: 0,
        drawer1Color: ColorResource.medinaDrawer1(isDarkMode),
        drawer2Color: ColorResource.medinaDrawer2(isDarkMode),
        highLightFullColor: ColorResource.medinaHighlightFull(isDarkMode),
        highLightFadedColor: ColorResource.medinaHighlightFaded(isDarkMode),
        backgroundColor: ColorResource.medinaBackground(isDarkMode),
        activityCardBackgroundColor:
            ColorResource.medinaActivityCardBackground(isDarkMode),
        activityCardBorderColor:
            ColorResource.medinaActivityCardBorder(isDarkMode),
      ),
    );

    list.add(
      MushafDesign(
        code: MushafCode.oldMedina,
        sampleAsset: Assets.svgsOldMedinaSample,
        surahAsset: Assets.svgsOldMedinaSurah,
        zoomedInSurahAsset: Assets.svgsOldMedinaSurah,
        verseAsset: Assets.svgsOldMedinaVerse,
        frameAsset: Assets.svgsOldMedinaFrame,
        hizbFullRes: 0,
        hizbHalfRes: 0,
        drawer1Color: ColorResource.oldMedinaDrawer1(isDarkMode),
        drawer2Color: ColorResource.oldMedinaDrawer2(isDarkMode),
        highLightFullColor: ColorResource.oldMedinaHighlightFull(isDarkMode),
        highLightFadedColor: ColorResource.oldMedinaHighlightFaded(isDarkMode),
        backgroundColor: ColorResource.oldMedinaBackground(isDarkMode),
        activityCardBackgroundColor:
            ColorResource.oldMedinaActivityCardBackground(isDarkMode),
        activityCardBorderColor:
            ColorResource.oldMedinaActivityCardBorder(isDarkMode),
      ),
    );

    list.add(
      MushafDesign(
        code: MushafCode.qaloon,
        sampleAsset: Assets.svgsQaloonSample,
        surahAsset: Assets.svgsQaloonSurah,
        zoomedInSurahAsset: Assets.svgsQaloonSurah,
        verseAsset: Assets.svgsQaloonVerse,
        frameAsset: Assets.svgsQaloonFrame,
        hizbFullRes: 0,
        hizbHalfRes: 0,
        drawer1Color: ColorResource.qaloonDrawer1(isDarkMode),
        drawer2Color: ColorResource.qaloonDrawer2(isDarkMode),
        highLightFullColor: ColorResource.qaloonHighlightFull(isDarkMode),
        highLightFadedColor: ColorResource.qaloonHighlightFaded(isDarkMode),
        backgroundColor: ColorResource.qaloonBackground(isDarkMode),
        activityCardBackgroundColor:
            ColorResource.qaloonActivityCardBackground(isDarkMode),
        activityCardBorderColor:
            ColorResource.qaloonActivityCardBorder(isDarkMode),
      ),
    );

    list.add(
      MushafDesign(
        code: MushafCode.mediumMedina,
        sampleAsset: Assets.svgsMediumMedinaSample,
        surahAsset: Assets.svgsMediumMedinaSurah,
        zoomedInSurahAsset: Assets.svgsMediumMedinaSurah,
        verseAsset: Assets.svgsMediumMedinaVerse,
        frameAsset: Assets.svgsMediumMedinaFrame,
        hizbFullRes: 0,
        hizbHalfRes: 0,
        drawer1Color: ColorResource.mediumMedinaDrawer1(isDarkMode),
        drawer2Color: ColorResource.mediumMedinaDrawer2(isDarkMode),
        highLightFullColor: ColorResource.mediumMedinaHighlightFull(isDarkMode),
        highLightFadedColor:
            ColorResource.mediumMedinaHighlightFaded(isDarkMode),
        backgroundColor: ColorResource.mediumMedinaBackground(isDarkMode),
        activityCardBackgroundColor:
            ColorResource.mediumMedinaActivityCardBackground(isDarkMode),
        activityCardBorderColor:
            ColorResource.mediumMedinaActivityCardBorder(isDarkMode),
      ),
    );

    list.add(
      MushafDesign(
        code: MushafCode.shubah,
        sampleAsset: Assets.svgsShubahSample,
        surahAsset: Assets.svgsShubahSurah,
        zoomedInSurahAsset: Assets.svgsShubahSurah,
        verseAsset: Assets.svgsShubahVerse,
        frameAsset: Assets.svgsShubahFrame,
        hizbFullRes: 0,
        hizbHalfRes: 0,
        drawer1Color: ColorResource.shubahDrawer1(isDarkMode),
        drawer2Color: ColorResource.shubahDrawer2(isDarkMode),
        highLightFullColor: ColorResource.shubahHighlightFull(isDarkMode),
        highLightFadedColor: ColorResource.shubahHighlightFaded(isDarkMode),
        backgroundColor: ColorResource.shubahBackground(isDarkMode),
        activityCardBackgroundColor:
            ColorResource.shubahActivityCardBackground(isDarkMode),
        activityCardBorderColor:
            ColorResource.shubahActivityCardBorder(isDarkMode),
      ),
    );

    list.add(
      MushafDesign(
        code: MushafCode.warsh,
        sampleAsset: Assets.svgsWarshSample,
        surahAsset: Assets.svgsWarshSurah,
        zoomedInSurahAsset: Assets.svgsWarshSurah,
        verseAsset: Assets.svgsWarshVerse,
        frameAsset: Assets.svgsWarshFrame,
        hizbFullRes: 0,
        hizbHalfRes: 0,
        drawer1Color: ColorResource.warshDrawer1(isDarkMode),
        drawer2Color: ColorResource.warshDrawer2(isDarkMode),
        highLightFullColor: ColorResource.warshHighlightFull(isDarkMode),
        highLightFadedColor: ColorResource.warshHighlightFaded(isDarkMode),
        backgroundColor: ColorResource.warshBackground(isDarkMode),
        activityCardBackgroundColor:
            ColorResource.warshActivityCardBackground(isDarkMode),
        activityCardBorderColor:
            ColorResource.warshActivityCardBorder(isDarkMode),
      ),
    );

    return list;
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code.name,
      'sampleAsset': sampleAsset,
      'surahAsset': surahAsset,
      'zoomedInSurahAsset': zoomedInSurahAsset,
      'verseAsset': verseAsset,
      'frameAsset': frameAsset,
      'hizbFullRes': hizbFullRes,
      'hizbHalfRes': hizbHalfRes,
      'drawer1Color': drawer1Color.value,
      'drawer2Color': drawer2Color.value,
      'highLightFullColor': highLightFullColor.value,
      'highLightFadedColor': highLightFadedColor.value,
      'backgroundColor': backgroundColor.value,
      'activityCardBackgroundColor': activityCardBackgroundColor.value,
      'activityCardBorderColor': activityCardBorderColor.value,
    };
  }

  factory MushafDesign.fromJson(Map<String, dynamic> json) {
    return MushafDesign(
      code: MushafCode.values.byName(json['code'] as String),
      sampleAsset: json['sampleAsset'] as String,
      surahAsset: json['surahAsset'] as String,
      zoomedInSurahAsset: json['zoomedInSurahAsset'] as String,
      verseAsset: json['verseAsset'] as String,
      frameAsset: json['frameAsset'] as String,
      hizbFullRes: json['hizbFullRes'] as int,
      hizbHalfRes: json['hizbHalfRes'] as int?,
      drawer1Color: Color(json['drawer1Color'] as int),
      drawer2Color: Color(json['drawer2Color'] as int),
      highLightFullColor: Color(json['highLightFullColor'] as int),
      highLightFadedColor: Color(json['highLightFadedColor'] as int),
      backgroundColor: Color(json['backgroundColor'] as int),
      activityCardBackgroundColor:
          Color(json['activityCardBackgroundColor'] as int),
      activityCardBorderColor: Color(json['activityCardBorderColor'] as int),
    );
  }

  @override
  List<Object?> get props => [
        code,
        surahAsset,
        zoomedInSurahAsset,
        verseAsset,
        frameAsset,
        hizbFullRes,
        hizbHalfRes,
        drawer1Color,
        drawer2Color,
        highLightFullColor,
        highLightFadedColor,
        backgroundColor,
        activityCardBackgroundColor,
        activityCardBorderColor,
      ];
}
