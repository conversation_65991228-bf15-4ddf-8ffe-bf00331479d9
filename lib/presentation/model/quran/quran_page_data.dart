import 'package:mushafi/presentation/model/quran/quran_page.dart';

class QuranPageData {
  QuranPage? previousRightPage;
  QuranPage? previousLeftPage;
  QuranPage? currentRightPage;
  QuranPage? currentLeftPage;
  QuranPage? nextRightPage;
  QuranPage? nextLeftPage;

  QuranPageData({
    this.previousRightPage,
    this.previousLeftPage,
    this.currentRightPage,
    this.currentLeftPage,
    this.nextRightPage,
    this.nextLeftPage,
  });

  bool isEmpty() {
    return previousRightPage == null &&
        previousLeftPage == null &&
        currentRightPage == null &&
        currentLeftPage == null &&
        nextRightPage == null &&
        nextLeftPage == null;
  }
}
