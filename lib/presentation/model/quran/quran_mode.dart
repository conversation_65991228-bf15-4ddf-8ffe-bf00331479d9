sealed class QuranMode {
  QuranMode();

  Map<String, dynamic> toJson();

  factory QuranMode.fromJson(Map<String, dynamic> json) {
    final type = json['type'] as String;
    switch (type) {
      case 'default':
        return DefaultQuranMode();
      case 'memorization':
        return MemorizationQuranMode(
          json['reveal'] as bool,
        );
      case 'recitationChecker':
        return RecitationCheckerQuranMode();
      default:
        throw Exception('Unknown QuranMode type: $type');
    }
  }
}

class DefaultQuranMode extends QuranMode {
  @override
  Map<String, dynamic> toJson() => {
        'type': 'default',
      };
}

class MemorizationQuranMode extends QuranMode {
  final bool reveal;

  MemorizationQuranMode(this.reveal);

  @override
  Map<String, dynamic> toJson() => {
        'type': 'memorization',
        'reveal': reveal,
      };
}

class RecitationCheckerQuranMode extends QuranMode {
  @override
  Map<String, dynamic> toJson() => {
        'type': 'recitationChecker',
      };
}
