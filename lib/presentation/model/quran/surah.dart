class Surah {
  final int id;
  final String revelationPlace;
  final String nameComplex;
  final String nameArabic;
  final int versesCount;
  final List<int> pages;
  final String englishTranslation;

  Surah({
    required this.id,
    required this.revelationPlace,
    required this.nameComplex,
    required this.nameArabic,
    required this.versesCount,
    required this.pages,
    required this.englishTranslation,
  });

  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      id: json['id'],
      revelationPlace: json['revelation_place'],
      nameComplex: json['name_complex'],
      nameArabic: json['name_arabic'],
      versesCount: json['verses_count'],
      pages: List<int>.from(json['pages']),
      englishTranslation: json['english_translation'],
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'revelation_place': revelationPlace,
        'name_complex': nameComplex,
        'name_arabic': nameArabic,
        'verses_count': versesCount,
        'pages': pages,
        'english_translation': englishTranslation,
      };
}
