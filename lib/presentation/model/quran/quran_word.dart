import 'package:equatable/equatable.dart';

class QuranWord extends Equatable {
  final int id;
  final String verseKey;
  final int lineNumber;
  final String codeV1;
  final String textUthmani;
  final String textImlaei;
  final bool isVerseNumber;
  final double? verseNumberSize;

  const QuranWord({
    required this.id,
    required this.verse<PERSON><PERSON>,
    required this.lineNumber,
    required this.codeV1,
    required this.textUthmani,
    required this.textImlaei,
    required this.isVerseNumber,
    this.verseNumberSize,
  });

  String getText() {
    return (codeV1.isNotEmpty)
        ? codeV1
        : (textUthmani.isNotEmpty)
            ? textUthmani
            : textImlaei;
  }

  bool get isGlobalFont {
    return textUthmani.isNotEmpty;
  }

  QuranWord copyWith({
    int? id,
    String? verseKey,
    int? lineNumber,
    String? codeV1,
    String? textUthmani,
    String? textImlaei,
    bool? isVerseNumber,
    double? verseNumberSize,
  }) {
    return QuranWord(
      id: id ?? this.id,
      verseKey: verseKey ?? this.verse<PERSON><PERSON>,
      lineNumber: lineNumber ?? this.lineNumber,
      codeV1: codeV1 ?? this.codeV1,
      textUthmani: textUthmani ?? this.textUthmani,
      textImlaei: textImlaei ?? this.textImlaei,
      isVerseNumber: isVerseNumber ?? this.isVerseNumber,
      verseNumberSize: verseNumberSize ?? this.verseNumberSize,
    );
  }

  factory QuranWord.fromJson(Map<String, dynamic> json) {
    return QuranWord(
      id: json['id'],
      verseKey: json['verseKey'],
      lineNumber: json['lineNumber'],
      codeV1: json['codeV1'],
      textUthmani: json['textUthmani'],
      textImlaei: json['textImlaei'],
      isVerseNumber: json['isVerseNumber'],
      verseNumberSize: json['verseNumberSize']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'verseKey': verseKey,
      'lineNumber': lineNumber,
      'codeV1': codeV1,
      'textUthmani': textUthmani,
      'textImlaei': textImlaei,
      'isVerseNumber': isVerseNumber,
      'verseNumberSize': verseNumberSize,
    };
  }

  @override
  List<Object?> get props => [
        id,
        verseKey,
        lineNumber,
        codeV1,
        textUthmani,
        textImlaei,
        isVerseNumber,
        verseNumberSize,
      ];
}
