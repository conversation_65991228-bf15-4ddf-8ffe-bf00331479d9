import 'package:equatable/equatable.dart';
import 'package:mushafi/presentation/model/quran/quran_audio_segment.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';

class QuranVerse extends Equatable {
  final int id;
  final int surahId;
  final int pageId;
  final String verseKey;
  final List<QuranWord> words;
  final List<QuranAudioSegment> audioSegments;
  final String translationOrTafseer;

  const QuranVerse({
    required this.id,
    required this.surahId,
    required this.pageId,
    required this.verseKey,
    required this.words,
    required this.audioSegments,
    required this.translationOrTafseer,
  });

  factory QuranVerse.fromJson(Map<String, dynamic> json) {
    return QuranVerse(
      id: json['id'],
      surahId: json['surahId'],
      pageId: json['pageId'],
      verseKey: json['verseKey'],
      words: List<QuranWord>.from(
          json['words'].map((word) => QuranWord.fromJson(word))),
      audioSegments: List<QuranAudioSegment>.from(json['audioSegments']
          .map((segment) => QuranAudioSegment.fromJson(segment))),
      translationOrTafseer: json['translationOrTafseer'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surahId': surahId,
      'pageId': pageId,
      'verseKey': verseKey,
      'words': words.map((word) => word.toJson()).toList(),
      'audioSegments':
          audioSegments.map((segment) => segment.toJson()).toList(),
      'translationOrTafseer': translationOrTafseer,
    };
  }

  @override
  List<Object?> get props => [
        id,
        surahId,
        pageId,
        verseKey,
        words,
        audioSegments,
        translationOrTafseer,
      ];
}
