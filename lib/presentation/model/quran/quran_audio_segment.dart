import 'package:equatable/equatable.dart';

class QuranAudioSegment extends Equatable {
  final int index;
  final int startTime;
  final int endTime;

  const QuranAudioSegment(
      {required this.index, required this.startTime, required this.endTime});

  factory QuranAudioSegment.fromJson(Map<String, dynamic> json) {
    return QuranAudioSegment(
      index: json['index'],
      startTime: json['startTime'],
      endTime: json['endTime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'index': index, 'startTime': startTime, 'endTime': endTime};
  }

  @override
  List<Object?> get props => [
        index,
        startTime,
        endTime,
      ];
}
