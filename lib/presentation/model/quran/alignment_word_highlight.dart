import 'package:equatable/equatable.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';

class AlignmentWordHighlight extends Equatable {
  final int verseId;
  final int wordIndex;
  final QuranWord word;
  final int pageNumber;
  final bool isCorrect;
  final bool isComplete;

  const AlignmentWordHighlight({
    required this.verseId,
    required this.wordIndex,
    required this.word,
    required this.pageNumber,
    required this.isCorrect,
    required this.isComplete,
  });

  AlignmentWordHighlight copyWith({
    int? verseId,
    int? wordIndex,
    QuranWord? word,
    int? pageNumber,
    bool? isCorrect,
    bool? isComplete,
  }) {
    return AlignmentWordHighlight(
      verseId: verseId ?? this.verseId,
      wordIndex: wordIndex ?? this.wordIndex,
      word: word ?? this.word,
      pageNumber: pageNumber ?? this.pageNumber,
      isCorrect: isCorrect ?? this.isCorrect,
      isComplete: isComplete ?? this.isComplete,
    );
  }

  @override
  List<Object?> get props =>
      [verseId, wordIndex, pageNumber, isCorrect, isComplete];

  factory AlignmentWordHighlight.fromJson(Map<String, dynamic> json) {
    return AlignmentWordHighlight(
      verseId: json['verseId'],
      wordIndex: json['wordIndex'],
      word: QuranWord.fromJson(json['word']),
      pageNumber: json['pageNumber'],
      isCorrect: json['isCorrect'],
      isComplete: json['isComplete'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'verseId': verseId,
      'wordIndex': wordIndex,
      'word': word.toJson(),
      'pageNumber': pageNumber,
      'isCorrect': isCorrect,
      'isComplete': isComplete,
    };
  }
}
