import 'package:mushafi/presentation/model/quran/alignment_type.dart';

class AlignmentWordResult {
  final String id;
  final int trackId;
  final int verseId;
  final String verseKey;
  final int wordId;
  final int wordIndex;
  final int pageNumber;
  final String verseCodeV1; // split by comma
  final String codeV1;
  final AlignmentType type;
  final bool isCorrect;
  final int time;
  final bool isHidden;

  AlignmentWordResult({
    this.id = '',
    required this.trackId,
    required this.verseId,
    required this.verseKey,
    required this.wordId,
    required this.wordIndex,
    required this.pageNumber,
    required this.verseCodeV1,
    required this.codeV1,
    required this.type,
    required this.isCorrect,
    required this.time,
    this.isHidden = false,
  });

  factory AlignmentWordResult.fromJson(Map<String, dynamic> json) {
    return AlignmentWordResult(
      id: json['id'] as String,
      trackId: json['trackId'] as int,
      verseId: json['verseId'] as int,
      verseKey: json['verseKey'] as String,
      wordId: json['wordId'] as int,
      wordIndex: json['wordIndex'] as int,
      pageNumber: json['pageNumber'] as int,
      verseCodeV1: json['verseCodeV1'] as String,
      codeV1: json['codeV1'] as String,
      type: AlignmentType.values.firstWhere(
        (element) => element.name == json['type'],
      ),
      isCorrect: json['isCorrect'] as bool,
      time: json['time'] as int,
      isHidden: json['isHidden'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trackId': trackId,
      'verseId': verseId,
      'verseKey': verseKey,
      'wordId': wordId,
      'wordIndex': wordIndex,
      'pageNumber': pageNumber,
      'verseCodeV1': verseCodeV1,
      'codeV1': codeV1,
      'type': type.name,
      'isCorrect': isCorrect,
      'time': time,
      'isHidden': isHidden,
    };
  }

  AlignmentWordResult copyWith({
    String? id,
    int? trackId,
    int? verseId,
    String? verseKey,
    int? wordId,
    int? wordIndex,
    int? pageNumber,
    String? verseCodeV1,
    String? codeV1,
    AlignmentType? type,
    bool? isCorrect,
    int? time,
    bool? isHidden,
  }) {
    return AlignmentWordResult(
      id: id ?? this.id,
      trackId: trackId ?? this.trackId,
      verseId: verseId ?? this.verseId,
      verseKey: verseKey ?? this.verseKey,
      wordId: wordId ?? this.wordId,
      wordIndex: wordIndex ?? this.wordIndex,
      pageNumber: pageNumber ?? this.pageNumber,
      verseCodeV1: verseCodeV1 ?? this.verseCodeV1,
      codeV1: codeV1 ?? this.codeV1,
      type: type ?? this.type,
      isCorrect: isCorrect ?? this.isCorrect,
      time: time ?? this.time,
      isHidden: isHidden ?? this.isHidden,
    );
  }
}
