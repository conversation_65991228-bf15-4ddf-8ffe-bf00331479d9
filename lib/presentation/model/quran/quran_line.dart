import 'package:mushafi/presentation/model/quran/quran_word.dart';

class QuranLine {
  List<QuranWord> words;
  String text;
  LineType type;
  double width = 0;
  double height = 0;
  double? additionalSpacePerGap;

  QuranLine({
    required this.words,
    required this.text,
    required this.type,
    this.width = 0,
    this.height = 0,
    this.additionalSpacePerGap,
  });

  factory QuranLine.fromJson(Map<String, dynamic> json) => QuranLine(
        words: (json['words'] as List)
            .map<QuranWord>((w) => QuranWord.fromJson(w))
            .toList(),
        text: json['text'],
        type: LineType.values.firstWhere((lt) => lt.index == json['type']),
        width: json['width'].toDouble(),
        height: json['height'].toDouble(),
        additionalSpacePerGap: json['additionalSpacePerGap']?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        'words': words.map((w) => w.toJson()).toList(),
        'text': text,
        'type': type.index,
        'width': width,
        'height': height,
        'additionalSpacePerGap': additionalSpacePerGap,
      };
}

enum LineType { quran, bismillah, surahHeader }
