import 'package:equatable/equatable.dart';

class QuranWidgetState extends Equatable {
  final bool showTopBar;
  final bool showBottomBar;

  const QuranWidgetState({
    required this.showTopBar,
    required this.showBottomBar,
  });

  bool get allShown => showTopBar && showBottomBar;

  bool get anyShown => showTopBar || showBottomBar;

  @override
  List<Object?> get props => [showTopBar, showBottomBar];
}
