import 'package:mushafi/presentation/model/quran/quran_verse.dart';

import 'quran_line.dart';

class QuranPage {
  final int surahNumber;
  final int juzNumber;
  final List<QuranVerse> verseList;
  final List<QuranLine> lineList;
  final int pageNumber;
  final List<int> fontByteList;
  final String fontName;
  final bool isGlobalFont;
  List<QuranLine> lineBreakList = [];

  QuranPage({
    required this.surahNumber,
    required this.juzNumber,
    required this.verseList,
    required this.lineList,
    required this.pageNumber,
    required this.fontByteList,
    required this.fontName,
    required this.isGlobalFont,
    this.lineBreakList = const [],
  });

  factory QuranPage.fromJson(Map<String, dynamic> json) {
    return QuranPage(
      surahNumber: json['surahNumber'],
      juzNumber: json['juzNumber'],
      verseList: (json['verseList'] as List)
          .map((v) => QuranVerse.fromJson(v))
          .toList(),
      lineList:
          (json['lineList'] as List).map((l) => QuranLine.fromJson(l)).toList(),
      pageNumber: json['pageNumber'],
      fontByteList: List<int>.from(json['fontByteList']),
      fontName: json['fontName'],
      isGlobalFont: json['isGlobalFont'],
      lineBreakList: (json['lineBreakList'] as List)
          .map((l) => QuranLine.fromJson(l))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'surahNumber': surahNumber,
      'juzNumber': juzNumber,
      'verseList': verseList.map((v) => v.toJson()).toList(),
      'lineList': lineList.map((l) => l.toJson()).toList(),
      'pageNumber': pageNumber,
      'fontByteList': fontByteList
          .toList(), // somehow this cannot be serialized, so use toList() to fix
      'fontName': fontName,
      'isGlobalFont': isGlobalFont,
      'lineBreakList': lineBreakList.map((l) => l.toJson()).toList(),
    };
  }
}
