import 'package:equatable/equatable.dart';
import 'package:mushafi/extension/verse_key_parser.dart';

class Track<PERSON>ang<PERSON> extends Equatable {
  final int startJuzNumber;
  final int startPageNumber;
  final int startSurahNumber;
  final int startVerseNumber;
  final int endJuzNumber;
  final int endPageNumber;
  final int endSurahNumber;
  final int endVerseNumber;

  const TrackRange({
    required this.startJuzNumber,
    required this.startPageNumber,
    required this.startSurahNumber,
    required this.startVerseNumber,
    required this.endJuzNumber,
    required this.endPageNumber,
    required this.endSurahNumber,
    required this.endVerseNumber,
  });

  String get startVerseKey => '$startSurahNumber:$startVerseNumber';

  String get endVerseKey => '$endSurahNumber:$endVerseNumber';

  TrackRange copyWith({
    int? startJuzNumber,
    int? startPageNumber,
    int? startSurahNumber,
    int? startVerseNumber,
    int? endJuzNumber,
    int? endPageNumber,
    int? endSurahNumber,
    int? endVerseNumber,
  }) =>
      TrackRange(
        startJuzNumber: startJuzNumber ?? this.startJuzNumber,
        startPageNumber: startPageNumber ?? this.startPageNumber,
        startSurahNumber: startSurahNumber ?? this.startSurahNumber,
        startVerseNumber: startVerseNumber ?? this.startVerseNumber,
        endJuzNumber: endJuzNumber ?? this.endJuzNumber,
        endPageNumber: endPageNumber ?? this.endPageNumber,
        endSurahNumber: endSurahNumber ?? this.endSurahNumber,
        endVerseNumber: endVerseNumber ?? this.endVerseNumber,
      );

  bool inRange(String verseKey) {
    final surahNumber = verseKey.parseSurahNumber();
    final verseNumber = verseKey.parseVerseNumber();

    final surahVerseNumber =
        "${surahNumber.toString().padLeft(3, '0')}${verseNumber.toString().padLeft(3, '0')}";
    final startSurahVerseNumber =
        "${startSurahNumber.toString().padLeft(3, '0')}${startVerseNumber.toString().padLeft(3, '0')}";
    final endSurahVerseNumber =
        "${endSurahNumber.toString().padLeft(3, '0')}${endVerseNumber.toString().padLeft(3, '0')}";

    return surahVerseNumber.compareTo(startSurahVerseNumber) >= 0 &&
        surahVerseNumber.compareTo(endSurahVerseNumber) <= 0;
  }

  @override
  List<Object> get props => [
        startJuzNumber,
        startPageNumber,
        startSurahNumber,
        startVerseNumber,
        endJuzNumber,
        endPageNumber,
        endSurahNumber,
        endVerseNumber,
      ];

  factory TrackRange.fromJson(Map<String, dynamic> json) {
    return TrackRange(
      startJuzNumber: json['startJuzNumber'],
      startPageNumber: json['startPageNumber'],
      startSurahNumber: json['startSurahNumber'],
      startVerseNumber: json['startVerseNumber'],
      endJuzNumber: json['endJuzNumber'],
      endPageNumber: json['endPageNumber'],
      endSurahNumber: json['endSurahNumber'],
      endVerseNumber: json['endVerseNumber'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startJuzNumber': startJuzNumber,
      'startPageNumber': startPageNumber,
      'startSurahNumber': startSurahNumber,
      'startVerseNumber': startVerseNumber,
      'endJuzNumber': endJuzNumber,
      'endPageNumber': endPageNumber,
      'endSurahNumber': endSurahNumber,
      'endVerseNumber': endVerseNumber,
    };
  }
}
