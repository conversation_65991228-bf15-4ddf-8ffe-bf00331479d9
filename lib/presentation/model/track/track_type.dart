import 'package:collection/collection.dart';

enum TrackType {
  listening('listening', 'listening'),
  readingCoverToCover('reading', 'cover_to_cover'),
  readingHabitual('reading', 'habitual'),
  readingWithAi('reading', 'recite_with_ai'),
  memorizing('memorizing', 'memorizing');

  final String activityType;
  final String activitySubType;

  const TrackType(this.activityType, this.activitySubType);

  static TrackType? fromType(String type) {
    return TrackType.values
        .firstWhereOrNull((trackType) => trackType.activityType == type);
  }

  static TrackType? fromSubType(String subType) {
    return TrackType.values
        .firstWhereOrNull((trackType) => trackType.activitySubType == subType);
  }
}
