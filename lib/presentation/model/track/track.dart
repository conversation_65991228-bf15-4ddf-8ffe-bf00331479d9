import 'package:equatable/equatable.dart';
import 'package:mushafi/presentation/model/nullable.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/presentation/model/recitation/last_recited_word.dart';
import 'package:mushafi/presentation/model/recitation/reciter.dart';
import 'package:mushafi/presentation/model/track/track_range_portion.dart';

import 'track_range.dart';
import 'track_schedule.dart';
import 'track_type.dart';

class Track extends Equatable {
  final int id;
  final TrackRange range;
  final String? reciterKey;
  final Reciter? reciter;
  final int reciteVerseCount;
  final bool isLoop;
  final bool enableEnglishRecitation;
  final String? bluetoothAddress;
  final TrackType? type;
  final MushafDesign? design;
  final String? lastHighlightedVerseKey;
  final LastRecitedWord? lastRecitedWord;
  final String? name;
  final TrackRangePortion rangePortion;
  final TrackSchedule? schedule;
  final bool enableRecitationChecker;
  final ZoomState zoomState;
  final int? reminderDayNumber;
  final String? reminderTime;
  final int? openingTimestamp;
  final bool enableDownloadAudio;
  final int audioSize;
  final bool isAudioDownloaded;

  const Track({
    this.id = 0,
    required this.range,
    this.reciterKey,
    this.reciter,
    required this.reciteVerseCount,
    required this.isLoop,
    required this.enableEnglishRecitation,
    this.bluetoothAddress,
    this.type,
    this.design,
    this.lastHighlightedVerseKey,
    this.lastRecitedWord,
    this.name,
    required this.rangePortion,
    this.schedule,
    required this.enableRecitationChecker,
    required this.zoomState,
    this.reminderDayNumber,
    this.reminderTime,
    this.openingTimestamp,
    required this.enableDownloadAudio,
    required this.audioSize,
    required this.isAudioDownloaded,
  });

  factory Track.create({
    required TrackRange range,
    Reciter? reciter,
    required int reciteVerseCount,
    required bool isLoop,
    required bool enableEnglishRecitation,
    String? bluetoothAddress,
    TrackType? type,
    MushafDesign? design,
    String? name,
    required TrackRangePortion rangePortion,
    TrackSchedule? schedule,
    required bool enableRecitationChecker,
    required ZoomState zoomState,
    int? reminderDayNumber,
    String? reminderTime,
    required bool enableDownloadAudio,
    required int audioSize,
    required bool isAudioDownloaded,
  }) {
    return Track(
      range: range,
      reciterKey: reciter?.key,
      reciter: reciter,
      reciteVerseCount: reciteVerseCount,
      isLoop: isLoop,
      enableEnglishRecitation: enableEnglishRecitation,
      bluetoothAddress: bluetoothAddress,
      type: type,
      design: design,
      name: name,
      rangePortion: rangePortion,
      schedule: schedule,
      enableRecitationChecker: enableRecitationChecker,
      zoomState: zoomState,
      reminderDayNumber: reminderDayNumber,
      reminderTime: reminderTime,
      enableDownloadAudio: enableDownloadAudio,
      audioSize: audioSize,
      isAudioDownloaded: isAudioDownloaded,
    );
  }

  Track copyWith({
    int? id,
    TrackRange? range,
    Nullable<String>? reciterKey,
    Nullable<Reciter>? reciter,
    int? reciteVerseCount,
    bool? isLoop,
    bool? enableEnglishRecitation,
    Nullable<String>? bluetoothAddress,
    Nullable<TrackType>? type,
    Nullable<MushafDesign>? design,
    Nullable<String>? lastHighlightedVerseKey,
    Nullable<LastRecitedWord>? lastRecitedWord,
    Nullable<String>? name,
    TrackRangePortion? rangePortion,
    Nullable<TrackSchedule>? schedule,
    bool? enableRecitationChecker,
    ZoomState? zoomState,
    Nullable<int>? reminderDayNumber,
    Nullable<String>? reminderTime,
    Nullable<int>? openingTimestamp,
    bool? enableDownloadAudio,
    int? audioSize,
    bool? isAudioDownloaded,
  }) {
    return Track(
      id: id ?? this.id,
      range: range ?? this.range,
      reciterKey: reciterKey != null
          ? reciterKey.value
          : reciter != null
              ? reciter.value?.key
              : this.reciterKey,
      reciter: reciter != null ? reciter.value : this.reciter,
      reciteVerseCount: reciteVerseCount ?? this.reciteVerseCount,
      isLoop: isLoop ?? this.isLoop,
      enableEnglishRecitation:
          enableEnglishRecitation ?? this.enableEnglishRecitation,
      bluetoothAddress: bluetoothAddress != null
          ? bluetoothAddress.value
          : this.bluetoothAddress,
      type: type != null ? type.value : this.type,
      design: design != null ? design.value : this.design,
      lastHighlightedVerseKey: lastHighlightedVerseKey != null
          ? lastHighlightedVerseKey.value
          : this.lastHighlightedVerseKey,
      lastRecitedWord: lastRecitedWord != null
          ? lastRecitedWord.value
          : this.lastRecitedWord,
      name: name != null ? name.value : this.name,
      rangePortion: rangePortion ?? this.rangePortion,
      schedule: schedule != null ? schedule.value : this.schedule,
      enableRecitationChecker:
          enableRecitationChecker ?? this.enableRecitationChecker,
      zoomState: zoomState ?? this.zoomState,
      reminderDayNumber: reminderDayNumber != null
          ? reminderDayNumber.value
          : this.reminderDayNumber,
      reminderTime:
          reminderTime != null ? reminderTime.value : this.reminderTime,
      openingTimestamp: openingTimestamp != null
          ? openingTimestamp.value
          : this.openingTimestamp,
      enableDownloadAudio: enableDownloadAudio ?? this.enableDownloadAudio,
      audioSize: audioSize ?? this.audioSize,
      isAudioDownloaded: isAudioDownloaded ?? this.isAudioDownloaded,
    );
  }

  static Track createMemorizationTemplate(String name, bool isDarkMode) {
    const range = TrackRange(
      startJuzNumber: 30,
      startPageNumber: 604,
      startSurahNumber: 112,
      startVerseNumber: 1,
      endJuzNumber: 30,
      endPageNumber: 604,
      endSurahNumber: 112,
      endVerseNumber: 4,
    );
    return Track.create(
      range: range,
      reciter: null,
      reciteVerseCount: 1,
      isLoop: false,
      enableEnglishRecitation: false,
      bluetoothAddress: null,
      type: TrackType.memorizing,
      design: MushafDesign.getList(isDarkMode).first,
      name: name,
      rangePortion: TrackRangePortion.surah,
      schedule: null,
      enableRecitationChecker: false,
      zoomState: ZoomState.defaultZoom,
      reminderDayNumber: null,
      reminderTime: null,
      enableDownloadAudio: false,
      audioSize: 0,
      isAudioDownloaded: false,
    );
  }

  static Track createAlKahfHabitualTemplate(String name, bool isDarkMode) {
    const range = TrackRange(
      startJuzNumber: 15,
      startPageNumber: 293,
      startSurahNumber: 18,
      startVerseNumber: 1,
      endJuzNumber: 15,
      endPageNumber: 293,
      endSurahNumber: 18,
      endVerseNumber: 1,
    );
    return Track.create(
      range: range,
      reciter: null,
      reciteVerseCount: 1,
      isLoop: false,
      enableEnglishRecitation: false,
      bluetoothAddress: null,
      type: TrackType.readingHabitual,
      design: MushafDesign.getList(isDarkMode).first,
      name: name,
      rangePortion: TrackRangePortion.range,
      schedule: null,
      enableRecitationChecker: false,
      zoomState: ZoomState.defaultZoom,
      reminderDayNumber: 6,
      reminderTime: '8:00 PM',
      enableDownloadAudio: false,
      audioSize: 0,
      isAudioDownloaded: false,
    );
  }

  static Track createReciteWithAITemplate(String name, bool isDarkMode) {
    const range = TrackRange(
      startJuzNumber: 1,
      startPageNumber: 1,
      startSurahNumber: 1,
      startVerseNumber: 1,
      endJuzNumber: 1,
      endPageNumber: 1,
      endSurahNumber: 1,
      endVerseNumber: 7,
    );
    return Track.create(
      range: range,
      reciter: null,
      reciteVerseCount: 1,
      isLoop: false,
      enableEnglishRecitation: false,
      bluetoothAddress: null,
      type: TrackType.readingWithAi,
      design: MushafDesign.getList(isDarkMode).first,
      name: name,
      rangePortion: TrackRangePortion.surah,
      schedule: null,
      enableRecitationChecker: false,
      zoomState: ZoomState.defaultZoom,
      reminderDayNumber: null,
      reminderTime: null,
      enableDownloadAudio: false,
      audioSize: 0,
      isAudioDownloaded: false,
    );
  }

  @override
  List<Object?> get props => [
        id,
        range,
        reciterKey,
        reciter,
        reciteVerseCount,
        isLoop,
        enableEnglishRecitation,
        bluetoothAddress,
        type,
        design,
        lastHighlightedVerseKey,
        lastRecitedWord,
        name,
        rangePortion,
        schedule,
        enableRecitationChecker,
        zoomState,
        reminderDayNumber,
        reminderTime,
        openingTimestamp,
        enableDownloadAudio,
        audioSize,
        isAudioDownloaded,
      ];

  factory Track.fromJson(Map<String, dynamic> json) {
    return Track(
      id: json['id'],
      range: TrackRange.fromJson(json['range']),
      reciterKey: json['reciterKey'],
      reciter:
          json['reciter'] != null ? Reciter.fromJson(json['reciter']) : null,
      reciteVerseCount: json['reciteVerseCount'],
      isLoop: json['isLoop'],
      enableEnglishRecitation: json['enableEnglishRecitation'],
      bluetoothAddress: json['bluetoothAddress'],
      type: json['type'] != null ? TrackType.fromSubType(json['type']) : null,
      design:
          json['design'] != null ? MushafDesign.fromJson(json['design']) : null,
      lastHighlightedVerseKey: json['lastHighlightedVerseKey'],
      lastRecitedWord: json['lastRecitedWord'] != null
          ? LastRecitedWord.fromJson(json['lastRecitedWord'])
          : null,
      name: json['name'],
      rangePortion: TrackRangePortion.fromName(json['rangePortion']) ??
          TrackRangePortion.range,
      schedule: json['schedule'] != null
          ? TrackSchedule.fromJson(json['schedule'])
          : null,
      enableRecitationChecker: json['enableRecitationChecker'],
      zoomState: ZoomState.fromName(json['zoomState']) ?? ZoomState.defaultZoom,
      reminderDayNumber: json['reminderDayNumber'],
      reminderTime: json['reminderTime'],
      openingTimestamp: json['openingTimestamp'],
      enableDownloadAudio: json['enableDownloadAudio'],
      audioSize: json['audioSize'],
      isAudioDownloaded: json['isAudioDownloaded'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'range': range.toJson(),
      'reciterKey': reciterKey,
      'reciter': reciter?.toJson(),
      'reciteVerseCount': reciteVerseCount,
      'isLoop': isLoop,
      'enableEnglishRecitation': enableEnglishRecitation,
      'bluetoothAddress': bluetoothAddress,
      'type': type?.activitySubType,
      'design': design?.toJson(),
      'lastHighlightedVerseKey': lastHighlightedVerseKey,
      'lastRecitedWord': lastRecitedWord?.toJson(),
      'name': name,
      'rangePortion': rangePortion.name,
      'schedule': schedule?.toJson(),
      'enableRecitationChecker': enableRecitationChecker,
      'zoomState': zoomState.name,
      'reminderDayNumber': reminderDayNumber,
      'reminderTime': reminderTime,
      'openingTimestamp': openingTimestamp,
      'enableDownloadAudio': enableDownloadAudio,
      'audioSize': audioSize,
      'isAudioDownloaded': isAudioDownloaded,
    };
  }
}
