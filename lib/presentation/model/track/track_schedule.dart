import 'package:equatable/equatable.dart';

class TrackSchedule extends Equatable {
  final int scheduleDayNumber;
  final String scheduleTime;

  const TrackSchedule({
    required this.scheduleDayNumber,
    required this.scheduleTime,
  });

  @override
  List<Object> get props => [scheduleDayNumber, scheduleTime];

  factory TrackSchedule.fromJson(Map<String, dynamic> json) {
    return TrackSchedule(
      scheduleDayNumber: json['scheduleDayNumber'],
      scheduleTime: json['scheduleTime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'scheduleDayNumber': scheduleDayNumber,
      'scheduleTime': scheduleTime,
    };
  }
}
