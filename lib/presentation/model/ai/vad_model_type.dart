enum VadModelType {
  // stride2('hb_vad_s2.onnx', 'Q1u', 2),
  stride2Q('hb_vad_cond_prof_s2_q.onnx', 'Q1u', 2),
  // stride3('hb_vad_s3.onnx', 'Q1t', 3),
  stride3Q('hb_vad_cond_prof_s3_q.onnx', 'Q1t', 3),
  // stride4('hb_vad_s4.onnx', 'Q1s', 4),
  stride4Q('hb_vad_cond_prof_s4_q.onnx', 'Q1s', 4);

  final String name;
  final String readableName;
  final int layerStride;

  const VadModelType(this.name, this.readableName, this.layerStride);
}
