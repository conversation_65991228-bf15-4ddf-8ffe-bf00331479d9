import 'dart:io';

import 'package:asset_delivery/asset_delivery.dart';
import 'package:mushafi/data/source/asset_pack_manager.dart';
import 'package:mushafi/data/source/minio_data_manager.dart';
import 'package:mushafi/presentation/model/ai/asr_model_type.dart';
import 'package:mushafi/presentation/model/ai/cond_model_type.dart';
import 'package:mushafi/presentation/model/ai/vad_model_type.dart';
import 'package:path_provider/path_provider.dart';

class InferenceModel {
  final String name;
  final int layerStride;
  final String condName;
  final String vadName;
  final String encodedVersesName = "encoded-quran-corpus-imlaei-tashkeel.txt";
  final String version;

  static const int modelCompat = 4;

  InferenceModel({
    required this.name,
    required this.layerStride,
    required this.condName,
    required this.vadName,
    required this.version,
  });

  factory InferenceModel.fromType(
    AsrModelType asrModelType,
    CondModelType condModelType,
    VadModelType vadModelType,
    String version,
  ) {
    return InferenceModel(
      name: asrModelType.name,
      layerStride: vadModelType.layerStride,
      condName: condModelType.name,
      vadName: vadModelType.name,
      version: version,
    );
  }

  Future<String?> get path => findModelPath(name);

  Future<String?> get condPath => findModelPath(condName);

  Future<String?> get vadPath => findModelPath(vadName);

  Future<String?> get encodedVersesPath =>
      findEncodedVersesPath(encodedVersesName);

  Future<File?> get file => findModelFile(name);

  Future<File?> get condFile => findModelFile(condName);

  Future<File?> get vadFile => findModelFile(vadName);

  Future<File?> get encodedVersesFile =>
      findEncodedVersesFile(encodedVersesName);

  Future<String?> findModelPath(String name) async {
    if (Platform.isAndroid) {
      final folderPath = await AssetDelivery.getAssetPackPath(
        assetPackName: AssetPackManager.inferencePackName,
        count: 4,
        namingPattern: '', // todo for ios
        fileExtension: '', // todo for ios
      );

      if (folderPath == null) return null;

      return '$folderPath/$name';
    }

    final dataDirectory = await getApplicationDocumentsDirectory();
    final destPath =
        '${dataDirectory.path}/${MinioDataManager.modelsPath}/$version/$name';
    final destFile = File(destPath);
    final destFileExists = await destFile.exists();

    if (!destFileExists) {
      return null;
    }

    if (await destFile.length() == 0) {
      return null;
    }

    return destPath;
  }

  Future<File?> findModelFile(String name) async {
    if (Platform.isAndroid) {
      final folderPath = await AssetDelivery.getAssetPackPath(
        assetPackName: AssetPackManager.inferencePackName,
        count: 4,
        namingPattern: '',
        fileExtension: '',
      );

      if (folderPath == null) return null;

      return File('$folderPath/$name');
    }

    final dataDirectory = await getApplicationDocumentsDirectory();
    final destPath =
        '${dataDirectory.path}/${MinioDataManager.modelsPath}/$version/$name';
    final destFile = File(destPath);
    final destFileExists = await destFile.exists();

    if (!destFileExists) {
      return null;
    }

    if (await destFile.length() == 0) {
      return null;
    }

    return destFile;
  }

  Future<String?> findEncodedVersesPath(String name) async {
    if (Platform.isAndroid) {
      final folderPath = await AssetDelivery.getAssetPackPath(
        assetPackName: AssetPackManager.inferencePackName,
        count: 4,
        namingPattern: '',
        fileExtension: '',
      );

      if (folderPath == null) return null;

      return '$folderPath/$name';
    }

    final dataDirectory = await getApplicationDocumentsDirectory();
    final destPath =
        '${dataDirectory.path}/${MinioDataManager.quranCorpusPath}/$name';
    final destFile = File(destPath);
    final destFileExists = await destFile.exists();

    if (!destFileExists) {
      return null;
    }

    return destPath;
  }

  Future<File?> findEncodedVersesFile(String name) async {
    if (Platform.isAndroid) {
      final folderPath = await AssetDelivery.getAssetPackPath(
        assetPackName: AssetPackManager.inferencePackName,
        count: 4,
        namingPattern: '',
        fileExtension: '',
      );

      if (folderPath == null) return null;

      return File('$folderPath/$name');
    }

    final dataDirectory = await getApplicationDocumentsDirectory();
    final destPath =
        '${dataDirectory.path}/${MinioDataManager.quranCorpusPath}/$name';
    final destFile = File(destPath);
    final destFileExists = await destFile.exists();

    if (!destFileExists) {
      return null;
    }

    if (await destFile.length() == 0) {
      return null;
    }

    return destFile;
  }
}
