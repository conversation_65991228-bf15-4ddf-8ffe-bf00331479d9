import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/notifier/home_completely_shown_notifier.dart';
import 'package:mushafi/presentation/notifier/request_page_navigation_notifier.dart';

final homeCompletelyShownProvider =
    ChangeNotifierProvider((ref) => HomeCompletelyShownNotifier());

final requestPageNavigationProvider =
    StateNotifierProvider<RequestPageNavigationNotifier, int?>((ref) {
  return RequestPageNavigationNotifier();
});
