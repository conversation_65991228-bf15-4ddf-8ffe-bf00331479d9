import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/activity/activity_input_screen.dart';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/notifier/page_navigation_notifier.dart';
import 'package:mushafi/presentation/quran/quran_screen.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:permission_handler/permission_handler.dart';

final surahListProvider = FutureProvider<List<Surah>>((ref) async {
  final jsonDataRepository = ref.watch(jsonDataRepositoryProvider);
  final surahList = await jsonDataRepository.getSurahList();
  return surahList;
});

final pageNavigationProvider =
    StateNotifierProvider<PageNavigationNotifier, int?>((ref) {
  return PageNavigationNotifier();
});

final selectedTrackProvider = StateProvider<Track?>((ref) => null);

// todo remove, not used
class MainScreen extends ConsumerStatefulWidget {
  const MainScreen({super.key});

  @override
  ConsumerState<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends ConsumerState<MainScreen>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  final inferencePipeline = InferencePipeline.getInstance();

  void initInferencePipeline() async {
    // final model = InferenceModel.fromType(
    //   AsrModelType.quantized,
    //   CondModelType.quantized,
    //   PreferenceStorage.getSelectedModel(),
    // );
    //
    // if (await model.modelsExist()) {
    //   inferencePipeline.initialize(model);
    // }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final micPermissionStatus = await Permission.microphone.status;

      if (micPermissionStatus.isGranted) {
        initInferencePipeline();
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.inactive) {
      setState(() {
        inferencePipeline.reset();
        inferencePipeline.stop();
      });
    } else if (state == AppLifecycleState.resumed) {
      final micPermissionStatus = await Permission.microphone.status;

      if (micPermissionStatus.isGranted) {
        initInferencePipeline();
      }
    } else if (state == AppLifecycleState.detached) {
      inferencePipeline.delete();
    }

    super.didChangeAppLifecycleState(state);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedTrack = ref.watch(selectedTrackProvider);
    return Scaffold(
      floatingActionButton: (selectedTrack != null) ? Container() : _buildFab(),
      body: Stack(
        children: [
          const QuranScreen(),
          const HomeScreen(),
        ],
      ),
    );
  }

  Widget _buildFab() {
    return FloatingActionButton.small(
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const ActivityInputScreen(),
          ),
        );
      },
      shape: const CircleBorder(),
      backgroundColor: ColorResource.primary(context.isDarkMode),
      child: const Icon(
        Icons.add_rounded,
        color: Colors.white,
      ),
    );
  }
}
