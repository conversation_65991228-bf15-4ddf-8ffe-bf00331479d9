import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';

class TrackHighlightedVerseNotifier extends AsyncNotifier<QuranVerse?> {
  @override
  FutureOr<QuranVerse?> build() {
    return null;
  }

  void fetchHighlightedVerse(QuranVerse quranVerse) {
    state = AsyncValue.loading();
    state = AsyncValue.data(quranVerse);
  }
}
