import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/presentation/model/translation/translation.dart';
import 'package:mushafi/provider/repository_provider.dart';

class TranslationTafsirDataInitNotifier extends AsyncNotifier<Translation?> {
  @override
  FutureOr<Translation?> build() async {
    return initTranslationTafsirData();
  }

  Future<Translation?> initTranslationTafsirData() async {
    final defaultTranslationKey = PreferenceStorage.getDefaultTranslationKey();
    final fileName = "$defaultTranslationKey.fb";
    final file = await ref
        .watch(minioDataRepositoryProvider)
        .getTranslationTafsir(fileName: fileName);
    final fileExists = file != null && await file.exists();

    if (fileExists) {
      await ref.watch(quranRepositoryProvider).initTranslationData(file);
    } else {
      throw Exception('Failed to load translation/tafsir data');
    }

    final translationList =
        await ref.read(minioDataRepositoryProvider).getTranslationList();
    return translationList.firstWhereOrNull((translation) {
      return translation.key == defaultTranslationKey;
    });
  }

  Future<void> fetchTranslationTafsirData() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      return initTranslationTafsirData();
    });
  }
}
