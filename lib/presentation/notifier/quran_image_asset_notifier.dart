import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_image_asset.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/quran_image_helper.dart';

class QuranImageAssetNotifier extends AsyncNotifier<QuranImageAsset> {
  @override
  FutureOr<QuranImageAsset> build() async {
    final isDarkMode = await CommonUtils.isDarkMode;
    return QuranImageHelper.getQuranThemeAsset(
        MushafDesign.getDefault(isDarkMode), isDarkMode);
  }

  Future<void> updateMushafDesign(MushafDesign mushafDesign) async {
    if (state.value?.design == mushafDesign) {
      return;
    }

    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final isDarkMode = await CommonUtils.isDarkMode;
      return QuranImageHelper.getQuranThemeAsset(mushafDesign, isDarkMode);
    });
  }
}
