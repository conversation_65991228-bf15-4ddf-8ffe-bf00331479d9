import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/model/quran/highlighted_verse_key.dart';

class HighlightedVerseKeyNotifier extends StateNotifier<HighlightedVerseKey?> {
  HighlightedVerseKeyNotifier() : super(null);

  void highlight(String? verseKey, [bool? quranFocused]) {
    state = (verseKey != null)
        ? HighlightedVerseKey(verseKey, quranFocused ?? false)
        : null;
  }
}
