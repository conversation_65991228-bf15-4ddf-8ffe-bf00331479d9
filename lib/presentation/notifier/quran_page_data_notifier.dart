import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/domain/quran_repository.dart';
import 'package:mushafi/presentation/model/quran/quran_page.dart';
import 'package:mushafi/presentation/model/quran/quran_page_data.dart';

typedef QuranPageDataState = ({
  QuranPageData data,
  bool isInitial,
  bool requestListeningTrack
});

class QuranPageDataNotifier extends StateNotifier<QuranPageDataState> {
  final QuranRepository quranRepository;

  QuranPageDataNotifier(this.quranRepository)
      : super((
          data: QuranPageData(),
          isInitial: true,
          requestListeningTrack: false
        ));

  Future<void> updatePageData({
    int? pageNumber,
    int? spreadNumber,
    required bool isInitial,
    bool requestListeningTrack = false,
  }) async {
    if (spreadNumber != null) {
      final QuranPage? previousRightQuranPage;
      final QuranPage? previousLeftQuranPage;
      final QuranPage? currentRightQuranPage;
      final QuranPage? currentLeftQuranPage;
      final QuranPage? nextRightQuranPage;
      final QuranPage? nextLeftQuranPage;

      final rightPageNumber = spreadNumber * 2 - 1;
      final leftPageNumber = spreadNumber * 2;

      previousRightQuranPage =
          await quranRepository.getCompleteQuranPage(rightPageNumber - 2);
      previousLeftQuranPage =
          await quranRepository.getCompleteQuranPage(rightPageNumber - 1);
      currentRightQuranPage =
          await quranRepository.getCompleteQuranPage(rightPageNumber);
      currentLeftQuranPage =
          await quranRepository.getCompleteQuranPage(leftPageNumber);
      nextRightQuranPage =
          await quranRepository.getCompleteQuranPage(leftPageNumber + 1);
      nextLeftQuranPage =
          await quranRepository.getCompleteQuranPage(leftPageNumber + 2);

      final fontFutureIterable = [
        previousRightQuranPage,
        previousLeftQuranPage,
        currentRightQuranPage,
        currentLeftQuranPage,
        nextRightQuranPage,
        nextLeftQuranPage
      ].whereType<QuranPage>().map((quranPage) {
        final fontByteList = quranPage.fontByteList;
        final byteData = ByteData.view(Uint8List.fromList(fontByteList).buffer);
        final fontLoader = FontLoader(quranPage.fontName);
        fontLoader.addFont(Future.value(byteData));
        return fontLoader.load();
      });

      await Future.wait(fontFutureIterable);

      final data = QuranPageData(
        previousRightPage: previousRightQuranPage,
        previousLeftPage: previousLeftQuranPage,
        currentRightPage: currentRightQuranPage,
        currentLeftPage: currentLeftQuranPage,
        nextRightPage: nextRightQuranPage,
        nextLeftPage: nextLeftQuranPage,
      );
      state = (
        data: data,
        isInitial: isInitial,
        requestListeningTrack: requestListeningTrack
      );
    } else if (pageNumber != null) {
      final previousQuranPage =
          await quranRepository.getCompleteQuranPage(pageNumber - 1);
      final currentQuranPage =
          await quranRepository.getCompleteQuranPage(pageNumber);
      final nextQuranPage =
          await quranRepository.getCompleteQuranPage(pageNumber + 1);

      // load the font
      final fontFutureIterable = [
        previousQuranPage,
        currentQuranPage,
        nextQuranPage
      ].whereType<QuranPage>().map((quranPage) {
        final fontByteList = quranPage.fontByteList;
        final byteData = ByteData.view(Uint8List.fromList(fontByteList).buffer);
        final fontLoader = FontLoader(quranPage.fontName);
        fontLoader.addFont(Future.value(byteData));
        return fontLoader.load();
      });

      await Future.wait(fontFutureIterable);

      final data = QuranPageData(
        previousRightPage: previousQuranPage,
        currentRightPage: currentQuranPage,
        nextRightPage: nextQuranPage,
      );
      state = (
        data: data,
        isInitial: isInitial,
        requestListeningTrack: requestListeningTrack
      );
    }
  }
}
