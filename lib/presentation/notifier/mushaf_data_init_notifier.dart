import 'dart:async';
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/integer_extension.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/provider/repository_provider.dart';

class MushafDataInitNotifier extends AsyncNotifier<void> {
  @override
  FutureOr<void> build() async {
    return buildData();
  }

  Future<void> buildData() async {
    bool fetch = false;
    int retryCount = 0;

    while (retryCount < 3) {
      try {
        await initMushafData(fetch: fetch);
        await loadCodeV1Fonts();
        break;
      } catch (e, st) {
        fetch = true;
        retryCount++;
        talker.handle(e, st);
        await Future.delayed(const Duration(seconds: 1));
      }
    }

    if (retryCount == 3) {
      throw Exception('Failed to download mushaf data. Please try again later');
    }
  }

  Future<void> initMushafData({bool fetch = false}) async {
    final mushafFile = fetch
        ? await ref.read(minioDataRepositoryProvider).fetchMushaf()
        : await ref.read(minioDataRepositoryProvider).getMushaf();
    final mushafFileExists = mushafFile != null && await mushafFile.exists();

    if (mushafFileExists) {
      await ref.read(quranRepositoryProvider).initQuranData(mushafFile);
    } else {
      throw Exception('Failed to load mushaf data');
    }
  }

  Future<void> fetchMushafData() async {
    state = AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      initMushafData();
    });
  }

  // todo: handle load other fonts
  Future<void> loadCodeV1Fonts() async {
    final quranRepository = ref.read(quranRepositoryProvider);

    final pageList = await quranRepository.getPageList();
    if (pageList != null) {
      for (final page in pageList) {
        if (page.font != null) {
          await loadFontFromList(
            Uint8List.fromList(page.font!),
            fontFamily: page.pageId.threeDigitsFormat(),
          );
        }
      }
    }
  }
}
