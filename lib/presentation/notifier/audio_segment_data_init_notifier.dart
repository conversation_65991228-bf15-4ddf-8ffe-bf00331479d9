import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/presentation/model/quran/audio_segment_init_state.dart';
import 'package:mushafi/provider/repository_provider.dart';

class AudioSegmentDataInitNotifier
    extends AsyncNotifier<AudioSegmentInitState?> {
  @override
  FutureOr<AudioSegmentInitState?> build() async {
    return initAudioSegmentData();
  }

  Future<AudioSegmentInitState?> initAudioSegmentData({
    String? reciterKey,
    bool? requestListeningTrack,
  }) async {
    final key = reciterKey ?? PreferenceStorage.getDefaultReciterKey();
    final fileName = "$key.fb";
    final file = await ref
        .read(minioDataRepositoryProvider)
        .getAudioSegment(fileName: fileName);
    final fileExists = file != null && await file.exists();

    if (fileExists) {
      await ref.read(quranRepositoryProvider).initAudioSegmentData(file);
    } else {
      throw Exception('Failed to load audio segment data');
    }

    final reciterList =
        await ref.read(minioDataRepositoryProvider).getReciterList();
    final reciter =
        reciterList.firstWhereOrNull((reciter) => reciter.key == key);
    return AudioSegmentInitState(reciter, requestListeningTrack);
  }

  Future<void> fetchAudioSegmentData(
      {String? reciterKey, bool? requestListeningTrack}) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return initAudioSegmentData(
        reciterKey: reciterKey,
        requestListeningTrack: requestListeningTrack,
      );
    });
  }
}
