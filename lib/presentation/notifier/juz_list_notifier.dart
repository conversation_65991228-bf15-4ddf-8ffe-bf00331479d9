import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/generated/flatbuffer_flatbuffer_generated.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/provider/repository_provider.dart';

class JuzListNotifier extends AsyncNotifier<List<Juz>> {
  @override
  FutureOr<List<Juz>> build() async {
    final mushafDataInit = ref.watch(mushafDataInitProvider);

    mushafDataInit.when(
      data: (_) async {
        state = const AsyncValue.loading();
        state = await AsyncValue.guard(() async {
          final quranRepository = ref.watch(quranRepositoryProvider);
          final juzList = await quranRepository.getJuzList();
          return juzList ?? [];
        });
      },
      error: (error, st) => state = AsyncValue.error(error, st),
      loading: () => state = const AsyncValue.loading(),
    );

    return future;
  }
}
