import 'dart:ui' as ui;

import 'package:adaptive_theme/adaptive_theme.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/resource/color_resource.dart';

class ThemeOptionScreen extends StatefulWidget {
  const ThemeOptionScreen({super.key});

  @override
  State<ThemeOptionScreen> createState() => _ThemeOptionScreenState();
}

class _ThemeOptionScreenState extends State<ThemeOptionScreen> {
  final themeList = AdaptiveThemeMode.values;

  AdaptiveThemeMode selectedTheme = AdaptiveThemeMode.system;

  void updateTheme(AdaptiveThemeMode themeMode) {
    AdaptiveTheme.of(context).setThemeMode(themeMode);
    setState(() {
      selectedTheme = themeMode;
    });
  }

  @override
  void initState() {
    FirebaseAnalytics.instance.logScreenView(screenName: "theme_settings");

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        selectedTheme = AdaptiveTheme.of(context).mode;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(),
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: buildThemeOptions(),
    );
  }

  Widget buildThemeOptions() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: ColorResource.backgroundWhite(context.isDarkMode),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    updateTheme(AdaptiveThemeMode.system);
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      buildPreview(
                        themeMode: AdaptiveThemeMode.system,
                        preview: Row(
                          children: [
                            Expanded(
                                flex: 1, child: Container(color: Colors.black)),
                            Expanded(
                                flex: 1, child: Container(color: Colors.white)),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        context.tr('settings.theme_options.auto'),
                        style: TextStyle(
                            color:
                                ColorResource.textDefault(context.isDarkMode)),
                      ),
                      Radio(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: const VisualDensity(
                          horizontal: VisualDensity.minimumDensity,
                          vertical: VisualDensity.minimumDensity,
                        ),
                        activeColor:
                            ColorResource.textDefault(context.isDarkMode),
                        value: AdaptiveThemeMode.system,
                        groupValue: selectedTheme,
                        onChanged: (value) {
                          if (value != null) {
                            updateTheme(AdaptiveThemeMode.system);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: InkWell(
                  onTap: () {
                    updateTheme(AdaptiveThemeMode.light);
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      buildPreview(
                        themeMode: AdaptiveThemeMode.light,
                        preview: Container(color: Colors.white),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        context.tr('settings.theme_options.light'),
                        style: TextStyle(
                            color:
                                ColorResource.textDefault(context.isDarkMode)),
                      ),
                      Radio(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: const VisualDensity(
                          horizontal: VisualDensity.minimumDensity,
                          vertical: VisualDensity.minimumDensity,
                        ),
                        activeColor:
                            ColorResource.textDefault(context.isDarkMode),
                        value: AdaptiveThemeMode.light,
                        groupValue: selectedTheme,
                        onChanged: (value) {
                          if (value != null) {
                            updateTheme(AdaptiveThemeMode.light);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: InkWell(
                  onTap: () {
                    updateTheme(AdaptiveThemeMode.dark);
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      buildPreview(
                        themeMode: AdaptiveThemeMode.dark,
                        preview: Container(color: Colors.black),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        context.tr('settings.theme_options.dark'),
                        style: TextStyle(
                            color:
                                ColorResource.textDefault(context.isDarkMode)),
                      ),
                      Radio(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: const VisualDensity(
                          horizontal: VisualDensity.minimumDensity,
                          vertical: VisualDensity.minimumDensity,
                        ),
                        activeColor:
                            ColorResource.textDefault(context.isDarkMode),
                        value: AdaptiveThemeMode.dark,
                        groupValue: selectedTheme,
                        onChanged: (value) {
                          if (value != null) {
                            updateTheme(AdaptiveThemeMode.dark);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildPreview(
      {required AdaptiveThemeMode themeMode, required Widget preview}) {
    return Opacity(
      opacity: (selectedTheme == themeMode) ? 1 : .25,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: (selectedTheme == themeMode) ? Colors.black : Colors.grey,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: AspectRatio(
          aspectRatio: 9 / 16,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(14),
            child: preview,
          ),
        ),
      ),
    );
  }

  AppBar buildAppBar() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Text(
        context.tr('settings.theme'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }
}
