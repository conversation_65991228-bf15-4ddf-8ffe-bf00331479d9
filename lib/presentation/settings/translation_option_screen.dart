import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/translation/translation_list.dart';

class TranslationOptionScreen extends StatefulWidget {
  const TranslationOptionScreen({super.key});

  @override
  State<TranslationOptionScreen> createState() =>
      _TranslationOptionScreenState();
}

class _TranslationOptionScreenState extends State<TranslationOptionScreen> {
  bool isLoading = true;

  @override
  void initState() {
    FirebaseAnalytics.instance
        .logScreenView(screenName: "translation_settings");

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        isLoading = false;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(),
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
        child: isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : const SingleChildScrollView(
                child: TranslationList(applyMushafTheme: false),
              ),
      ),
    );
  }

  AppBar buildAppBar() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Text(
        context.tr('translation.default_translation'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }
}
