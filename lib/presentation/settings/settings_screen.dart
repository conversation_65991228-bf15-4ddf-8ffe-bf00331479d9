import 'dart:io';
import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/ai/asr_model_type.dart';
import 'package:mushafi/presentation/model/ai/cond_model_type.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/settings/ai_model_screen.dart';
import 'package:mushafi/presentation/settings/app_credits_screen.dart';
import 'package:mushafi/presentation/settings/juz_name_script_option_screen.dart';
import 'package:mushafi/presentation/settings/language_screen.dart';
import 'package:mushafi/presentation/settings/reciter_option_screen.dart';
import 'package:mushafi/presentation/settings/storage_screen.dart';
import 'package:mushafi/presentation/settings/theme_option_screen.dart';
import 'package:mushafi/presentation/settings/translation_option_screen.dart';
import 'package:mushafi/presentation/splash/splash_screen.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/app_update_helper.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/constants.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:mushafi/widget/home/<USER>';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool appUpdateAvailable = false;
  bool modelUpdateAvailable = false;
  String appVersion = '';

  void initInferencePipeline() async {
    final modelVersion = PreferenceStorage.getLatestTestedModelVersion();
    if (modelVersion == null) return;

    final model = InferenceModel.fromType(
      AsrModelType.quantized,
      CondModelType.quantized,
      PreferenceStorage.getSelectedModel(),
      modelVersion,
    );

    if (mounted) {
      final modelsExist =
          await ref.read(minioDataRepositoryProvider).doModelsExist(model);
      if (modelsExist) {
        final inferencePipeline = InferencePipeline.getInstance();
        final success = await inferencePipeline.initialize(model);
        if (!success) {
          CommonUtils.showToast('Failed to initialize inference pipeline');
        }
      }
    }
  }

  Future<void> checkAppUpdate() async {
    AppUpdateHelper.isUpdateAvailable().then((available) {
      setState(() {
        appUpdateAvailable = available;
      });
    });
  }

  Future<void> checkModelUpdate() async {
    final latestModelVersion = await ref
        .read(minioDataRepositoryProvider)
        .updateAndGetLatestModelVersion();
    final currentModelVersion = PreferenceStorage.getLatestTestedModelVersion();
    if (mounted) {
      setState(() {
        modelUpdateAvailable = (latestModelVersion != null)
            ? latestModelVersion != currentModelVersion
            : false;
      });
    }
  }

  @override
  void initState() {
    FirebaseAnalytics.instance.logScreenView(screenName: "settings");

    checkAppUpdate();
    checkModelUpdate();

    PackageInfo.fromPlatform().then((info) {
      setState(() {
        appVersion = info.version;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
        appBar: buildAppBar(context),
        body: Column(
          children: [
            buildTabBar(),
            Expanded(
              child: TabBarView(
                children: [
                  buildBasicSettings(),
                  buildAppearanceSettings(),
                  buildAboutSection(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  TabBar buildTabBar() {
    return TabBar(
      indicatorColor: ColorResource.textDefault(context.isDarkMode),
      labelColor: ColorResource.textDefault(context.isDarkMode),
      unselectedLabelColor:
          ColorResource.textDefault(context.isDarkMode).withOpacity(.5),
      tabAlignment: TabAlignment.start,
      isScrollable: true,
      tabs: [
        Tab(text: context.tr('settings.basic')),
        Tab(text: context.tr('settings.appearance')),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Tab(text: context.tr('settings.about')),
            if (appUpdateAvailable || modelUpdateAvailable) ...[
              const SizedBox(width: 8),
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  AppBar buildAppBar(BuildContext context) {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      leading: IconButton(
        onPressed: () {
          Navigator.pop(context);
        },
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
      ),
      title: Text(context.tr('settings.title'),
          style: TextStyle(
            fontSize: 16,
            color: ColorResource.textDefault(context.isDarkMode),
          )),
      centerTitle: true,
    );
  }

  Widget buildBasicSettings() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            color: ColorResource.backgroundWhite(context.isDarkMode),
            border: Border.all(
                color: ColorResource.border(context.isDarkMode), width: 1.0),
          ),
          child: Column(
            children: [
              buildSettingsItem(
                Assets.svgsIcGlobe,
                context.tr('settings.language'),
                () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const LanguageScreen(),
                    ),
                  );
                },
              ),
              const Divider(thickness: .2, height: 1),
              buildSettingsItem(
                Assets.svgsIcBookReader,
                context.tr('settings.default_reciter'),
                () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ReciterOptionScreen(),
                    ),
                  );

                  ref
                      .read(audioSegmentDataInitProvider.notifier)
                      .fetchAudioSegmentData();
                },
              ),
              const Divider(thickness: .2, height: 1),
              buildSettingsItem(
                Assets.svgsIcTranslation,
                context.tr('settings.default_translation'),
                () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TranslationOptionScreen(),
                    ),
                  );

                  ref
                      .read(translationTafsirDataInitProvider.notifier)
                      .fetchTranslationTafsirData();
                },
              ),
              const Divider(thickness: .2, height: 1),
              buildSettingsItem(
                Assets.svgsIcFolderOpen,
                context.tr('settings.storage'),
                () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const StorageScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildAIModelSettings() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            color: ColorResource.backgroundWhite(context.isDarkMode),
            border: Border.all(
                color: ColorResource.border(context.isDarkMode), width: 1.0),
          ),
          child: Column(
            children: [
              buildSettingsItem(
                Assets.svgsIcBrain,
                context.tr('settings.ai.selected_model'),
                () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const AiModelScreen(),
                    ),
                  );

                  final oldInferencePipeline = InferencePipeline.getInstance();
                  oldInferencePipeline.delete();

                  final micPermissionStatus =
                      await Permission.microphone.status;
                  if (micPermissionStatus.isGranted) {
                    initInferencePipeline();
                  }
                },
              ),
              // buildSettingsItem(
              //   Assets.svgsIcClipboard,
              //   'Last Tested',
              //   () async {
              //     await Navigator.of(context).push(
              //       MaterialPageRoute(
              //         builder: (context) => const LanguageScreen(),
              //       ),
              //     );
              //   },
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildAppearanceSettings() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            color: ColorResource.backgroundWhite(context.isDarkMode),
            border: Border.all(
                color: ColorResource.border(context.isDarkMode), width: 1.0),
          ),
          child: Column(
            children: [
              buildSettingsItem(
                Assets.svgsIcSun,
                context.tr('settings.theme'),
                () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ThemeOptionScreen(),
                    ),
                  );

                  if (mounted) {
                    ref
                        .read(quranImageAssetProvider.notifier)
                        .updateMushafDesign(
                            MushafDesign.getDefault(context.isDarkMode));
                  }

                  ref.invalidate(trackListProvider);
                },
              ),
              const Divider(thickness: .2, height: 1),
              buildSettingsItem(
                Assets.svgsIcBookReader,
                context.tr('settings.juz_name_script'),
                () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const JuzNameScriptOptionScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildAboutContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.0),
              color: ColorResource.backgroundWhite(context.isDarkMode),
              border: Border.all(
                  color: ColorResource.border(context.isDarkMode), width: 1.0),
            ),
            child: Column(
              children: [
                buildSettingsItem(
                  Assets.svgsIcWeb,
                  'mushafi.app',
                  () {
                    final url = (context.isArabic)
                        ? mushafiArabicWebUrl
                        : mushafiWebUrl;
                    launchUrl(Uri.parse(url));
                  },
                ),
                const Divider(thickness: .2, height: 1),
                buildSettingsItem(
                  Assets.svgsIcCredit,
                  context.tr('about.app_credits'),
                  () async {
                    await Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AppCreditsScreen(),
                      ),
                    );
                  },
                ),
                const Divider(thickness: .2, height: 1),
                buildSettingsItem(
                  Assets.svgsIcForward,
                  context.tr('drawer.share'),
                  () {
                    final message = context.tr(
                      'settings.share.message',
                      args: [mushafiPlayStoreUrl, mushafiAppStoreUrl],
                    );
                    Share.share(message);
                  },
                ),
                const Divider(thickness: .2, height: 1),
                buildSettingsItem(
                  Assets.svgsIcStar,
                  context.tr('drawer.review'),
                  () {
                    openStoreUrl();
                  },
                ),
              ],
            ),
          ),
        ),
        if (appUpdateAvailable)
          buildAppUpdateAvailableCard()
        else if (modelUpdateAvailable)
          buildModelUpdateAvailableCard(),
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32.0),
          child: Text(
            context.tr('drawer.follow_us'),
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              buildSocialMediaButton(
                context,
                Assets.svgsIcInstagram,
                mushafiInstagramUrl,
              ),
              buildSocialMediaButton(
                context,
                Assets.svgsIcFacebook,
                mushafiFacebookUrl,
              ),
              buildSocialMediaButton(
                context,
                Assets.svgsIcYoutube,
                mushafiYoutubeUrl,
              ),
              buildSocialMediaButton(
                context,
                Assets.svgsIcTiktok,
                mushafiTiktokUrl,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildAboutSection() {
    final modelVersion = PreferenceStorage.getLatestTestedModelVersion();

    return OrientationBuilder(builder: (context, orientation) {
      return switch (orientation) {
        Orientation.portrait => Column(
            children: [
              buildAboutContent(),
              const Spacer(),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 32.0, vertical: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    if (modelVersion != null) ...[
                      Text(
                        context.tr(
                          'settings.model.version',
                          args: [modelVersion],
                        ),
                        style: TextStyle(
                          fontSize: 12,
                          color: ColorResource.textGrey(context.isDarkMode),
                        ),
                      ),
                    ],
                    if (appVersion.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        context.tr('settings.app_version', args: [appVersion]),
                        style: TextStyle(
                          fontSize: 12,
                          color: ColorResource.textGrey(context.isDarkMode),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        Orientation.landscape => SingleChildScrollView(
            child: Column(
              children: [
                buildAboutContent(),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 32.0, vertical: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      if (modelVersion != null) ...[
                        Text(
                          context.tr(
                            'settings.model.version',
                            args: [modelVersion],
                          ),
                          style: TextStyle(
                            fontSize: 12,
                            color: ColorResource.textGrey(context.isDarkMode),
                          ),
                        ),
                      ],
                      if (appVersion.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          context
                              .tr('settings.app_version', args: [appVersion]),
                          style: TextStyle(
                            fontSize: 12,
                            color: ColorResource.textGrey(context.isDarkMode),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
      };
    });
  }

  Widget buildSocialMediaButton(
      BuildContext context, String asset, String url) {
    return IconButton(
      icon: SvgPicture.asset(
        asset,
        width: 16,
        height: 16,
      ),
      onPressed: () async {
        await launchUrl(Uri.parse(url));
      },
    );
  }

  Widget buildSettingsItem(
      String iconAsset, String name, VoidCallback onClick) {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return ListTile(
      onTap: onClick,
      leading: SvgPicture.asset(
        iconAsset,
        colorFilter: ColorFilter.mode(
          ColorResource.textDefault(context.isDarkMode),
          BlendMode.srcIn,
        ),
      ),
      title: Text(
        name,
        style: TextStyle(
          fontSize: 14,
          color: ColorResource.textDefault(context.isDarkMode),
        ),
      ),
      trailing: SvgPicture.asset(
        (isRtl) ? Assets.svgsIcChevronLeft24px : Assets.svgsIcChevronRight24px,
        width: 16,
        height: 16,
      ),
    );
  }

  Widget buildAppUpdateAvailableCard() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: ColorResource.backgroundWhite(context.isDarkMode),
          border: Border.all(
              color: ColorResource.border(context.isDarkMode), width: 1.0),
        ),
        child: ListTile(
          onTap: () {
            openStoreUrl();
          },
          dense: true,
          leading: Lottie.asset(
            Assets.jsonRedDotBadgeAnimation,
            width: 24,
            height: 24,
          ),
          title: Text(
            context.tr('settings.app_update_available'),
            style: TextStyle(
              fontSize: 14,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          trailing: SvgPicture.asset(
            (isRtl)
                ? Assets.svgsIcChevronLeft24px
                : Assets.svgsIcChevronRight24px,
            width: 16,
            height: 16,
          ),
        ),
      ),
    );
  }

  Widget buildModelUpdateAvailableCard() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: ColorResource.backgroundWhite(context.isDarkMode),
          border: Border.all(
              color: ColorResource.border(context.isDarkMode), width: 1.0),
        ),
        child: ListTile(
          onTap: () {
            showModelUpdateAvailableDialog();
          },
          dense: true,
          leading: Lottie.asset(
            Assets.jsonRedDotBadgeAnimation,
            width: 24,
            height: 24,
          ),
          title: Text(
            context.tr('settings.model.update_available'),
            style: TextStyle(
              fontSize: 14,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          trailing: SvgPicture.asset(
            (isRtl)
                ? Assets.svgsIcChevronLeft24px
                : Assets.svgsIcChevronRight24px,
            width: 16,
            height: 16,
          ),
        ),
      ),
    );
  }

  void openStoreUrl() {
    final url = (Platform.isIOS || Platform.isMacOS)
        ? mushafiAppStoreUrl
        : mushafiPlayStoreUrl;
    launchUrl(Uri.parse(url));
  }

  Future<void> showModelUpdateAvailableDialog() async {
    if (Platform.isIOS || Platform.isMacOS) {
      return showCupertinoDialog(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(
              context.tr('settings.model.new_available'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            content: Text(
              context.tr('settings.model.update_message'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            actions: <Widget>[
              TextButton(
                child: Text(
                  context.tr('storage.cancel'),
                  style: TextStyle(
                      color: ColorResource.textBlue(context.isDarkMode)),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              TextButton(
                child: Text(
                  context.tr('settings.model.run_test'),
                  style: TextStyle(
                    color: ColorResource.textBlue(context.isDarkMode),
                    fontWeight: ui.FontWeight.bold,
                  ),
                ),
                onPressed: () {
                  PreferenceStorage.saveLatestTestedModelVersion(null);
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(
                        builder: (context) => const SplashScreen()),
                    ModalRoute.withName('/'),
                  );
                },
              ),
            ],
          );
        },
      );
    }

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            context.tr('settings.model.new_available'),
            style:
                TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
          ),
          content: Text(
            context.tr('settings.model.update_message'),
            style:
                TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: ColorResource.textGrey(context.isDarkMode),
              ),
              child: Text(
                context.tr('storage.cancel'),
                style: TextStyle(
                    color: ColorResource.textDefault(context.isDarkMode)),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                context.tr('settings.model.run_test'),
                style: TextStyle(
                    color: ColorResource.textBlue(context.isDarkMode)),
              ),
              onPressed: () {
                PreferenceStorage.saveLatestTestedModelVersion(null);
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const SplashScreen()),
                  ModalRoute.withName('/'),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
