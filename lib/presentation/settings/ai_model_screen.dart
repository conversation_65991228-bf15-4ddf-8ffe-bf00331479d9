import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/ai/vad_model_type.dart';
import 'package:mushafi/resource/color_resource.dart';

class AiModelScreen extends StatefulWidget {
  const AiModelScreen({super.key});

  @override
  State<AiModelScreen> createState() => _AiModelScreenState();
}

class _AiModelScreenState extends State<AiModelScreen> {
  final modelList = VadModelType.values;

  VadModelType selectedModel = PreferenceStorage.getSelectedModel();

  @override
  void initState() {
    FirebaseAnalytics.instance
        .logScreenView(screenName: "selected_model_settings");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(context),
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: buildAIModelList(),
    );
  }

  AppBar buildAppBar(BuildContext context) {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Text(
        context.tr('settings.ai.title'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }

  Widget buildAIModelList() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: ColorResource.backgroundWhite(context.isDarkMode),
          border: Border.all(
              color: ColorResource.border(context.isDarkMode), width: 1.0),
        ),
        child: ListView.separated(
          shrinkWrap: true,
          padding: const EdgeInsets.all(0),
          itemCount: modelList.length,
          itemBuilder: (context, index) {
            final model = modelList[index];
            final additionalText = switch (model.readableName) {
              'Q1u' => '(${context.tr('settings.ai.accuracy')})',
              'Q1t' => '(${context.tr('settings.ai.balanced')})',
              'Q1s' => '(${context.tr('settings.ai.fastest')})',
              _ => '',
            };
            return ListTile(
              onTap: () {
                PreferenceStorage.saveSelectedModel(model);
                setState(() {
                  selectedModel = model;
                });
              },
              title: Text.rich(
                TextSpan(
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                  children: [
                    TextSpan(text: model.readableName),
                    const TextSpan(text: ' '),
                    TextSpan(text: additionalText),
                  ],
                ),
              ),
              trailing: model.name == selectedModel.name
                  ? const Icon(
                      Icons.check,
                      size: 24,
                    )
                  : null,
            );
          },
          separatorBuilder: (context, index) {
            return const Divider(thickness: .2, height: 1);
          },
        ),
      ),
    );
  }
}
