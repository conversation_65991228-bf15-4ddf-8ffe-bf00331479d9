import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/quran/juz_name_script.dart';
import 'package:mushafi/resource/color_resource.dart';

class JuzNameScriptOptionScreen extends StatefulWidget {
  const JuzNameScriptOptionScreen({super.key});

  @override
  State<JuzNameScriptOptionScreen> createState() =>
      _JuzNameScriptOptionScreenState();
}

class _JuzNameScriptOptionScreenState extends State<JuzNameScriptOptionScreen> {
  JuzNameScript? groupValue;

  void onSelected(JuzNameScript script) {
    if (groupValue != script) {
      PreferenceStorage.saveJuzNameScript(script);

      setState(() {
        groupValue = script;
      });
    }
  }

  @override
  void initState() {
    FirebaseAnalytics.instance
        .logScreenView(screenName: "juz_name_script_settings");

    groupValue = PreferenceStorage.getJuzNameScript();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(),
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: buildScriptOptions(),
    );
  }

  Widget buildScriptOptions() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: ColorResource.backgroundWhite(context.isDarkMode),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    onSelected(JuzNameScript.latin);
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Opacity(
                        opacity: (groupValue == JuzNameScript.latin) ? 1 : .25,
                        child:
                            SvgPicture.asset(Assets.svgsImgLatinScriptPreview),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        context.tr('settings.latin_script'),
                        style: TextStyle(
                            color:
                                ColorResource.textDefault(context.isDarkMode)),
                      ),
                      Radio(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: const VisualDensity(
                          horizontal: VisualDensity.minimumDensity,
                          vertical: VisualDensity.minimumDensity,
                        ),
                        activeColor:
                            ColorResource.textDefault(context.isDarkMode),
                        value: JuzNameScript.latin,
                        groupValue: groupValue,
                        onChanged: (value) {
                          if (value != null) {
                            onSelected(value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  onTap: () {
                    onSelected(JuzNameScript.arabic);
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Opacity(
                        opacity: (groupValue == JuzNameScript.arabic) ? 1 : .25,
                        child:
                            SvgPicture.asset(Assets.svgsImgArabicScriptPreview),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        context.tr('settings.arabic_script'),
                        style: TextStyle(
                            color:
                                ColorResource.textDefault(context.isDarkMode)),
                      ),
                      Radio(
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: const VisualDensity(
                          horizontal: VisualDensity.minimumDensity,
                          vertical: VisualDensity.minimumDensity,
                        ),
                        activeColor:
                            ColorResource.textDefault(context.isDarkMode),
                        value: JuzNameScript.arabic,
                        groupValue: groupValue,
                        onChanged: (value) {
                          if (value != null) {
                            onSelected(value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  AppBar buildAppBar() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Text(
        context.tr('settings.juz_name_script'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }
}
