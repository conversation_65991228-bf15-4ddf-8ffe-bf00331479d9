import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:language_code/language_code.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/resource/color_resource.dart';

class LanguageScreen extends StatefulWidget {
  const LanguageScreen({super.key});

  @override
  State<LanguageScreen> createState() => _LanguageScreenState();
}

class _LanguageScreenState extends State<LanguageScreen> {
  @override
  void initState() {
    FirebaseAnalytics.instance.logScreenView(screenName: "language_settings");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(),
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
        child: buildLanguageList(),
      ),
    );
  }

  AppBar buildAppBar() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Text(
        context.tr('settings.language'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }

  Widget buildLanguageList() {
    return Directionality(
      textDirection: ui.TextDirection.ltr,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.0),
              color: ColorResource.backgroundWhite(context.isDarkMode),
              border: Border.all(
                  color: ColorResource.border(context.isDarkMode), width: 1.0),
            ),
            child: ListView.separated(
              shrinkWrap: true,
              padding: const EdgeInsets.all(0),
              itemCount: supportedLocales.length,
              itemBuilder: (context, index) {
                final locale = supportedLocales[index];
                final code = LanguageCodes.fromLocale(locale);
                return ListTile(
                  onTap: () {
                    context.setLocale(locale);
                  },
                  title: Text(
                    code.nativeName,
                    style: TextStyle(
                      fontSize: 14,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  trailing: locale == context.locale
                      ? const Icon(
                          Icons.check,
                          size: 24,
                        )
                      : null,
                );
              },
              separatorBuilder: (context, index) {
                return const Divider(thickness: .2, height: 1);
              },
            ),
          ),
        ],
      ),
    );
  }
}
