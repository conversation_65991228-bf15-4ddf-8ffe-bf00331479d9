import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/reciter/reciter_list.dart';

class ReciterOptionsBottomSheet extends ConsumerStatefulWidget {
  final AnimationController animationController;

  const ReciterOptionsBottomSheet(this.animationController, {super.key});

  @override
  ConsumerState<ReciterOptionsBottomSheet> createState() =>
      _ReciterOptionsBottomSheetState();
}

class _ReciterOptionsBottomSheetState
    extends ConsumerState<ReciterOptionsBottomSheet> {
  bool isLoading = true;

  @override
  void initState() {
    widget.animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              isLoading = false;
            });
          });
        }
      }
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildHeader(),
          const SizedBox(height: 16),
          isLoading
              ? const Center(child: CircularProgressIndicator())
              : const Expanded(
                  child: SingleChildScrollView(
                    child: ReciterList(),
                  ),
                ),
        ],
      ),
    );
  }

  Widget buildHeader() {
    return Row(
      children: [
        InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child:
              SvgPicture.asset(Assets.svgsIcCloseCircle, width: 20, height: 20),
        ),
        const Spacer(),
        Text(
          context.tr('reciter.select_reciter'),
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: ColorResource.textDefault(context.isDarkMode),
          ),
        ),
        const Spacer(),
        InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: Text(
            context.tr('reciter.done'),
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: ColorResource.textGrey(context.isDarkMode),
            ),
          ),
        ),
      ],
    );
  }
}
