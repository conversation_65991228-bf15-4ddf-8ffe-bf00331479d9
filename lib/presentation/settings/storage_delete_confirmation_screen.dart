import 'dart:io';
import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/storage/model_item_storage.dart';
import 'package:mushafi/presentation/model/translation/translation.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/file_utils.dart';
import 'package:mushafi/utils/inference_pipeline.dart';

class StorageDeleteConfirmationScreen extends ConsumerWidget {
  final Map<Translation, File> selectedTranslationToFileMap;
  final Map<ModelItemStorage, File> selectedModelToFileMap;

  const StorageDeleteConfirmationScreen({
    super.key,
    required this.selectedTranslationToFileMap,
    required this.selectedModelToFileMap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: buildAppBar(context),
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildConfirmationText(context),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 8.0),
              child: Divider(thickness: .2),
            ),
            buildTranslationList(),
            buildModelList(),
            const Spacer(),
            buildDeleteButton(context),
          ],
        ),
      ),
    );
  }

  AppBar buildAppBar(BuildContext context) {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context, false);
        },
      ),
      title: Text(
        context.tr('storage.delete_file'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }

  Widget buildConfirmationText(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('storage.delete_confirm_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ColorResource.textDefault(context.isDarkMode),
          ),
        ),
        Text(
          context.tr('storage.delete_confirm_message'),
          style: TextStyle(
            fontSize: 12,
            color: ColorResource.textGrey(context.isDarkMode),
          ),
        ),
      ],
    );
  }

  Widget buildTranslationList() {
    return ListView.separated(
      shrinkWrap: true,
      itemCount: selectedTranslationToFileMap.length,
      itemBuilder: (context, index) {
        final translationEntry =
            selectedTranslationToFileMap.entries.toList()[index];
        final translation = translationEntry.key;
        final name = (translation.nativeTranslatedName.isNotEmpty)
            ? translation.nativeTranslatedName
            : translation.name;
        final fileSize = FileUtils.getFileSize(translationEntry.value);
        return buildItem(
          context,
          name,
          translationEntry.value,
          FileType.translation,
          fileSize,
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 8);
      },
    );
  }

  Widget buildModelList() {
    return ListView.separated(
      shrinkWrap: true,
      itemCount: selectedModelToFileMap.length,
      itemBuilder: (context, index) {
        final modelEntry = selectedModelToFileMap.entries.toList()[index];
        return buildItem(
          context,
          modelEntry.key.readableName,
          modelEntry.value,
          FileType.model,
          modelEntry.key.size,
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 8);
      },
    );
  }

  Widget buildItem(BuildContext context, String name, File file, FileType type,
      int fileSize) {
    final readableFileSize =
        CommonUtils.humanReadableKBCountBin(fileSize / 1024);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            name,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          const SizedBox(width: 4),
          Container(
            width: 2,
            height: 2,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: ColorResource.textGrey(context.isDarkMode),
            ),
          ),
          const SizedBox(width: 4),
          Text(
            switch (type) {
              FileType.translation => context.tr('storage.translation'),
              FileType.model => context.tr('storage.ai_model'),
            },
            style: TextStyle(
              fontSize: 10,
              color: ColorResource.textGrey(context.isDarkMode),
            ),
          ),
          const Spacer(),
          Directionality(
            textDirection: ui.TextDirection.ltr,
            child: Text(
              readableFileSize,
              style: TextStyle(
                fontSize: 10,
                color: ColorResource.textGrey(context.isDarkMode),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildDeleteButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: FilledButton(
          onPressed: () {
            for (final entry in selectedTranslationToFileMap.entries) {
              entry.value.deleteSync(recursive: true);
            }
            for (final entry in selectedModelToFileMap.entries) {
              entry.value.deleteSync(recursive: true);
            }

            if (selectedModelToFileMap.isNotEmpty) {
              InferencePipeline.getInstance().delete();
            }

            Navigator.pop(context, true);
          },
          style: FilledButton.styleFrom(
            backgroundColor: ColorResource.primary(context.isDarkMode),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
          ),
          child: Text(
            context.tr('common.delete'),
            style: TextStyle(
              color: ColorResource.onPrimary(context.isDarkMode),
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }
}

enum FileType {
  translation,
  model,
}
