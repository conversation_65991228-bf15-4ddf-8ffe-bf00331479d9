import 'dart:io';
import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/minio_data_manager.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/ai/vad_model_type.dart';
import 'package:mushafi/presentation/model/recitation/reciter.dart';
import 'package:mushafi/presentation/model/storage/model_item_storage.dart';
import 'package:mushafi/presentation/model/translation/translation.dart';
import 'package:mushafi/presentation/settings/storage_delete_confirmation_screen.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/file_utils.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:path_provider/path_provider.dart';
import 'package:disk_space_2/disk_space_2.dart';

class StorageScreen extends ConsumerStatefulWidget {
  const StorageScreen({super.key});

  @override
  ConsumerState<StorageScreen> createState() => _StorageScreenState();
}

class _StorageScreenState extends ConsumerState<StorageScreen> {
  final deleteButtonKey = GlobalKey();

  final otherColor = const Color(0xFF96C8A6);
  final appColor = const Color(0xFFF08658);
  final freeColor = const Color(0xFF666666);

  double deleteButtonHeight = 0;

  String defaultTranslationKey = '';
  VadModelType defaultModel = PreferenceStorage.getSelectedModel();

  List<Reciter> downloadedReciterList = [];
  Map<Translation, File> translationToFileMap = {};
  Map<ModelItemStorage, File> modelToFileMap = {};

  int otherSpaceBytes = 0;
  int appSpaceBytes = 0;
  int freeSpaceBytes = 0;

  double otherValue = 0;
  double appValue = 0;

  Map<Translation, File> selectedTranslationToFileMap = {};
  Map<ModelItemStorage, File> selectedModelToFileMap = {};

  bool get isSelected =>
      selectedTranslationToFileMap.isNotEmpty ||
      selectedModelToFileMap.isNotEmpty;

  int get selectedLength =>
      selectedTranslationToFileMap.length + selectedModelToFileMap.length;

  void showDeleteSuccessToast() {
    CommonUtils.showToast(context.tr('storage.delete_success'));
  }

  void deleteAll() async {
    final dataDirectory = await getApplicationDocumentsDirectory();

    final translationsTafsirsDirectory = Directory(
        '${dataDirectory.path}/${MinioDataManager.translationsTafsirsPath}');
    final modelsDirectory =
        Directory('${dataDirectory.path}/${MinioDataManager.modelsPath}');
    final corpusDirectory =
        Directory('${dataDirectory.path}/${MinioDataManager.quranCorpusPath}');
    final audioSegmentsDirectory = Directory(
        '${dataDirectory.path}/${MinioDataManager.audioSegmentsPath}');

    if (await translationsTafsirsDirectory.exists()) {
      await translationsTafsirsDirectory.delete(recursive: true);
    }

    if (await modelsDirectory.exists()) {
      await modelsDirectory.delete(recursive: true);
    }

    if (await corpusDirectory.exists()) {
      await corpusDirectory.delete(recursive: true);
    }

    if (await audioSegmentsDirectory.exists()) {
      await audioSegmentsDirectory.delete(recursive: true);
    }

    InferencePipeline.getInstance().delete();

    showDeleteSuccessToast();

    setState(() {
      selectedTranslationToFileMap = {};
      selectedModelToFileMap = {};
    });

    fetchSpace();
  }

  @override
  void initState() {
    FirebaseAnalytics.instance.logScreenView(screenName: "storage_settings");

    fetchSpace();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        final deleteButtonBox =
            deleteButtonKey.currentContext?.findRenderObject() as RenderBox;
        final totalVerticalPadding = 32;
        deleteButtonHeight = deleteButtonBox.size.height + totalVerticalPadding;
      });
    });

    super.initState();
  }

  Future<void> fetchSpace() async {
    final minioRepository = ref.read(minioDataRepositoryProvider);
    final allReciterList = await minioRepository.getReciterList();
    final cachedReciterFileList =
        await minioRepository.getCachedAudioSegmentNameList();

    final allTranslationList = await minioRepository.getTranslationList();
    final cachedTranslationFileList =
        await minioRepository.getCachedTranslationTafsirNameList();
    final filteredTranslationList = allTranslationList
        .where((translation) =>
            cachedTranslationFileList.contains(translation.fileName))
        .toList();

    // disk_space_2 returns values in MB, convert to bytes for consistency with totalSize
    final double totalDiskSpaceMB = (await DiskSpace.getTotalDiskSpace) ?? 0.0;
    final double freeDiskSpaceMB = (await DiskSpace.getFreeDiskSpace) ?? 0.0;
    // Convert MB to bytes (1 MB = 1024 * 1024 bytes)
    final int totalDiskSpace = (totalDiskSpaceMB * 1024 * 1024).toInt();
    final int freeDiskSpace = (freeDiskSpaceMB * 1024 * 1024).toInt();
    final int usedDiskSpace = totalDiskSpace - freeDiskSpace;

    final dataDirectory = await getApplicationDocumentsDirectory();
    final audioSegmentsDirectory = Directory(
        '${dataDirectory.path}/${MinioDataManager.audioSegmentsPath}');
    final translationsTafsirsDirectory = Directory(
        '${dataDirectory.path}/${MinioDataManager.translationsTafsirsPath}');
    final modelsDirectory =
        Directory('${dataDirectory.path}/${MinioDataManager.modelsPath}');

    final modelsExist = await modelsDirectory.exists();

    final audioSegmentsSize =
        await FileUtils.getDirSize(audioSegmentsDirectory);
    final translationsTafsirsSize =
        await FileUtils.getDirSize(translationsTafsirsDirectory);
    final modelsSize = await FileUtils.getDirSize(modelsDirectory);
    final totalSize = translationsTafsirsSize + modelsSize;

    defaultTranslationKey = PreferenceStorage.getDefaultTranslationKey();

    if (mounted) {
      setState(() {
        downloadedReciterList = allReciterList.where((reciter) {
          return cachedReciterFileList.contains(reciter.audioSegmentFileName);
        }).toList();

        translationToFileMap = {};
        for (final translation in filteredTranslationList) {
          translationToFileMap[translation] = File(
            '${dataDirectory.path}/${MinioDataManager.translationsTafsirsPath}/${translation.fileName}',
          );
        }

        modelToFileMap = {};
        if (modelsExist) {
          final vadModel = VadModelType.stride4Q;
          final model = ModelItemStorage(
            vadModel.name,
            vadModel.readableName,
            modelsSize,
            false,
          );
          modelToFileMap[model] =
              File('${dataDirectory.path}/${MinioDataManager.modelsPath}');
        }

        otherSpaceBytes = usedDiskSpace - totalSize;
        appSpaceBytes = totalSize;
        freeSpaceBytes = freeDiskSpace;

        otherValue = otherSpaceBytes / totalDiskSpace;
        appValue = (otherSpaceBytes + appSpaceBytes) / totalDiskSpace;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(),
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: Column(
        children: [
          buildSummary(),
          const SizedBox(height: 16),
          Expanded(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                buildDownloadedList(),
                if (isSelected) buildDeleteSelectedButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildDeleteSelectedButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 32),
      child: SizedBox(
        width: double.infinity,
        child: FilledButton(
          onPressed: () async {
            final result = await Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => StorageDeleteConfirmationScreen(
                  selectedTranslationToFileMap: selectedTranslationToFileMap,
                  selectedModelToFileMap: selectedModelToFileMap,
                ),
              ),
            );

            if (result) {
              showDeleteSuccessToast();

              setState(() {
                selectedTranslationToFileMap = {};
                selectedModelToFileMap = {};
              });

              fetchSpace();
            }
          },
          style: FilledButton.styleFrom(
            backgroundColor: ColorResource.primary(context.isDarkMode),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
          ),
          child: Text(
            (selectedLength > 1)
                ? context.tr('storage.delete_files',
                    args: [selectedLength.toString()])
                : context.tr('storage.delete_file_single',
                    args: [selectedLength.toString()]),
            style: TextStyle(
              color: ColorResource.onPrimary(context.isDarkMode),
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  AppBar buildAppBar() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Text(
        context.tr('storage.title'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }

  Widget buildSummary() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: ColorResource.backgroundWhite(context.isDarkMode),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Stack(
                children: [
                  LinearProgressIndicator(
                    minHeight: 8,
                    borderRadius: const BorderRadius.all(Radius.circular(16)),
                    backgroundColor: freeColor,
                    color: freeColor,
                    value: 0,
                  ),
                  LinearProgressIndicator(
                    minHeight: 8,
                    borderRadius: const BorderRadius.all(Radius.circular(16)),
                    backgroundColor: Colors.transparent,
                    color: appColor,
                    value: appValue,
                  ),
                  LinearProgressIndicator(
                    minHeight: 8,
                    borderRadius: const BorderRadius.all(Radius.circular(16)),
                    backgroundColor: Colors.transparent,
                    color: otherColor,
                    value: otherValue,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              buildIndicatorInfo(context.tr('storage.other_apps'),
                  otherSpaceBytes, otherColor),
              const SizedBox(height: 4),
              buildIndicatorInfo(
                  context.tr('storage.downloads'), appSpaceBytes, appColor),
              const SizedBox(height: 4),
              buildIndicatorInfo(
                  context.tr('storage.free'), freeSpaceBytes, freeColor),
              const SizedBox(height: 16),
              Text(
                context.tr('storage.remove_all_warning'),
                textAlign: TextAlign.start,
                style: TextStyle(
                    color: ColorResource.textGrey(context.isDarkMode),
                    fontSize: 10),
              ),
              const SizedBox(height: 8),
              OutlinedButton(
                key: deleteButtonKey,
                onPressed: () {
                  showDeleteAllConfirmDialog();
                },
                style: OutlinedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                  ),
                  side: BorderSide(
                    width: 1.5,
                    color: ColorResource.border(context.isDarkMode),
                  ),
                ),
                child: Text(
                  context.tr('storage.remove_all_downloads'),
                  style: TextStyle(
                    color: ColorResource.textGrey(context.isDarkMode),
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> showDeleteAllConfirmDialog() async {
    if (Platform.isIOS || Platform.isMacOS) {
      return showCupertinoDialog(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(
              context.tr('storage.remove_all_confirm_title'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            content: Text(
              context.tr('storage.remove_all_confirm_message'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            actions: <Widget>[
              TextButton(
                child: Text(
                  context.tr('storage.cancel'),
                  style: TextStyle(
                      color: ColorResource.textBlue(context.isDarkMode)),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              TextButton(
                child: Text(
                  context.tr('storage.continue'),
                  style: TextStyle(
                    color: ColorResource.textBlue(context.isDarkMode),
                    fontWeight: ui.FontWeight.bold,
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  deleteAll();
                },
              ),
            ],
          );
        },
      );
    }

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            context.tr('storage.remove_all_confirm_title'),
            style:
                TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
          ),
          content: Text(
            context.tr('storage.remove_all_confirm_message'),
            style:
                TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: ColorResource.textGrey(context.isDarkMode),
              ),
              child: Text(
                context.tr('storage.cancel'),
                style: TextStyle(
                    color: ColorResource.textDefault(context.isDarkMode)),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                context.tr('storage.continue'),
                style: TextStyle(
                    color: ColorResource.textBlue(context.isDarkMode)),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                deleteAll();
              },
            ),
          ],
        );
      },
    );
  }

  Widget buildIndicatorInfo(String title, int spaceBytes, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: ColorResource.textDefault(context.isDarkMode),
          ),
        ),
        const SizedBox(width: 8),
        Container(
          width: 4,
          height: 4,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: ColorResource.textGrey(context.isDarkMode),
          ),
        ),
        const SizedBox(width: 8),
        Directionality(
          textDirection: ui.TextDirection.ltr,
          child: Text(
            CommonUtils.humanReadableKBCountBin(spaceBytes / 1024),
            style: TextStyle(
              fontSize: 12,
              color: ColorResource.textGrey(context.isDarkMode),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildDownloadedList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.tr('storage.downloaded'),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.only(bottom: deleteButtonHeight),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.0),
                    color: ColorResource.backgroundWhite(context.isDarkMode),
                  ),
                  child: Column(
                    children: [
                      buildTranslationList(),
                      const Divider(thickness: .2, height: 1),
                      buildModelList(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildTranslationList() {
    return ListView.separated(
      shrinkWrap: true,
      itemCount: translationToFileMap.length,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        final translationEntry = translationToFileMap.entries.toList()[index];
        return buildDownloadedTranslationItem(translationEntry);
      },
      separatorBuilder: (context, index) {
        return const Divider(thickness: .2, height: 1);
      },
    );
  }

  Widget buildModelList() {
    return ListView.separated(
      shrinkWrap: true,
      itemCount: modelToFileMap.length,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        final modelEntry = modelToFileMap.entries.toList()[index];
        return buildDownloadedModelItem(modelEntry);
      },
      separatorBuilder: (context, index) {
        return const Divider(thickness: .2, height: 1);
      },
    );
  }

  void toggleTranslation(MapEntry<Translation, File> translationEntry) {
    final translation = translationEntry.key;
    final file = translationEntry.value;

    setState(() {
      if (selectedTranslationToFileMap.containsKey(translation)) {
        selectedTranslationToFileMap.remove(translation);
      } else {
        selectedTranslationToFileMap[translation] = file;
      }
    });
  }

  Widget buildDownloadedTranslationItem(
      MapEntry<Translation, File> translationEntry) {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;

    final translation = translationEntry.key;
    final file = translationEntry.value;
    final fileSize = FileUtils.getFileSize(file);
    final readableFileSize =
        CommonUtils.humanReadableKBCountBin(fileSize / 1024);

    return InkWell(
      onTap: () {
        if (translation.key == defaultTranslationKey) return;
        toggleTranslation(translationEntry);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: RichText(
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: (translation.nativeTranslatedName.isNotEmpty)
                          ? translation.nativeTranslatedName
                          : translation.name,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: ColorResource.textDefault(context.isDarkMode),
                      ),
                    ),
                    TextSpan(
                      text: " • ",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: ColorResource.textDefault(context.isDarkMode),
                      ),
                    ),
                    TextSpan(
                      text: context.tr('storage.translation'),
                      style: TextStyle(
                        fontSize: 10,
                        color: ColorResource.textGrey(context.isDarkMode),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Directionality(
              textDirection: ui.TextDirection.ltr,
              child: Text(
                readableFileSize,
                style: TextStyle(
                  fontSize: 10,
                  color: ColorResource.textGrey(context.isDarkMode),
                ),
              ),
            ),
            if (translation.key == defaultTranslationKey)
              Padding(
                padding: EdgeInsets.only(
                  left: isRtl ? 0 : 16,
                  top: 16,
                  bottom: 16,
                  right: isRtl ? 16 : 0,
                ),
                child: Text(
                  context.tr('storage.default'),
                  style: TextStyle(
                    fontSize: 10,
                    color: ColorResource.textGrey(context.isDarkMode),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              )
            else
              Checkbox(
                activeColor: (context.isDarkMode) ? Colors.white : Colors.black,
                value: selectedTranslationToFileMap.containsKey(translation),
                onChanged: (value) {
                  toggleTranslation(translationEntry);
                },
              ),
          ],
        ),
      ),
    );
  }

  void toggleModel(MapEntry<ModelItemStorage, File> modelEntry) {
    final model = modelEntry.key;
    final file = modelEntry.value;

    setState(() {
      if (selectedModelToFileMap.containsKey(model)) {
        selectedModelToFileMap.remove(model);
      } else {
        selectedModelToFileMap[model] = file;
      }
    });
  }

  Widget buildDownloadedModelItem(MapEntry<ModelItemStorage, File> modelEntry) {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;

    final model = modelEntry.key;
    final fileSize = model.size;
    final readableFileSize =
        CommonUtils.humanReadableKBCountBin(fileSize / 1024);

    return InkWell(
      onTap: () {
        if (model.isDefault) return;
        toggleModel(modelEntry);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              model.readableName,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: ColorResource.textDefault(context.isDarkMode),
              ),
            ),
            Text(
              " • ",
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: ColorResource.textDefault(context.isDarkMode),
              ),
            ),
            Text(
              context.tr('storage.ai_model'),
              style: TextStyle(
                fontSize: 10,
                color: ColorResource.textGrey(context.isDarkMode),
              ),
            ),
            const Spacer(),
            Directionality(
              textDirection: ui.TextDirection.ltr,
              child: Text(
                readableFileSize,
                style: TextStyle(
                  fontSize: 10,
                  color: ColorResource.textGrey(context.isDarkMode),
                ),
              ),
            ),
            if (model.isDefault)
              Padding(
                padding: EdgeInsets.only(
                  left: isRtl ? 0 : 16,
                  top: 16,
                  bottom: 16,
                  right: isRtl ? 16 : 0,
                ),
                child: Text(
                  context.tr('storage.default'),
                  style: TextStyle(
                    fontSize: 10,
                    color: ColorResource.textGrey(context.isDarkMode),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              )
            else
              Checkbox(
                activeColor: (context.isDarkMode) ? Colors.white : Colors.black,
                value: selectedModelToFileMap.containsKey(model),
                onChanged: (value) {
                  toggleModel(modelEntry);
                },
              ),
          ],
        ),
      ),
    );
  }
}
