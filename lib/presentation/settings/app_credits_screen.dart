import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/resource/color_resource.dart';

class AppCreditsScreen extends StatelessWidget {
  const AppCreditsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(context),
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
        child: ListView(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('about.app_credits'),
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 26,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  Text(
                    context.tr('about.intro_text'),
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('about.consultants.title'),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  Text(
                    context.tr('about.consultants.list'),
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('about.tanzil.title'),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(Assets.imagesTanzil, fit: BoxFit.cover),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.tr('about.tanzil.description'),
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('about.everyayah.title'),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child:
                        Image.asset(Assets.imagesEveryayah, fit: BoxFit.cover),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.tr('about.everyayah.description'),
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('about.quran_com.title'),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(Assets.imagesQuran, fit: BoxFit.cover),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.tr('about.quran_com.description'),
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('about.tarteel.title'),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(Assets.imagesTarteel, fit: BoxFit.cover),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.tr('about.tarteel.description'),
                    style: TextStyle(
                      fontFamily: 'ProductSansRegular',
                      fontSize: 16,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  AppBar buildAppBar(BuildContext context) {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Text(
        context.tr('about.app_credits'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
    );
  }
}
