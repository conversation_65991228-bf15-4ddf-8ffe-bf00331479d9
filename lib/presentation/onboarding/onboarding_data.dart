import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mushafi/presentation/model/introduction_step.dart';
import 'package:mushafi/presentation/model/introduction_step_data.dart';

IntroductionStepData getIntroductionStepData(
    BuildContext context, IntroductionStep step) {
  switch (step) {
    case IntroductionStep.first:
      return IntroductionStepData(
        "assets/svgs/img_onboarding_read_listen_quran.svg",
        AppLocalizations.of(context)!.sentenceReadListenQuranOnboardingTitle,
        AppLocalizations.of(context)!
            .sentenceReadListenQuranOnboardingDescription,
      );
    case IntroductionStep.second:
      return IntroductionStepData(
        "assets/svgs/img_onboarding_memorizing.svg",
        AppLocalizations.of(context)!.sentenceMemorizingOnboardingTitle,
        AppLocalizations.of(context)!.sentenceMemorizingOnboardingDescription,
      );
    case IntroductionStep.third:
      return IntroductionStepData(
        "assets/svgs/img_onboarding_voice_search.svg",
        AppLocalizations.of(context)!.sentenceVoiceSearchOnboardingTitle,
        AppLocalizations.of(context)!.sentenceVoiceSearchOnboardingDescription,
      );
  }
}
