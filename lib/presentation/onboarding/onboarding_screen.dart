import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/resource/color.dart';

import '../model/introduction_step.dart';
import 'onboarding_data.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  IntroductionStep _introductionStep = IntroductionStep.first;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        switch (_introductionStep) {
          case IntroductionStep.first:
            SystemNavigator.pop(animated: true);
            break;
          case IntroductionStep.second:
            setState(() {
              _introductionStep = IntroductionStep.first;
            });
          case IntroductionStep.third:
            setState(() {
              _introductionStep = IntroductionStep.second;
            });
        }
      },
      child: <PERSON><PERSON><PERSON>(
        child: Scaffold(
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: _introduction(context),
                ),
                FilledButton(
                  key: const ValueKey("continueButton"),
                  style: FilledButton.styleFrom(
                    backgroundColor: ColorResource.primary,
                  ),
                  onPressed: () {
                    switch (_introductionStep) {
                      case IntroductionStep.third:
                        break;
                      case IntroductionStep.first:
                        setState(() {
                          _introductionStep = IntroductionStep.second;
                        });
                      case IntroductionStep.second:
                        setState(() {
                          _introductionStep = IntroductionStep.third;
                        });
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(AppLocalizations.of(context)!.titleContinue),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _introductionIndicator(bool selected, int stepNumber) {
    return Stack(
      alignment: AlignmentDirectional.center,
      children: [
        Container(
          width: 24.0,
          height: 24.0,
          decoration: BoxDecoration(
            color: selected
                ? ColorResource.primary
                : ColorResource.backgroundGreyDark,
            shape: BoxShape.circle,
          ),
        ),
        Text(
          stepNumber.toString(),
          style: TextStyle(
              fontSize: 12, color: selected ? Colors.white : Colors.black),
        )
      ],
    );
  }

  List<Widget> _introductionIndicatorList() {
    return IntroductionStep.values.mapIndexed((index, step) {
      return Padding(
        padding: const EdgeInsets.only(right: 8),
        child: _introductionIndicator(step == _introductionStep, index + 1),
      );
    }).toList();
  }

  Widget _introduction(BuildContext context) {
    final data = getIntroductionStepData(context, _introductionStep);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SvgPicture.asset(data.imageAsset),
        const SizedBox(height: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: _introductionIndicatorList(),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Text(
                data.title,
                style:
                    const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ),
            Text(
              data.description,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ],
    );
  }
}
