import 'dart:io';

import 'package:collection/collection.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/minio_data_manager.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/model/ai/asr_model_type.dart';
import 'package:mushafi/presentation/model/ai/cond_model_type.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/presentation/model/ai/model_preparation_state.dart';
import 'package:mushafi/presentation/model/ai/rtf_data.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/inference_init_manager.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:mushafi/utils/model_fetch_manager.dart';
import 'package:mushafi/utils/word_image_render_helper.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  ModelPreparationState modelPreparationState = ModelPreparationState.idle;
  double progress = 0;

  Future<void> fetchModels(InferenceModel model) async {
    modelPreparationState = ModelPreparationState.downloading;
    if (mounted) {
      setState(() {});
    }

    if (ModelFetchManager.instance.isFetching) {
      await ModelFetchManager.instance.waitForFetchOperation();
    } else {
      await ModelFetchManager.instance.runFetchOperation(model);
    }

    modelPreparationState = ModelPreparationState.initializing;
    progress = 1;
    if (mounted) {
      setState(() {});
    }
  }

  void saveCompatibleData(RtfData rtfData) {
    final prevModelCompatible = PreferenceStorage.getModelCompatible();
    PreferenceStorage.saveModelCompatible(rtfData.isCompatible);
    if (prevModelCompatible != rtfData.isCompatible && !rtfData.isCompatible) {
      PreferenceStorage.saveShowIncompatibleCard(true);
    }
  }

  Future<void> prepareModels() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        if (androidInfo.supported32BitAbis.isNotEmpty &&
            androidInfo.supported64BitAbis.isEmpty) {
          final data = RtfData(rtf: 1);
          saveCompatibleData(data);

          // skip the preparation if the models are not compatible
          if (!data.isCompatible) {
            await deleteModels();
            if (mounted) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const HomeScreen()),
              );
            }

            return;
          }
        }
      }

      final minioRepository = ref.read(minioDataRepositoryProvider);
      String? modelVersion = PreferenceStorage.getLatestTestedModelVersion() ??
          await minioRepository.getLatestModelVersion();
      if (modelVersion == null) {
        await showPrepareModelsFailedDialog();
        return;
      }

      final latestModelVersion =
          await minioRepository.updateAndGetLatestModelVersion();
      if (modelVersion != latestModelVersion) {
        final indexMap = await minioRepository.getModelIndexMap();

        final lastTestedModelVersionEntry =
            indexMap.entries.firstWhereOrNull((entry) {
          return entry.value.contains(modelVersion);
        });
        final lastTestedCompat = lastTestedModelVersionEntry?.key;

        if (lastTestedCompat != InferenceModel.modelCompat) {
          PreferenceStorage.saveLatestTestedModelVersion(null);
          modelVersion = latestModelVersion;
        }
      }

      if (modelVersion == null) {
        await showPrepareModelsFailedDialog();
        return;
      }

      final model = InferenceModel.fromType(
        AsrModelType.quantized,
        CondModelType.quantized,
        PreferenceStorage.getSelectedModel(),
        modelVersion!,
      );

      await FirebaseAuth.instance.signInAnonymously();

      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      final rtf = await firestoreRepository.getRTF(modelVersion);

      if (rtf != null) {
        final data = RtfData(rtf: rtf);
        saveCompatibleData(data);

        // skip the preparation if the models are not compatible
        if (!data.isCompatible) {
          await deleteModels();
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const HomeScreen()),
            );
          }

          return;
        }
      }

      final modelsExist =
          await ref.read(minioDataRepositoryProvider).doModelsExist(model);
      if (!modelsExist) {
        await deleteModels();
        await fetchModels(model);
      }

      final success = (InferenceInitManager.instance.isInit)
          ? await InferenceInitManager.instance.waitForInitOperation()
          : await InferenceInitManager.instance.runInitOperation(model, () {
              return fetchModels(model);
            });

      if (!success) {
        await showPrepareModelsFailedDialog();
        return;
      }

      // do the testing if the rtf data is not available in the firestore
      if (rtf == null) {
        modelPreparationState = ModelPreparationState.testing;
        if (mounted) {
          setState(() {});
        }

        final newRtf = InferencePipeline.getInstance().testCompatibility();
        final newRtfData = RtfData(rtf: newRtf);
        saveCompatibleData(newRtfData);

        if (!newRtfData.isCompatible) {
          await deleteModels();
        }

        await firestoreRepository.setRTF(newRtf, modelVersion);

        modelPreparationState = ModelPreparationState.idle;
        if (mounted) {
          setState(() {});
        }
      }

      final micPermissionStatus = await Permission.microphone.status;
      if (!micPermissionStatus.isGranted) {
        InferencePipeline.getInstance().delete();
      }

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      }
    } catch (e, st) {
      FirebaseCrashlytics.instance.recordError(e, st);
      talker.handle(e, st);
      await showPrepareModelsFailedDialog();
    }
  }

  Future<void> showPrepareModelsFailedDialog() async {
    if (Platform.isIOS || Platform.isMacOS) {
      await showCupertinoDialog(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(
              context.tr('splash.model_error.title'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            content: Text(
              context.tr('splash.model_error.restart_message'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            actions: <Widget>[
              TextButton(
                child: Text(
                  context.tr('splash.model_error.ok'),
                  style: TextStyle(
                      color: ColorResource.textBlue(context.isDarkMode)),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );

      if (mounted) {
        SystemNavigator.pop();
      }

      return;
    }

    await showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            context.tr('splash.model_error.title'),
            style:
                TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
          ),
          content: Text(
            context.tr('splash.model_error.try_again_message'),
            style:
                TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: ColorResource.textGrey(context.isDarkMode),
              ),
              child: Text(
                context.tr('splash.model_error.ok'),
                style: TextStyle(
                    color: ColorResource.textDefault(context.isDarkMode)),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );

    if (mounted) {
      SystemNavigator.pop();
    }
  }

  Future<void> deleteModels() async {
    InferencePipeline.getInstance().delete();

    final dataDirectory = await getApplicationDocumentsDirectory();
    final modelsDirectory =
        Directory('${dataDirectory.path}/${MinioDataManager.modelsPath}');
    final corpusDirectory =
        Directory('${dataDirectory.path}/${MinioDataManager.quranCorpusPath}');

    if (await modelsDirectory.exists()) {
      await modelsDirectory.delete(recursive: true);
    }

    if (await corpusDirectory.exists()) {
      await corpusDirectory.delete(recursive: true);
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await WordImageRenderHelper.preloadWordImages();
      prepareModels();
    });

    ModelFetchManager.instance.onDownloading = (double percentage) {
      if (mounted) {
        setState(() {
          progress = percentage;
        });
      }
    };

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final String mushafiLogoAsset;
    if (context.isArabic) {
      mushafiLogoAsset = context.isDarkMode
          ? Assets.svgsImgMushafiVerticalArabicDarkMode
          : Assets.svgsImgMushafiVerticalArabic;
    } else {
      mushafiLogoAsset = context.isDarkMode
          ? Assets.svgsImgMushafiVerticalDarkMode
          : Assets.svgsImgMushafiVertical;
    }

    final progressText = switch (modelPreparationState) {
      ModelPreparationState.idle => '',
      ModelPreparationState.downloading =>
        context.tr('splash.model_state.downloading'),
      ModelPreparationState.initializing =>
        context.tr('splash.model_state.initializing'),
      ModelPreparationState.testing => context.tr('splash.model_state.testing'),
    };

    return Scaffold(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      body: Center(
        child: FractionallySizedBox(
          widthFactor: .5,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(mushafiLogoAsset),
              Visibility(
                visible: modelPreparationState != ModelPreparationState.idle,
                maintainSize: true,
                maintainAnimation: true,
                maintainState: true,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    LinearProgressIndicator(
                      value: progress,
                      backgroundColor:
                          ColorResource.backgroundWhite(context.isDarkMode),
                      valueColor: AlwaysStoppedAnimation(
                          ColorResource.primary(context.isDarkMode)),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      progressText,
                      style: TextStyle(
                          color: ColorResource.textDefault(context.isDarkMode)),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
