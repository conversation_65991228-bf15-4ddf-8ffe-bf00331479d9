import 'dart:ui';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/model/quran/alignment_word_highlight.dart';
import 'package:mushafi/presentation/model/quran/audio_segment_init_state.dart';
import 'package:mushafi/presentation/model/quran/highlighted_verse_key.dart';
import 'package:mushafi/presentation/model/quran/quran_widget_state.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/presentation/model/quran/recitation_pending_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_image_asset.dart';
import 'package:mushafi/presentation/model/recitation/word_play_state.dart';
import 'package:mushafi/presentation/model/translation/translation.dart';
import 'package:mushafi/presentation/notifier/audio_segment_data_init_notifier.dart';
import 'package:mushafi/presentation/notifier/focus_highlight_notifier.dart';
import 'package:mushafi/presentation/notifier/highlighted_verse_key_notifier.dart';
import 'package:mushafi/presentation/notifier/mushaf_data_init_notifier.dart';
import 'package:mushafi/presentation/notifier/quran_image_asset_notifier.dart';
import 'package:mushafi/presentation/notifier/quran_navigation_height_notifier.dart';
import 'package:mushafi/presentation/notifier/quran_page_data_notifier.dart';
import 'package:mushafi/presentation/notifier/reciter_translation_option_dialog_shown.dart';
import 'package:mushafi/presentation/notifier/request_next_recitation_notifier.dart';
import 'package:mushafi/presentation/notifier/request_recitation_notifier.dart';
import 'package:mushafi/presentation/notifier/restart_activity_notifier.dart';
import 'package:mushafi/presentation/notifier/reveal_verse_notifier.dart';
import 'package:mushafi/presentation/notifier/reveal_word_notifier.dart';
import 'package:mushafi/presentation/notifier/revert_undo_alignment_notifier.dart';
import 'package:mushafi/presentation/notifier/show_hint_notifier.dart';
import 'package:mushafi/presentation/notifier/stop_recitation_notifier.dart';
import 'package:mushafi/presentation/notifier/translation_sheet_height_notifier.dart';
import 'package:mushafi/presentation/notifier/translation_tafsir_data_init_notifier.dart';
import 'package:mushafi/presentation/notifier/undo_alignment_snackbar_notifier.dart';
import 'package:mushafi/presentation/notifier/undo_word_notifier.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/utils/method_channel_helper.dart';
import 'package:uuid/uuid.dart';

final transcriptionProvider = StateProvider((ref) => '');

final alignmentResultProvider =
    StateProvider<AlignmentWordHighlight?>((ref) => null);

final currentAlignmentHighlightProvider =
    StateProvider<AlignmentWordHighlight?>((ref) => null);

final revealWordProvider =
    ChangeNotifierProvider((ref) => RevealWordNotifier());

final revealVerseProvider =
    ChangeNotifierProvider((ref) => RevealVerseNotifier());

final restartActivityProvider =
    ChangeNotifierProvider((ref) => RestartActivityNotifier());

final undoWordProvider = ChangeNotifierProvider((ref) => UndoWordNotifier());

final showHintProvider = ChangeNotifierProvider((ref) => ShowHintNotifier());

final alignmentHighlightRectProvider = StateProvider<Rect?>((ref) => null);

final quranPageDataProvider =
    StateNotifierProvider<QuranPageDataNotifier, QuranPageDataState>((ref) {
  final quranRepository = ref.watch(quranRepositoryProvider);
  return QuranPageDataNotifier(quranRepository);
});

final isMobileProvider =
    FutureProvider<bool>((ref) => MethodChannelHelper.isMobile());

final mushafDataInitProvider =
    AsyncNotifierProvider<MushafDataInitNotifier, void>(
        () => MushafDataInitNotifier());

final translationTafsirDataInitProvider =
    AsyncNotifierProvider<TranslationTafsirDataInitNotifier, Translation?>(
        () => TranslationTafsirDataInitNotifier());

final audioSegmentDataInitProvider =
    AsyncNotifierProvider<AudioSegmentDataInitNotifier, AudioSegmentInitState?>(
        () => AudioSegmentDataInitNotifier());

final reciterTranslationOptionDialogShownProvider = ChangeNotifierProvider(
    (ref) => ReciterTranslationOptionDialogShownNotifier());

final quranImageAssetProvider =
    AsyncNotifierProvider<QuranImageAssetNotifier, QuranImageAsset>(
        () => QuranImageAssetNotifier());

final translationSheetHeightProvider =
    StateNotifierProvider<TranslationSheetHeightNotifier, double>(
        (ref) => TranslationSheetHeightNotifier());

final transcriptionWordPlayStateProvider =
    StateProvider<WordPlayState>((ref) => WordPlayState.idle);

final quranWidgetStateProvider =
    StateProvider<QuranWidgetState>((ref) => const QuranWidgetState(
          showTopBar: false,
          showBottomBar: false,
        ));

final undoAlignmentSnackbarProvider =
    StateNotifierProvider<UndoAlignmentSnackbarNotifier, int?>((ref) {
  return UndoAlignmentSnackbarNotifier();
});

final revertUndoAlignmentProvider =
    ChangeNotifierProvider((ref) => RevertUndoAlignmentNotifier());

final showPageNavigationBarProvider = StateProvider<bool>((ref) => false);

final navigationTextSizeProvider = StateProvider<double>((ref) => 1);

final showAlignmentEndSnackbarProvider = StateProvider<bool>((ref) => false);

final requestRecitationProvider =
    ChangeNotifierProvider((ref) => RequestRecitationNotifier());

final requestNextRecitationProvider =
    ChangeNotifierProvider((ref) => RequestNextRecitationNotifier());

final stopRecitationProvider =
    ChangeNotifierProvider((ref) => StopRecitationNotifier());

final recitationPlayingProvider = StateProvider<bool>((ref) => false);

final recitationPendingProvider =
    StateProvider<RecitationPendingData?>((ref) => null);

final highlightedVerseKeyProvider =
    StateNotifierProvider<HighlightedVerseKeyNotifier, HighlightedVerseKey?>(
        (ref) => HighlightedVerseKeyNotifier());

final highlightedWordProvider = StateProvider<QuranWord?>((ref) => null);

final pageIdProvider = StateProvider((ref) => const Uuid().v1());

final quranNavigationHeightNotifierProvider =
    StateNotifierProvider<QuranNavigationHeightNotifier, double>((ref) {
  return QuranNavigationHeightNotifier();
});

final quranPageNavigationTopProvider = StateProvider((ref) => 0.0);

final focusHighlightProvider =
    ChangeNotifierProvider((ref) => FocusHighlightNotifier());

final reciterSheetShownProvider = StateProvider((ref) => false);
