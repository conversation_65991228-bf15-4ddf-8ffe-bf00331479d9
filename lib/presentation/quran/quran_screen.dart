import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/string_extension.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/ai/asr_model_type.dart';
import 'package:mushafi/presentation/model/ai/cond_model_type.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/quran_mode.dart';
import 'package:mushafi/presentation/model/quran/quran_widget_state.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_data.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/inference_init_manager.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:mushafi/utils/model_fetch_manager.dart';
import 'package:mushafi/utils/recitation_player.dart';
import 'package:mushafi/widget/activity/activity_loading_page.dart';
import 'package:mushafi/widget/quran/juz_navigation_drawer.dart';
import 'package:mushafi/widget/quran/page_navigation_bar.dart';
import 'package:mushafi/widget/quran/quran_additional_widget.dart';
import 'package:mushafi/widget/quran/quran_view.dart';
import 'package:mushafi/widget/quran/surah_navigation_drawer.dart';
import 'package:mushafi/widget/quran/transcription_bar.dart';
import 'package:permission_handler/permission_handler.dart';

final quranModeProvider = StateProvider<QuranMode>((ref) => DefaultQuranMode());

final recitationAlignmentBarHeightProvider = StateProvider<double>((ref) => 0);

class QuranScreen extends ConsumerStatefulWidget {
  const QuranScreen({super.key});

  @override
  ConsumerState<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends ConsumerState<QuranScreen> {
  final quranCanvasData = QuranCanvasData();

  final inferencePipeline = InferencePipeline.getInstance();
  bool isFetchingModels = false;

  Timer? barDelayTimer;

  Future<void> fetchModels(InferenceModel model) async {
    isFetchingModels = true;
    if (mounted) {
      setState(() {});
    }

    if (ModelFetchManager.instance.isFetching) {
      await ModelFetchManager.instance.waitForFetchOperation();
    } else {
      await ModelFetchManager.instance.runFetchOperation(model);
    }

    isFetchingModels = false;
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> setupInferencePipeline() async {
    if (!inferencePipeline.isInitialized && !inferencePipeline.isInitializing) {
      if (mounted) {
        final modelVersion = PreferenceStorage.getLatestTestedModelVersion();
        if (modelVersion == null) return;

        final model = InferenceModel.fromType(
          AsrModelType.quantized,
          CondModelType.quantized,
          PreferenceStorage.getSelectedModel(),
          modelVersion,
        );

        final modelsExist =
            await ref.read(minioDataRepositoryProvider).doModelsExist(model);
        if (!modelsExist) {
          await fetchModels(model);
        }

        final success = (InferenceInitManager.instance.isInit)
            ? await InferenceInitManager.instance.waitForInitOperation()
            : await InferenceInitManager.instance.runInitOperation(model, () {
                return fetchModels(model);
              });

        if (!success && mounted) {
          CommonUtils.showToast('Failed to initialize inference pipeline');
        }

        if (mounted) {
          setState(() {});
        }
      }
    }
  }

  bool get isLoading => isFetchingModels || inferencePipeline.isInitializing;

  void restrictToPortrait() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  void allowLandscapeAndPortrait() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void initState() {
    FirebaseAnalytics.instance.logScreenView(screenName: "quran");

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.bottom]);

    ref.listenManual(selectedTrackProvider, fireImmediately: true,
        (prevTrack, nextTrack) async {
      if (prevTrack != nextTrack && nextTrack != null) {
        FirebaseAnalytics.instance.logEvent(
          name: "open_activity",
          parameters: {
            "activity_type": nextTrack.type?.name.camelToSnakeCase ?? ''
          },
        );

        if (nextTrack.type == TrackType.memorizing ||
            nextTrack.type == TrackType.readingWithAi) {
          restrictToPortrait();

          final permissionStatus = await Permission.microphone.request();
          if (permissionStatus.isGranted) {
            setupInferencePipeline();
          } else if (permissionStatus.isDenied ||
              permissionStatus.isPermanentlyDenied) {
            showNeedMicrophonePermissionToast();
          }
        }
      }
    });

    ref.listenManual(quranWidgetStateProvider, (prevState, nextState) {
      if (nextState.showTopBar) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
            overlays: SystemUiOverlay.values);
      } else {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
            overlays: [SystemUiOverlay.bottom]);
      }

      if (nextState.anyShown) {
        barDelayTimer?.cancel();
        barDelayTimer = Timer(const Duration(seconds: 4), () {
          final quranMode = ref.read(quranModeProvider.notifier).state;
          final translationSheetHeight =
              ref.read(translationSheetHeightProvider);
          final reciterSheetShown = ref.read(reciterSheetShownProvider);

          ref.read(quranWidgetStateProvider.notifier).state = QuranWidgetState(
            showTopBar: false,
            showBottomBar: quranMode is! DefaultQuranMode ||
                RecitationPlayer.player.playing ||
                translationSheetHeight > 0 ||
                reciterSheetShown,
          );
        });
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final mushafDataInit = ref.read(mushafDataInitProvider);
      if (!mushafDataInit.hasValue && !mushafDataInit.isLoading) {
        ref.invalidate(mushafDataInitProvider);
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    allowLandscapeAndPortrait();
    barDelayTimer?.cancel();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quranMode = ref.watch(quranModeProvider);
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final showPageNavigationBar = ref.watch(showPageNavigationBarProvider);
    final pageNavigationTop = ref.watch(quranPageNavigationTopProvider);

    final design = mushafDesignAsync.value?.design;
    final backgroundColor = design?.drawer1Color ??
        ColorResource.backgroundGreyDark(context.isDarkMode);
    final drawerColor = design?.drawer1Color ??
        MushafDesign.getDefault(context.isDarkMode).drawer1Color;

    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;

    return Scaffold(
      backgroundColor: backgroundColor,
      drawerEnableOpenDragGesture: false,
      endDrawerEnableOpenDragGesture: false,
      drawer: (isRtl)
          ? buildJuzNavigationDrawer(drawerColor)
          : buildSurahNavigationDrawer(drawerColor),
      endDrawer: (isRtl)
          ? buildSurahNavigationDrawer(drawerColor)
          : buildJuzNavigationDrawer(drawerColor),
      body: SafeArea(
        child: LayoutBuilder(builder: (context, constraints) {
          return PopScope(
            canPop: !Platform.isIOS,
            child: Stack(
              children: [
                buildQuranView(constraints.maxWidth, constraints.maxHeight),
                if (showPageNavigationBar)
                  Positioned(
                    top: pageNavigationTop,
                    left: 0,
                    child: PageNavigationBar(constraints.maxWidth),
                  ),
                if (quranMode is! DefaultQuranMode) const TranscriptionBar(),
                buildAdditionalWidget(
                    constraints.maxWidth, constraints.maxHeight),
                if (isLoading) ActivityLoadingPage(isFetchingModels),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget buildQuranView(double width, double height) {
    return SizedBox(
      width: width,
      height: height,
      child: QuranView(quranCanvasData, width, height),
    );
  }

  Widget buildAdditionalWidget(double layoutWidth, double layoutHeight) {
    return QuranAdditionalWidget(
      layoutWidth: layoutWidth,
      layoutHeight: layoutHeight,
      onSetupInferenceRequested: () {
        return setupInferencePipeline();
      },
    );
  }

  void showNeedMicrophonePermissionToast() {
    CommonUtils.showToast(context.tr('permission.microphone_required'));
  }

  Widget buildSurahNavigationDrawer(Color drawerColor) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Drawer(
        backgroundColor: drawerColor,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.zero)),
        child: const SurahNavigationDrawer(),
      ),
    );
  }

  Widget buildJuzNavigationDrawer(Color drawerColor) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Drawer(
        backgroundColor: drawerColor,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.zero)),
        child: const JuzNavigationDrawer(),
      ),
    );
  }
}
