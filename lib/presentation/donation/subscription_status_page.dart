import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mushafi/presentation/donation/widgets/customAmountDialog.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:mushafi/utils/stripe_payment.dart';

class SubscriptionStatusView extends StatefulWidget {
  final Map<String, dynamic> subscriptionInfo;
  final VoidCallback onMakeOneTimeDonation;
  final VoidCallback onCancelSubscription;
  final VoidCallback?
      onSubscriptionUpdated; // Optional callback to notify parent

  const SubscriptionStatusView({
    Key? key,
    required this.subscriptionInfo,
    required this.onMakeOneTimeDonation,
    required this.onCancelSubscription,
    this.onSubscriptionUpdated,
  }) : super(key: key);

  @override
  State<SubscriptionStatusView> createState() => _SubscriptionStatusViewState();
}

class _SubscriptionStatusViewState extends State<SubscriptionStatusView> {
  final StripePaymentHandler _stripeHandler = StripePaymentHandler();
  bool _isUpdating = false;
  bool _isCancelling = false;
  late Map<String, dynamic> _subscriptionInfo;

  @override
  void initState() {
    super.initState();
    _subscriptionInfo = widget.subscriptionInfo;
  }

  @override
  void didUpdateWidget(SubscriptionStatusView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.subscriptionInfo != oldWidget.subscriptionInfo) {
      _subscriptionInfo = widget.subscriptionInfo;
    }
  }

  void _handleUpdateSubscriptionAmount(int newAmount, bool isDark) async {
    final currentAmount = (_subscriptionInfo['amount'] ?? 0.0).toInt();

    // Validation: Check if amount is 0 or same as current
    if (newAmount == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(tr('home.subscription_status.amount_cannot_be_zero')),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (newAmount == currentAmount) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(tr('home.subscription_status.amount_unchanged')),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() => _isUpdating = true);

    await _stripeHandler.updateSubscriptionAmount(
      subscriptionId: _subscriptionInfo['subscription_id'],
      newAmount: newAmount.toDouble(),
      onSuccess: (updatedSubscriptionInfo) {
        setState(() {
          _subscriptionInfo = {
            ...updatedSubscriptionInfo,
            'amount': newAmount.toDouble(),
          };
          _isUpdating = false;
        });

        // Notify parent if callback provided
        widget.onSubscriptionUpdated?.call();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text(tr('home.subscription_status.amount_updated_success')),
              backgroundColor: ColorResource.primary(isDark),
            ),
          );
        }
      },
      onError: (error) {
        setState(() => _isUpdating = false);

        if (mounted) {
          String errorMessage;
          if (error.contains('No internet')) {
            errorMessage = context.tr('common.no_internet');
          } else if (error.contains('timed out')) {
            errorMessage = context.tr('common.request_timeout');
          } else {
            errorMessage = context.tr('home.donation_page.subscription_error');
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: ColorResource.danger(isDark),
            ),
          );
          print('Subscription update failed: $error');
        }
      },
    );
  }

  void _showCancelConfirmationDialog(bool isDark) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: ColorResource.backgroundWhite(isDark),
          title: Text(
            tr('home.subscription_status.cancel_confirmation_title'),
            style: TextStyle(
              color: ColorResource.textDefault(isDark),
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            tr('home.subscription_status.cancel_confirmation_message'),
            style: TextStyle(
              color: ColorResource.textGrey(isDark),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                tr('common.no_cancel'),
                style: TextStyle(
                  color: ColorResource.primary(isDark),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _cancelSubscription(isDark);
              },
              child: Text(
                tr('common.yes_cancel'),
                style: TextStyle(
                  color: ColorResource.danger(isDark),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _cancelSubscription(bool isDark) async {
    setState(() => _isCancelling = true);

    await _stripeHandler.cancelSubscription(
      subscriptionId: _subscriptionInfo['subscription_id'],
      onSuccess: (cancellationInfo) {
        setState(() {
          _isCancelling = false;
          _subscriptionInfo = cancellationInfo;
        });

        // Notify parent about the cancellation
        widget.onCancelSubscription();
        widget.onSubscriptionUpdated?.call();

        // Show success message with end date
        if (mounted) {
          final endDate = DateTime.fromMillisecondsSinceEpoch(
            cancellationInfo['current_period_end'] * 1000,
          );
          final formattedDate = DateFormat('MMMM d, yyyy').format(endDate);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                tr('home.subscription_status.cancel_success',
                    args: [formattedDate]),
              ),
              backgroundColor: ColorResource.primary(isDark),
              duration: const Duration(seconds: 5),
            ),
          );
        }
      },
      onError: (error) {
        setState(() => _isCancelling = false);

        if (mounted) {
          String errorMessage;
          if (error.contains('No internet')) {
            errorMessage = context.tr('common.no_internet');
          } else if (error.contains('timed out')) {
            errorMessage = context.tr('common.request_timeout');
          } else {
            errorMessage = context.tr('home.subscription_status.cancel_error');
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: ColorResource.danger(isDark),
            ),
          );
          print('Subscription cancellation failed: $error');
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final amount = _subscriptionInfo['amount'] ?? 0.0;
    final nextPayment = _subscriptionInfo['current_period_end'] != null
        ? DateTime.fromMillisecondsSinceEpoch(
            _subscriptionInfo['current_period_end'] * 1000)
        : null;
    final isCancelledAtPeriodEnd =
        _subscriptionInfo['cancel_at_period_end'] ?? false;

    return SafeArea(
      child: Stack(
        children: [
          Container(
            color: ColorResource.backgroundDefault(isDark),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const SizedBox(width: 48),
                        Expanded(
                          child: Center(
                            child: Text(
                              tr('home.subscription_status.title'),
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: ColorResource.textDefault(isDark),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 48),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Center(
                      child: Text(
                        tr('home.subscription_status.thank_you'),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: ColorResource.textGrey(isDark),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Subscription Status Card
                    Container(
                      decoration: BoxDecoration(
                        color: ColorResource.backgroundWhite(isDark),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: ColorResource.border(isDark)),
                      ),
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                tr('home.subscription_status.current_plan'),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: ColorResource.textDefault(isDark),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 4),
                                decoration: BoxDecoration(
                                  color: isCancelledAtPeriodEnd
                                      ? Colors.orange[600]
                                      : Colors.green[600],
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  isCancelledAtPeriodEnd
                                      ? tr(
                                          'home.subscription_status.cancelling')
                                      : tr('home.subscription_status.active'),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Text(
                            tr('home.subscription_status.monthly_amount'),
                            style: TextStyle(
                              color: ColorResource.textGrey(isDark),
                            ),
                          ),
                          Text(
                            "${_subscriptionInfo['currency'].toString().toUpperCase()} ${amount.toStringAsFixed(2)}",
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: ColorResource.textDefault(isDark),
                            ),
                          ),
                          const Divider(),
                          if (nextPayment != null)
                            Text(
                              isCancelledAtPeriodEnd
                                  ? tr('home.subscription_status.ends_on',
                                      args: [
                                          DateFormat('MMMM d, yyyy')
                                              .format(nextPayment)
                                        ])
                                  : tr(
                                      'home.subscription_status.next_payment_on',
                                      args: [
                                          DateFormat('MMMM d, yyyy')
                                              .format(nextPayment)
                                        ]),
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: isCancelledAtPeriodEnd
                                    ? ColorResource.danger(isDark)
                                    : ColorResource.textDefault(isDark),
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Show different buttons based on cancellation status
                    if (!isCancelledAtPeriodEnd) ...[
                      ElevatedButton(
                        onPressed: _isUpdating || _isCancelling
                            ? null
                            : () => showCustomAmountDialog(
                                  context: context,
                                  currency: NumberFormat.simpleCurrency(
                                          name: _subscriptionInfo['currency']
                                              .toUpperCase())
                                      .currencySymbol,
                                  initialAmount: amount.toInt(),
                                  onAmountSelected: (amount) =>
                                      _handleUpdateSubscriptionAmount(
                                          amount, isDark),
                                ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              ColorResource.chipBackgroundSelected(isDark),
                          foregroundColor:
                              ColorResource.chipTextSelected(isDark),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        child: _isUpdating
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        ColorResource.chipTextSelected(isDark),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    tr('home.subscription_status.updating'),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              )
                            : Text(
                                tr('home.subscription_status.change_amount'),
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    ElevatedButton(
                      onPressed: _isUpdating || _isCancelling
                          ? null
                          : widget.onMakeOneTimeDonation,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorResource.primary(isDark),
                        foregroundColor: ColorResource.onPrimary(isDark),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text(
                        isCancelledAtPeriodEnd
                            ? tr('home.donation_page.donate')
                            : tr('home.subscription_status.make_one_time'),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (!isCancelledAtPeriodEnd)
                      OutlinedButton(
                        onPressed: _isUpdating || _isCancelling
                            ? null
                            : () => _showCancelConfirmationDialog(isDark),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: ColorResource.danger(isDark),
                          side: BorderSide(color: ColorResource.danger(isDark)),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        child: _isCancelling
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        ColorResource.danger(isDark),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    tr('home.subscription_status.cancelling'),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              )
                            : Text(
                                tr('home.subscription_status.cancel'),
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          if (_isUpdating || _isCancelling)
            Container(
              color: Colors.black54,
              child: Center(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _isCancelling
                                ? ColorResource.danger(isDark)
                                : ColorResource.primary(isDark),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _isCancelling
                              ? tr('home.subscription_status.cancelling')
                              : tr(
                                  'home.subscription_status.updating_subscription'),
                          style: TextStyle(
                            color: ColorResource.textDefault(isDark),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
