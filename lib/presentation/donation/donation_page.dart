// TODO: Detect if there is no internet connection and show a page for that
// TODO: refactor the code to have a page controller to handle the state, either
// showing the donation or subscription status page
// TODO: Current implementation combines UI methods with business logic, which
//is not ideal. Consider using a state management solution like Riverpod to separate concerns.
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui' as ui;
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/presentation/donation/subscription_status_page.dart';
import 'package:mushafi/presentation/donation/widgets/customAmountDialog.dart';
import 'package:mushafi/presentation/donation/widgets/email_verification_dialog.dart';
import 'package:mushafi/presentation/donation/widgets/email_input_dialog.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/utils/currency_service.dart';
import 'package:mushafi/utils/donation_amounts.dart';
import 'package:mushafi/utils/stripe_payment.dart';

class DonationPage extends ConsumerStatefulWidget {
  final ValueNotifier<bool> visibilityNotifier;

  const DonationPage({Key? key, required this.visibilityNotifier})
      : super(key: key);

  @override
  ConsumerState<DonationPage> createState() => _DonationPageState();
}

class _DonationPageState extends ConsumerState<DonationPage> {
  final StripePaymentHandler _paymentHandler = StripePaymentHandler();

  static const String _defaultCurrency = 'USD';

  String _selectedCurrency = _defaultCurrency;
  String _currencySymbol = DonationAmounts.getSymbol(_defaultCurrency);
  bool _isRTLCurrency = DonationAmounts.isRTL(_defaultCurrency);
  List<int> _donationAmounts = DonationAmounts.getAmounts(_defaultCurrency)!;
  bool _isLoadingCurrency = false;
  String? _currencyError;

  int? selectedAmount;
  final TextEditingController messageController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  bool monthlyPayment = true;

  bool _isProcessing = false;

  List<Map<String, dynamic>>? _paymentHistory;
  bool _loadingHistory = false;
  String? _loadingError;

  bool _showSubscriptionStatus = false;
  Map<String, dynamic>? _subscriptionInfo;
  bool _hasCustomerId = false;
  bool _hasInitialized = false;

  // Check if user has an active subscription that's not cancelling
  bool get _hasActiveSubscription {
    return _subscriptionInfo != null &&
        !(_subscriptionInfo!['cancel_at_period_end'] ?? false) &&
        _subscriptionInfo!['status'] == 'active';
  }

  @override
  void initState() {
    super.initState();
    FirebaseCrashlytics.instance.log('donation_flow: Page initialized');

    final savedEmail = PreferenceStorage.getEmail();
    if (savedEmail != null) {
      emailController.text = savedEmail;
      FirebaseCrashlytics.instance.log('donation_flow: Saved email loaded');
    }

    // Initialize currency even if not visible yet because it is a one-time setup
    _initializeCurrency();

    // Listen to visibility changes
    widget.visibilityNotifier.addListener(_onVisibilityChanged);

    // Check if already visible on init
    if (widget.visibilityNotifier.value) {
      _hasInitialized = true;
      _initializeData();
    }
  }

  void _onVisibilityChanged() {
    if (widget.visibilityNotifier.value && !_hasInitialized) {
      _hasInitialized = true;
      FirebaseCrashlytics.instance.log('donation_flow: Page became visible');
      // Ensure UI updates first, then run API calls asynchronously
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeData().catchError((error) {
          FirebaseCrashlytics.instance.log(
              'donation_flow: Error initializing data - ${error.toString()}');
          FirebaseCrashlytics.instance
              .recordError(error, StackTrace.current, fatal: false);
          print('Error initializing donation data: $error');
        });
      });
    }
  }

  Future<void> _initializeData() async {
    FirebaseCrashlytics.instance
        .log('donation_flow: Data initialization started');
    _checkCustomerId();
    _loadPaymentHistory();
    _checkSubscription();
  }

  /// Checks if a customer ID is already stored using the helper function
  Future<void> _checkCustomerId() async {
    FirebaseCrashlytics.instance.log('donation_flow: Checking customer ID');
    String? customerId = await _getOrCreateCustomer();
    setState(() {
      _hasCustomerId = customerId != null;
    });
    FirebaseCrashlytics.instance
        .log('donation_flow: Customer ID check - ${customerId ?? "not found"}');
  }

  @override
  void dispose() {
    widget.visibilityNotifier.removeListener(_onVisibilityChanged);
    messageController.dispose();
    emailController.dispose();
    super.dispose();
  }

  Future<void> _saveEmail(String email) async {
    await PreferenceStorage.saveEmail(email);
  }

  /// Loads payment history for the current user
  ///
  /// This method attempts to retrieve and display the user's one-time payment history:
  /// 1. Sets loading state and clears previous errors
  /// 2. Gets cached customer ID (returns early if no customer exists)
  /// 3. Fetches payment history from backend using customer ID
  /// 4. Updates UI state with payment data or error messages
  ///
  /// The method gracefully handles cases where:
  /// - No customer ID is cached (shows no payment history)
  /// - Network errors occur (shows localized error messages)
  /// - Backend errors occur (shows generic loading error)
  ///
  /// Updates: _loadingHistory, _loadingError, _paymentHistory state variables
  Future<void> _loadPaymentHistory() async {
    setState(() {
      _loadingHistory = true;
      _loadingError = null;
    });

    // Get cached customer ID (no email needed)
    String? customerId = await _getOrCreateCustomer();

    if (customerId == null) {
      // No customer ID available - no payment history to show
      setState(() {
        _loadingHistory = false;
        _paymentHistory = null;
      });
      return;
    }

    await _paymentHandler.getPaymentHistory(
      customerId: customerId,
      onSuccess: (payments) {
        setState(() {
          _paymentHistory = payments;
          _loadingHistory = false;
        });
      },
      onError: (errorCode) {
        setState(() {
          _loadingHistory = false;
          _loadingError = _getLocalizedMessage(errorCode);
        });
      },
    );
  }

  /// Checks for existing subscription and updates UI state accordingly
  ///
  /// This method retrieves the user's subscription status on app initialization:
  /// 1. Gets cached customer ID (returns silently if no customer exists)
  /// 2. Fetches all subscriptions for the customer from backend
  /// 3. If subscriptions exist, updates UI to show subscription status
  /// 4. Automatically disables monthly payment toggle if active subscription exists
  ///
  /// The method handles the app's subscription-aware flow:
  /// - Shows subscription status page if subscription exists
  /// - Prevents duplicate subscriptions by disabling monthly toggle
  /// - Fails silently to avoid disrupting the donation flow
  ///
  /// Updates: _subscriptionInfo, _showSubscriptionStatus, monthlyPayment state variables
  Future<void> _checkSubscription() async {
    // Get cached customer ID (no email needed)
    String? customerId = await _getOrCreateCustomer();

    if (customerId == null) {
      // No customer ID available - no subscriptions to check
      return;
    }

    await _paymentHandler.getCustomerSubscriptions(
      customerId: customerId,
      onSuccess: (subscriptions) {
        if (subscriptions.isNotEmpty &&
            subscriptions.first['status'] == 'active') {
          setState(() {
            _subscriptionInfo = subscriptions.first;
            // Set monthly payment to false if there's an active subscription
            if (_hasActiveSubscription) {
              monthlyPayment = false;
              _showSubscriptionStatus = true;
            }
          });
        }
      },
      onError: (errorCode) {
        // Silently fail for subscription check (don't show error to user)
        FirebaseCrashlytics.instance
            .log('donation_flow: Failed to check subscriptions: ${errorCode}');
      },
    );
  }

  /// Gets existing customer ID or creates a new customer via email verification
  ///
  /// This method handles all customer ID logic:
  /// - If no email provided: returns cached customer ID or null
  /// - If email provided: returns cached customer ID or starts email verification flow
  /// - Uses the EmailVerificationDialog widget for the complete verification process
  /// - Customer ID is automatically cached by the verification dialog
  /// - Provides clean separation between UI and business logic
  ///
  /// [email] - Optional email address for customer creation via verification
  /// Returns: Customer ID if available/successful, null if not found/failed
  Future<String?> _getOrCreateCustomer([String? email]) async {
    // Check if we already have a cached customer ID
    String? cachedCustomerId = PreferenceStorage.getCustomerId();
    if (cachedCustomerId != null) {
      return cachedCustomerId;
    }

    // No cached customer ID - need email to create one
    if (email == null || email.isEmpty) {
      return null;
    }

    // Show email verification dialog which handles the complete verification flow
    return await showEmailVerificationDialog(
      context: context,
      email: email,
      paymentHandler: _paymentHandler,
    );
  }

  /// Maps error codes to localized error messages
  ///
  /// Handles error codes from all StripePaymentHandler methods
  ///
  /// [errorCode] - Error code returned from StripePaymentHandler methods
  /// Returns: Localized error message for display to user
  String _getLocalizedMessage(String errorCode) {
    switch (errorCode) {
      // Network errors (using StripePaymentHandler constants)
      case 'socket_error':
        return context.tr('common.no_internet');
      case 'timeout_error':
        return context.tr('common.request_timeout');

      // Generic errors
      case 'SERVER_ERROR':
        return context.tr('common.server_error');

      default:
        // Fallback for unknown error codes or specific error messages
        if (errorCode.contains('The payment flow has been canceled')) {
          return context.tr('home.donation_page.payment_cancelled');
        } else if (errorCode.contains('No internet') ||
            errorCode.contains('socket')) {
          return context.tr('common.no_internet');
        } else if (errorCode.contains('timed out') ||
            errorCode.contains('timeout')) {
          return context.tr('common.request_timeout');
        } else if (errorCode.contains('Failed to')) {
          return context.tr('common.server_error');
        } else {
          return context.tr('common.unknown_error');
        }
    }
  }

  /// Initializes the currency system on page load
  ///
  /// This method handles the complete currency setup flow:
  /// 1. Gets user's preferred currency (saved preference or IP detection)
  /// 2. Gets localized donation amounts for selected currency
  /// 3. Handles any errors gracefully with user feedback
  ///
  /// Called during initState() to set up currency state before UI renders.
  Future<void> _initializeCurrency() async {
    setState(() {
      _isLoadingCurrency = true;
      _currencyError = null;
    });

    // Get user's preferred currency (either saved or detected via IP)
    final currency = await CurrencyService.getUserCurrency();
    if (currency != null) {
      _onCurrencyChanged(currency);
    }

    setState(() {
      _isLoadingCurrency = false;
    });
  }

  void _showCurrencyError() {
    if (_currencyError != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_currencyError!),
          backgroundColor: Colors.orange, // Warning color, not critical
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: context.tr('common.retry'),
            textColor: Colors.white,
            onPressed: () {
              // Retry currency initialization
              _initializeCurrency();
            },
          ),
        ),
      );
    }
  }

  /// Handles currency change when user selects a different currency
  ///
  /// This method updates the donation amounts to use localized amounts
  /// for the selected currency. If the currency is not supported,
  /// it keeps the current currency and shows an error message.
  ///
  /// [newCurrency] - The new currency code selected by the user
  Future<void> _onCurrencyChanged(String newCurrency) async {
    if (newCurrency == _selectedCurrency) return;

    FirebaseCrashlytics.instance.log(
        'donation_flow: Currency change started - current ${_selectedCurrency}');

    setState(() {
      _isLoadingCurrency = true;
      _currencyError = null;
    });

    // Check if the currency is supported (Just in case)
    if (DonationAmounts.isCurrencySupported(newCurrency)) {
      // Get localized donation amounts for this currency
      final amounts = DonationAmounts.getAmounts(newCurrency)!;
      final symbol = DonationAmounts.getSymbol(newCurrency);
      final isRTL = DonationAmounts.isRTL(newCurrency);

      setState(() {
        _selectedCurrency = newCurrency;
        _currencySymbol = symbol;
        _isRTLCurrency = isRTL;
        _donationAmounts = amounts;
        _isLoadingCurrency = false;

        // Reset custom amount if it was selected (since amounts changed)
        if (selectedAmount != null &&
            !_donationAmounts.contains(selectedAmount)) {
          selectedAmount = null;
        }
      });

      FirebaseCrashlytics.instance.log(
          'donation_flow: Currency changed successfully to ${newCurrency}');

      // Save the new currency preference
      await CurrencyService.saveUserCurrency(newCurrency);
    } else {
      // Fail silently since this should not happen given that the choices are from the same service
      setState(() {
        _isLoadingCurrency = false;
      });
      FirebaseCrashlytics.instance
          .log('donation_flow: Unsupported currency selected: $newCurrency');
    }
  }

  /// Shows a bottom sheet for currency selection
  ///
  /// Displays all supported currencies in a mobile-friendly bottom sheet format.
  /// Uses the device's theme colors and provides smooth scrolling for long lists.
  ///
  /// Returns: Future<void> - Automatically calls _onCurrencyChanged if user selects
  Future<void> _showCurrencyDialog() async {
    final isDark = context.isDarkMode;

    // Get all supported currencies from the service
    final currencies = DonationAmounts.getSupportedCurrencies();

    // Show modal bottom sheet with currency options
    final selectedCurrency = await showModalBottomSheet<String>(
      context: context,
      // Enable drag to dismiss
      isDismissible: true,
      // Allow dragging from anywhere on the sheet
      enableDrag: true,
      // Rounded corners at the top
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      // Use theme-appropriate background color
      backgroundColor: ColorResource.backgroundDefault(isDark),
      builder: (BuildContext context) {
        return Container(
          // Constrain height to 60% of screen for better UX
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Drag handle indicator
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: ColorResource.textGrey(isDark).withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header with title
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  context.tr('home.donation_page.select_currency'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: ColorResource.textDefault(isDark),
                  ),
                ),
              ),

              // Divider
              Divider(
                height: 1,
                color: ColorResource.border(isDark),
              ),

              // Scrollable currency list
              Flexible(
                child: ListView.builder(
                  // Allow list to shrink if currencies fit on screen
                  shrinkWrap: true,
                  // Number of currency options available
                  itemCount: currencies.length,
                  itemBuilder: (context, index) {
                    final currency = currencies[index];

                    // Check if this currency is currently selected
                    final isSelected = currency == _selectedCurrency;

                    return ListTile(
                      // Currency code as main text
                      title: Text(
                        currency,
                        style: TextStyle(
                          color: ColorResource.textDefault(isDark),
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      // Show checkmark for selected currency
                      trailing: isSelected
                          ? Icon(
                              Icons.check,
                              color: ColorResource.primary(isDark),
                            )
                          : null,
                      // Handle currency selection
                      onTap: () {
                        // Close bottom sheet and return selected currency
                        Navigator.pop(context, currency);
                      },
                    );
                  },
                ),
              ),

              // Bottom safe area padding for devices with home indicators
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ],
          ),
        );
      },
    );

    // If user selected a currency (didn't just dismiss), change to it
    if (selectedCurrency != null && selectedCurrency != _selectedCurrency) {
      await _onCurrencyChanged(selectedCurrency);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = context.isDarkMode;
    if (_showSubscriptionStatus && _subscriptionInfo != null) {
      return SubscriptionStatusView(
        key: ValueKey(
            _subscriptionInfo), // Add key to force rebuild when data changes
        subscriptionInfo: _subscriptionInfo!,
        onMakeOneTimeDonation: () {
          setState(() {
            _showSubscriptionStatus = false;
            // Ensure monthly payment is false when coming from subscription status
            if (_hasActiveSubscription) {
              monthlyPayment = false;
            }
          });
        },
        onCancelSubscription: () {
          // Handle the cancellation by refreshing subscription data
          setState(() {
            // Mark subscription as cancelling
            _subscriptionInfo!['cancel_at_period_end'] = true;
            // Set monthly payment to true so user can create new subscription
            monthlyPayment = true;
          });
        },
        onSubscriptionUpdated: () async {
          // Refresh subscription info when it's updated
          await _checkSubscription();
        },
      );
    }

    return SafeArea(
      child: Container(
        color: ColorResource.backgroundDefault(isDark),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 20),
                _buildTitle(isDark),
                const SizedBox(height: 16),
                _buildSubtitle(isDark),
                const SizedBox(height: 20),
                _buildPaymentHistory(isDark),
                const SizedBox(height: 16),
                _buildAmountButtons(isDark),
                const SizedBox(height: 16),
                _buildCurrencySelector(isDark),
                const SizedBox(height: 16),
                _buildEmailField(isDark),
                const SizedBox(height: 16),
                _buildMessageField(isDark),
                const SizedBox(height: 24),
                // Only show the monthly payment toggle if no active subscription
                if (!_hasActiveSubscription)
                  _buildMonthlyPaymentToggle(isDark)
                else
                  const SizedBox(height: 10),
                // Show link to change email or link existing donations
                _buildEmailActionLink(isDark),
                const SizedBox(height: 10),
                _buildDonateButton(isDark),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(bool isDark) {
    return Row(
      children: [
        // If the user is subscribed, the subscription status will be the default view
        if (_subscriptionInfo != null)
          IconButton(
            icon: Icon(Icons.arrow_back,
                color: ColorResource.textDefault(isDark)),
            onPressed: () {
              setState(() {
                _showSubscriptionStatus = true;
              });
              _checkSubscription();
            },
          )
        else
          const SizedBox(width: 48), // Reserve space for alignment
        Expanded(
          child: Center(
            child: Text(
              context.tr('home.donation_page.title'),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: ColorResource.textDefault(isDark),
              ),
            ),
          ),
        ),
        const SizedBox(width: 48), // Reserve space for symmetry
      ],
    );
  }

  Widget _buildSubtitle(bool isDark) {
    return Center(
      child: Text(
        context.tr('home.donation_page.subtitle'),
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 16,
          color: ColorResource.textGrey(isDark),
        ),
      ),
    );
  }

  Widget _buildPaymentHistory(bool isDark) {
    // If loading history, show a loading indicator
    if (_loadingHistory) {
      return Center(
        child: CircularProgressIndicator(
          color: ColorResource.primary(isDark),
        ),
      );
    }

    // If there's an error loading history, show the error message and a retry button
    if (_loadingError != null) {
      return Container(
        decoration: BoxDecoration(
          color: ColorResource.backgroundWhite(isDark),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: ColorResource.border(isDark)),
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: ColorResource.danger(isDark),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _loadingError!,
                style: TextStyle(
                  color: ColorResource.textDefault(isDark),
                  fontSize: 14,
                ),
              ),
            ),
            TextButton(
              onPressed: _loadingHistory
                  ? null
                  : () {
                      setState(() {
                        _loadingHistory = true;
                        _loadingError = null;
                      });
                      _loadPaymentHistory();
                    },
              child: _loadingHistory
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: ColorResource.primary(isDark),
                      ),
                    )
                  : Text(
                      context.tr('common.retry'),
                      style: TextStyle(
                        color: ColorResource.primary(isDark),
                      ),
                    ),
            ),
          ],
        ),
      );
    }

    // If customer id is present but no payment history, show nothing
    if (_paymentHistory == null || _paymentHistory!.isEmpty) {
      return const SizedBox.shrink();
    }

    String firstCurrency =
        _paymentHistory!.first['currency'].toString().toUpperCase();
    bool mixedCurrencies = _paymentHistory!.any((payment) =>
        payment['currency'].toString().toUpperCase() != firstCurrency);

    // Calculate total donations if there are no mixed currencies
    double total = 0.0;
    if (!mixedCurrencies) {
      total = _paymentHistory!.fold(
        0,
        (sum, payment) => sum + (payment['amount'] as double),
      );
    }

    // Build the payment history list
    return Container(
      decoration: BoxDecoration(
        color: ColorResource.backgroundWhite(isDark),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ColorResource.border(isDark)),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent, // Removes the lines when expanded
        ),
        child: ExpansionTile(
          title: Text(
            context.tr('home.donation_page.your_donations'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: ColorResource.textDefault(isDark),
            ),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _paymentHistory!.length,
                separatorBuilder: (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Divider(
                    color: ColorResource.border(isDark),
                    height: 1,
                  ),
                ),
                itemBuilder: (context, index) {
                  final payment = _paymentHistory![index];
                  final date = DateTime.fromMillisecondsSinceEpoch(
                      payment['created'] * 1000);
                  final formattedDate = DateFormat('MMM d, yyyy').format(date);
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          formattedDate,
                          style: TextStyle(
                            color: ColorResource.textGrey(isDark),
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          '${payment['currency'].toString().toUpperCase()} ${payment['amount'].toStringAsFixed(2)}',
                          style: TextStyle(
                            color: ColorResource.textDefault(isDark),
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.tr('home.donation_page.total_impact'),
                    style: TextStyle(
                      color: ColorResource.textDefault(isDark),
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    mixedCurrencies
                        ? context.tr('home.donation_page.mixed_currencies')
                        : '$firstCurrency ${total.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: ColorResource.textDefault(isDark),
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountButtons(bool isDark) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildAmountButton(_donationAmounts[0], isDark)),
            const SizedBox(width: 16),
            Expanded(child: _buildAmountButton(_donationAmounts[1], isDark)),
            const SizedBox(width: 16),
            Expanded(child: _buildAmountButton(_donationAmounts[2], isDark)),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildAmountButton(_donationAmounts[3], isDark)),
            const SizedBox(width: 16),
            Expanded(child: _buildAmountButton(_donationAmounts[4], isDark)),
            const SizedBox(width: 16),
            Expanded(child: _buildMoreButton(isDark)),
          ],
        ),
      ],
    );
  }

  Widget _buildAmountButton(int amount, bool isDark) {
    final bool isSelected = selectedAmount == amount;

    // if loading currency, show loading indicator instead of amount
    return Expanded(
      child: InkWell(
        onTap: _isLoadingCurrency
            ? null
            : () {
                setState(() {
                  selectedAmount = amount;
                });
              },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: isSelected
                ? ColorResource.chipBackgroundSelected(isDark)
                : ColorResource.backgroundWhite(isDark),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: isSelected
                  ? Colors.transparent
                  : ColorResource.border(isDark),
              width: 1,
            ),
          ),
          child: Center(
            child: _isLoadingCurrency
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: ColorResource.textGrey(isDark),
                    ),
                  )
                : Text(
                    _isRTLCurrency
                        ? '$amount $_currencySymbol'
                        : '$_currencySymbol$amount',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isSelected
                          ? ColorResource.chipTextSelected(isDark)
                          : ColorResource.textDefault(isDark),
                    ),
                    textDirection: _isRTLCurrency
                        ? ui.TextDirection.rtl
                        : ui.TextDirection.ltr,
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildMoreButton(bool isDark) {
    final bool isCustomSelected =
        selectedAmount != null && !_donationAmounts.contains(selectedAmount);

    return Expanded(
      child: InkWell(
        onTap: () => showCustomAmountDialog(
            context: context,
            currency: _currencySymbol,
            onAmountSelected: (amount) {
              setState(() {
                selectedAmount = amount;
              });
              FirebaseCrashlytics.instance
                  .log('donation_flow: Custom amount selected - \$${amount}');
            }),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: isCustomSelected
                ? ColorResource.chipBackgroundSelected(isDark)
                : ColorResource.backgroundWhite(isDark),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: isCustomSelected
                  ? Colors.transparent
                  : ColorResource.border(isDark),
              width: 1,
            ),
          ),
          child: Center(
            child: Text(
              isCustomSelected
                  ? (_isRTLCurrency
                      ? '$selectedAmount $_currencySymbol'
                      : '$_currencySymbol$selectedAmount')
                  : '...',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isCustomSelected
                    ? ColorResource.chipTextSelected(isDark)
                    : ColorResource.textDefault(isDark),
              ),
              textDirection: isCustomSelected && _isRTLCurrency
                  ? ui.TextDirection.rtl
                  : ui.TextDirection.ltr,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCurrencySelector(bool isDark) {
    return Center(
      child: GestureDetector(
        onTap: () => _showCurrencyDialog(),
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: ColorResource.textGrey(isDark),
                width: 1.0,
              ),
            ),
          ),
          child: Text(
            '${context.tr("home.donation_page.amounts_in", namedArgs: {
                  "currency": _selectedCurrency
                })} · ${context.tr("common.change")}',
            style: TextStyle(
              color: ColorResource.textGrey(isDark),
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMessageField(bool isDark) {
    return TextField(
      controller: messageController,
      decoration: InputDecoration(
        labelText: context.tr('home.donation_page.message'),
        labelStyle: TextStyle(color: ColorResource.textGrey(isDark)),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: ColorResource.border(isDark)),
        ),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: ColorResource.primary(isDark)),
        ),
      ),
      style: TextStyle(color: ColorResource.textDefault(isDark)),
      maxLines: 1,
      cursorColor: ColorResource.textDefault(isDark),
    );
  }

  Widget _buildEmailField(bool isDark) {
    return TextField(
      controller: emailController,
      keyboardType: TextInputType.emailAddress,
      readOnly: _hasCustomerId,
      decoration: InputDecoration(
        labelText: context.tr('common.email'),
        labelStyle: TextStyle(color: ColorResource.textGrey(isDark)),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: ColorResource.border(isDark)),
        ),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: ColorResource.primary(isDark)),
        ),
        suffixIcon: _hasCustomerId
            ? Icon(Icons.lock_outline, color: ColorResource.textGrey(isDark))
            : null,
      ),
      style: TextStyle(
          color: _hasCustomerId
              ? ColorResource.textGrey(isDark)
              : ColorResource.textDefault(isDark)),
      maxLines: 1,
      cursorColor: ColorResource.textDefault(isDark),
      autofillHints: const [AutofillHints.email],
    );
  }

  Widget _buildEmailActionLink(bool isDark) {
    return Center(
      child: GestureDetector(
        onTap: () async {
          // Show email input dialog first
          String? email = await showEmailInputDialog(
            context: context,
            isChangeEmail: _hasCustomerId,
          );
          if (email == null) return; // User cancelled

          // Then show email verification dialog
          String? customerId = await showEmailVerificationDialog(
            context: context,
            email: email,
            paymentHandler: _paymentHandler,
          );

          if (customerId != null) {
            FirebaseCrashlytics.instance.log(
                'donation_flow: Email verification completed via action link');
            // Update email field and state
            emailController.text = email;
            await _saveEmail(email);
            setState(() {
              _hasCustomerId = true;
            });
            // Refresh payment history and subscription status
            await _loadPaymentHistory();
            await _checkSubscription();
          }
        },
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: ColorResource.primary(isDark),
                width: 1.0,
              ),
            ),
          ),
          child: Text(
            _hasCustomerId
                ? context.tr('home.donation_page.change_email')
                : context.tr('home.donation_page.donated_before'),
            style: TextStyle(
              color: ColorResource.primary(isDark),
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMonthlyPaymentToggle(bool isDark) {
    return Row(
      children: [
        Switch(
          value: monthlyPayment,
          onChanged: (value) {
            setState(() {
              monthlyPayment = value;
            });
            FirebaseCrashlytics.instance.log(
                'donation_flow: Monthly payment toggled - ${value ? "enabled" : "disabled"}');
          },
          activeColor: ColorResource.primary(isDark),
        ),
        const SizedBox(width: 8),
        Text(
          context.tr('home.donation_page.repeat_monthly'),
          style: TextStyle(
            fontSize: 16,
            color: ColorResource.textDefault(isDark),
          ),
        ),
      ],
    );
  }

  Widget _buildDonateButton(bool isDark) {
    return ElevatedButton(
      onPressed: _isProcessing
          ? null
          // TODO: factor this logic into a separate method
          : () async {
              if (selectedAmount == null) {
                FirebaseCrashlytics.instance
                    .log('donation_flow: Error - No amount selected');
                _showErrorMessage(
                    context.tr('home.donation_page.select_amount'));
                return;
              }
              final email = emailController.text.trim();
              final emailRegex = RegExp(r"^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$");
              if (email.isEmpty || !emailRegex.hasMatch(email)) {
                FirebaseCrashlytics.instance
                    .log('donation_flow: Error - Invalid email format');
                _showErrorMessage(context.tr('common.enter_valid_email'));
                return;
              }

              FirebaseCrashlytics.instance
                  .log('donation_flow: Email validated successfully');

              await _saveEmail(email);

              setState(() {
                _isProcessing = true;
              });

              FirebaseCrashlytics.instance.log(
                  'donation_flow: Payment processing started - ${monthlyPayment ? "subscription" : "one-time"}');
              FirebaseCrashlytics.instance.log(
                  'donation_flow: Amount: \$${selectedAmount} ${_selectedCurrency}');

              // NOTE: Logic for getting or creating customer ID and refreshing the data is duplicated in email action flow
              // Get or create customer ID first
              String? customerId = await _getOrCreateCustomer(email);
              if (customerId == null) {
                FirebaseCrashlytics.instance
                    .log('donation_flow: Error - Customer ID creation failed');
                setState(() {
                  _isProcessing = false;
                });
                // Error message already shown by _getOrCreateCustomer
                return;
              }

              FirebaseCrashlytics.instance.log(
                  'donation_flow: Customer ID obtained successfully: $customerId');

              if (!_hasCustomerId) {
                // If the user has an active subscription, it'll be set to false
                // save its value to show an error if the user was trying to
                // subscribe while having a subscription
                final prevMonthlyPayment = monthlyPayment;

                // Update state to make email field read-only after verification
                setState(() {
                  _hasCustomerId = true;
                });
                // Load customer data after verification
                _loadPaymentHistory();
                await _checkSubscription();

                // Check if user is trying to create subscription when they already have one
                if (prevMonthlyPayment && _hasActiveSubscription) {
                  FirebaseCrashlytics.instance.log(
                      'donation_flow: Error - User already has active subscription');
                  setState(() {
                    _isProcessing = false;
                  });
                  _showErrorMessage(
                      context.tr('home.donation_page.already_subscribed'));
                  return;
                }
              }

              if (monthlyPayment) {
                FirebaseCrashlytics.instance
                    .log('donation_flow: Starting subscription creation');
                // Create subscription
                await _paymentHandler.subscribeUser(
                  customerId: customerId,
                  amount: selectedAmount!.toDouble(),
                  currency: _selectedCurrency.toLowerCase(),
                  context: context,
                  interval: 'month',
                  metadata: {
                    if (messageController.text.isNotEmpty)
                      'message': messageController.text,
                  },
                  onSuccess: (subscriptionInfo) {
                    FirebaseCrashlytics.instance.log(
                        'donation_flow: Subscription created successfully');
                    setState(() {
                      _isProcessing = false;
                      _subscriptionInfo = subscriptionInfo;
                      _showSubscriptionStatus = true;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(context
                            .tr('home.donation_page.success_subscription')),
                        backgroundColor: ColorResource.primary(isDark),
                      ),
                    );
                  },
                  onError: (errorCode) {
                    FirebaseCrashlytics.instance.log(
                        'donation_flow: Subscription creation failed - ${errorCode}');
                    setState(() {
                      _isProcessing = false;
                    });
                    _showErrorMessage(_getLocalizedMessage(errorCode));
                    FirebaseCrashlytics.instance.recordError(
                        Exception('Subscription failed: $errorCode'),
                        StackTrace.current,
                        fatal: false);
                    print('Subscription failed: $errorCode');
                  },
                );
              } else {
                FirebaseCrashlytics.instance
                    .log('donation_flow: Starting one-time payment processing');
                // Process one-time payment
                await _paymentHandler.processPayment(
                  amount: selectedAmount!.toDouble(),
                  currency: _selectedCurrency.toLowerCase(),
                  customerId: customerId,
                  context: context,
                  description: messageController.text,
                  onSuccess: () {
                    FirebaseCrashlytics.instance.log(
                        'donation_flow: One-time payment completed successfully');
                    setState(() {
                      _isProcessing = false;
                    });
                    _loadPaymentHistory();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                            context.tr('home.donation_page.success_payment')),
                        backgroundColor: ColorResource.primary(isDark),
                      ),
                    );
                  },
                  onError: (errorCode) {
                    FirebaseCrashlytics.instance.log(
                        'donation_flow: One-time payment failed - ${errorCode}');
                    setState(() {
                      _isProcessing = false;
                    });
                    _showErrorMessage(_getLocalizedMessage(errorCode));
                    FirebaseCrashlytics.instance.recordError(
                        Exception('Payment error: $errorCode'),
                        StackTrace.current,
                        fatal: false);
                    print('Payment error: $errorCode');
                  },
                );
              }
            },
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorResource.primary(isDark),
        foregroundColor: ColorResource.onPrimary(isDark),
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      child: _isProcessing
          ? SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                color: ColorResource.onPrimary(isDark),
                strokeWidth: 2.5,
              ),
            )
          : Text(
              context.tr('home.donation_page.donate'),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
    );
  }

  /// Shows error message to user via SnackBar
  void _showErrorMessage(String message) {
    final isDark = context.isDarkMode;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: ColorResource.danger(isDark),
      ),
    );
  }
}
