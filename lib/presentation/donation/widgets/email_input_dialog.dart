import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/extension/context_extension.dart';

/// Dialog widget for inputting email address
/// 
/// This dialog provides a clean interface for users to enter their email address
/// when they want to change their linked email or link existing donations.
/// 
/// Features:
/// - Auto-focused email input field
/// - Real-time email validation
/// - Prevents closing with invalid email
/// - Consistent styling with app theme
/// - Localized text and labels
/// 
/// Usage:
/// ```dart
/// String? email = await showEmailInputDialog(
///   context: context,
///   isChangeEmail: true, // or false for "donated before" flow
/// );
/// if (email != null) {
///   // Proceed with email verification
/// }
/// ```
class EmailInputDialog extends StatefulWidget {
  /// Whether this is for changing email (true) or linking existing donations (false)
  final bool isChangeEmail;

  const EmailInputDialog({
    super.key,
    required this.isChangeEmail,
  });

  @override
  State<EmailInputDialog> createState() => _EmailInputDialogState();
}

class _EmailInputDialogState extends State<EmailInputDialog> {
  /// Controller for the email input field
  final TextEditingController _emailController = TextEditingController();
  
  /// Current validation error message
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  /// Validates the email format and updates error state
  void _validateEmail() {
    final email = _emailController.text.trim();
    final emailRegex = RegExp(r"^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$");
    
    setState(() {
      if (email.isEmpty) {
        _errorMessage = context.tr('common.enter_valid_email');
      } else if (!emailRegex.hasMatch(email)) {
        _errorMessage = context.tr('common.enter_valid_email');
      } else {
        _errorMessage = null;
      }
    });
  }

  /// Handles the confirm button press
  void _handleConfirm() {
    _validateEmail();
    if (_errorMessage == null) {
      Navigator.of(context).pop(_emailController.text.trim());
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = context.isDarkMode;
    
    return AlertDialog(
      backgroundColor: ColorResource.backgroundDefault(isDark),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Text(
        widget.isChangeEmail 
            ? context.tr('home.donation_page.change_email')
            : context.tr('home.donation_page.enter_email'),
        style: TextStyle(
          color: ColorResource.textDefault(isDark),
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description text
          Text(
            widget.isChangeEmail
                ? context.tr('home.donation_page.change_email_description')
                : context.tr('home.donation_page.link_donations_description'),
            style: TextStyle(
              color: ColorResource.textGrey(isDark),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          
          // Email input field
          TextField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            autofocus: true,
            onChanged: (_) => _validateEmail(),
            onSubmitted: (_) => _handleConfirm(),
            decoration: InputDecoration(
              labelText: context.tr('common.email'),
              labelStyle: TextStyle(color: ColorResource.textGrey(isDark)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ColorResource.border(isDark),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ColorResource.primary(isDark),
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ColorResource.danger(isDark),
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ColorResource.danger(isDark),
                  width: 2,
                ),
              ),
              errorText: _errorMessage,
            ),
            style: TextStyle(color: ColorResource.textDefault(isDark)),
          ),
        ],
      ),
      actions: [
        // Cancel button
        TextButton(
          onPressed: () => Navigator.of(context).pop(null),
          child: Text(
            context.tr('common.cancel'),
            style: TextStyle(color: ColorResource.textGrey(isDark)),
          ),
        ),
        
        // Confirm button
        ElevatedButton(
          onPressed: _errorMessage == null && _emailController.text.trim().isNotEmpty
              ? _handleConfirm
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorResource.primary(isDark),
            foregroundColor: ColorResource.onPrimary(isDark),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(context.tr('common.confirm')),
        ),
      ],
    );
  }
}

/// Shows the email input dialog and returns the entered email
/// 
/// This is the main entry point for collecting email addresses from users
/// when they want to change their linked email or link existing donations.
/// 
/// The dialog validates email format in real-time and prevents submission
/// of invalid emails. It provides appropriate messaging based on the context.
/// 
/// [context] - Build context for showing the dialog
/// [isChangeEmail] - Whether this is for changing email (true) or linking existing donations (false)
/// 
/// Returns:
/// - String: Valid email address if user confirms
/// - null: If user cancels or dismisses the dialog
Future<String?> showEmailInputDialog({
  required BuildContext context,
  required bool isChangeEmail,
}) async {
  return await showDialog<String>(
    context: context,
    barrierDismissible: false, // Prevent dismissing by tapping outside
    builder: (context) => EmailInputDialog(
      isChangeEmail: isChangeEmail,
    ),
  );
}