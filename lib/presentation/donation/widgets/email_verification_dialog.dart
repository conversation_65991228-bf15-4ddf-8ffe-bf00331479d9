import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/utils/stripe_payment.dart';

/// Email verification dialog widget that handles the complete email verification flow
/// 
/// This widget provides a complete email verification experience:
/// 1. Automatically requests verification code when shown
/// 2. Displays loading states during API calls
/// 3. Provides user-friendly error handling
/// 4. Validates 6-digit codes with real-time feedback
/// 5. Saves customer ID to preferences on successful verification
/// 6. Returns customer ID to caller for immediate use
/// 
/// The dialog flow:
/// - Shows loading while requesting verification code
/// - Displays code input field with auto-focus
/// - Validates code format (6 digits)
/// - Shows error messages with retry options
/// - Handles network errors gracefully
/// - Prevents multiple simultaneous requests
/// 
/// Usage:
/// ```dart
/// String? customerId = await showEmailVerificationDialog(
///   context: context,
///   email: '<EMAIL>',
///   paymentHandler: _paymentHandler,
/// );
/// if (customerId != null) {
///   // Use the customer ID for payment processing
/// }
/// ```
class EmailVerificationDialog extends StatefulWidget {
  /// The email address to verify
  final String email;
  
  /// Payment handler instance for making verification API calls
  final StripePaymentHandler paymentHandler;

  const EmailVerificationDialog({
    super.key,
    required this.email,
    required this.paymentHandler,
  });

  @override
  State<EmailVerificationDialog> createState() => _EmailVerificationDialogState();
}

class _EmailVerificationDialogState extends State<EmailVerificationDialog> {
  /// Controller for the 6-digit verification code input
  final TextEditingController _codeController = TextEditingController();
  
  /// Focus node to auto-focus the code input field
  final FocusNode _codeFocusNode = FocusNode();
  
  /// Token received from the verification request API call
  /// Used to complete the verification process
  String? _verificationToken;
  
  /// Loading state for the initial verification request
  bool _isRequestingVerification = false;
  
  /// Loading state for the code verification process
  bool _isVerifying = false;
  
  /// Current error message to display to the user
  String? _errorMessage;
  
  /// Flag to prevent multiple verification requests
  bool _hasRequestedVerification = false;

  @override
  void initState() {
    super.initState();
    FirebaseCrashlytics.instance.log('donation_flow: Email verification dialog opened');
    // Add listener to text controller to update button state
    _codeController.addListener(() {
      setState(() {
        // This will trigger rebuild when text changes to enable/disable button
      });
    });
    // Automatically request verification code when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _requestVerification();
    });
  }

  @override
  void dispose() {
    _codeController.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }

  /// Requests a verification code to be sent to the user's email
  /// 
  /// This method:
  /// - Prevents duplicate requests using [_hasRequestedVerification] flag
  /// - Shows loading state while API call is in progress
  /// - Stores verification token for later use in code verification
  /// - Auto-focuses the code input field on success
  /// - Displays localized error messages on failure
  /// 
  /// The verification token is required for the subsequent code verification call
  Future<void> _requestVerification() async {
    if (_hasRequestedVerification) return;
    
    FirebaseCrashlytics.instance.log('donation_flow: Requesting email verification code');
    setState(() {
      _isRequestingVerification = true;
      _errorMessage = null;
    });

    await widget.paymentHandler.requestVerification(
      email: widget.email,
      onSuccess: (token) {
        FirebaseCrashlytics.instance.log('donation_flow: Email verification code sent successfully');
        setState(() {
          _verificationToken = token;
          _isRequestingVerification = false;
          _hasRequestedVerification = true;
        });
        
        // Auto-focus the code input field for better UX
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _codeFocusNode.requestFocus();
        });
      },
      onError: (errorCode) {
        FirebaseCrashlytics.instance.log('donation_flow: Email verification code request failed - ${errorCode}');
        setState(() {
          _isRequestingVerification = false;
          _errorMessage = _getLocalizedMessage(errorCode);
        });
      },
    );
  }

  /// Verifies the entered 6-digit code with the backend
  /// 
  /// This method:
  /// - Validates code format (must be exactly 6 digits)
  /// - Checks that verification token is available
  /// - Shows loading state during API call
  /// - Saves customer ID to preferences on success
  /// - Closes dialog and returns customer ID to caller
  /// - Displays specific error messages for different failure scenarios
  /// 
  /// On successful verification:
  /// - Customer ID is saved to device preferences for future use
  /// - Dialog closes with customer ID as result
  /// - Caller can immediately use the customer ID for payments
  Future<void> _verifyCode() async {
    final code = _codeController.text.trim();
    FirebaseCrashlytics.instance.log('donation_flow: Email verification code entered - ${code.length} digits');
    if (code.length != 6) {
      FirebaseCrashlytics.instance.log('donation_flow: Email verification error - Invalid code length');
      setState(() {
        _errorMessage = context.tr('verification.code_must_be_6_digits');
      });
      return;
    }

    if (_verificationToken == null) {
      FirebaseCrashlytics.instance.log('donation_flow: Email verification error - No verification token');
      setState(() {
        _errorMessage = context.tr('verification.invalid_token');
      });
      return;
    }

    FirebaseCrashlytics.instance.log('donation_flow: Starting email verification with code');
    setState(() {
      _isVerifying = true;
      _errorMessage = null;
    });

    await widget.paymentHandler.completeVerification(
      verificationToken: _verificationToken!,
      code: code,
      email: widget.email,
      onSuccess: (customerId, wasCreated) async {
        FirebaseCrashlytics.instance.log('donation_flow: Email verification successful - Customer ${wasCreated ? "created" : "found"}');
        // Save customer ID for future use
        await PreferenceStorage.saveCustomerId(customerId);
        // Close dialog and return customer ID to caller
        if (mounted) {
          Navigator.of(context).pop(customerId);
        }
      },
      onError: (errorCode) {
        FirebaseCrashlytics.instance.log('donation_flow: Email verification failed - ${errorCode}');
        setState(() {
          _isVerifying = false;
          _errorMessage = _getLocalizedMessage(errorCode);
        });
      },
    );
  }

  /// Maps error codes from the payment handler to localized error messages
  /// 
  /// This method handles all possible error scenarios:
  /// - Email validation errors (invalid format)
  /// - Network errors (timeout, no internet)
  /// - Server errors (email send failed, token issues)
  /// - Verification errors (invalid code, expired token)
  /// - Customer creation errors
  /// 
  /// Falls back to generic error messages for unknown error codes
  /// and provides smart detection for common network issues
  /// 
  /// [errorCode] - Error code returned from StripePaymentHandler methods
  /// Returns: Localized error message suitable for display to user
  String _getLocalizedMessage(String errorCode) {
    switch (errorCode) {
      // Email validation errors
      case 'INVALID_EMAIL':
        return context.tr('verification.invalid_email');
      case 'EMAIL_SEND_FAILED':
        return context.tr('verification.email_send_failed');
      
      // Token-related errors
      case 'INVALID_TOKEN_TYPE':
        return context.tr('verification.invalid_token_type');
      case 'INVALID_TOKEN':
        return context.tr('verification.invalid_token');
      case 'TOKEN_EXPIRED':
        return context.tr('verification.token_expired');
      
      // Verification errors
      case 'EMAIL_MISMATCH':
        return context.tr('verification.email_mismatch');
      case 'INVALID_CODE':
        return context.tr('verification.invalid_code');
      
      // Customer errors
      case 'CUSTOMER_CREATION_FAILED':
        return context.tr('verification.customer_creation_failed');
      
      // Network errors
      case 'socket_error':
        return context.tr('common.no_internet');
      case 'timeout_error':
        return context.tr('common.request_timeout');
      case 'SERVER_ERROR':
        return context.tr('common.server_error');
      
      // Fallback with smart detection
      default:
        if (errorCode.contains('No internet') || errorCode.contains('socket')) {
          return context.tr('common.no_internet');
        } else if (errorCode.contains('timed out') || errorCode.contains('timeout')) {
          return context.tr('common.request_timeout');
        } else if (errorCode.contains('Failed to')) {
          return context.tr('common.server_error');
        } else {
          return context.tr('common.unknown_error');
        }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = context.isDarkMode;
    
    return AlertDialog(
      backgroundColor: ColorResource.backgroundDefault(isDark),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Text(
        context.tr('verification.email_verification'),
        style: TextStyle(
          color: ColorResource.textDefault(isDark),
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Email confirmation message
          Text(
            context.tr('verification.code_sent_to', namedArgs: {'email': widget.email}),
            style: TextStyle(
              color: ColorResource.textGrey(isDark),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          
          // Loading state for verification request
          if (_isRequestingVerification)
            Center(
              child: Column(
                children: [
                  CircularProgressIndicator(
                    color: ColorResource.primary(isDark),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    context.tr('verification.sending_code'),
                    style: TextStyle(
                      color: ColorResource.textGrey(isDark),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            )
          
          // Code input section (shown after verification request succeeds)
          else if (_hasRequestedVerification) ...[
            Text(
              context.tr('verification.enter_6_digit_code'),
              style: TextStyle(
                color: ColorResource.textDefault(isDark),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 12),
            
            // 6-digit code input field with special styling
            TextField(
              controller: _codeController,
              focusNode: _codeFocusNode,
              keyboardType: TextInputType.number,
              maxLength: 6,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: ColorResource.textDefault(isDark),
                fontSize: 18,
                fontWeight: FontWeight.bold,
                letterSpacing: 4, // Space out digits for better readability
              ),
              decoration: InputDecoration(
                counterText: '', // Hide character counter
                hintText: '000000',
                hintStyle: TextStyle(
                  color: ColorResource.textGrey(isDark).withOpacity(0.5),
                  letterSpacing: 4,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: ColorResource.border(isDark),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: ColorResource.primary(isDark),
                    width: 2,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: ColorResource.danger(isDark),
                  ),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: ColorResource.danger(isDark),
                    width: 2,
                  ),
                ),
              ),
              onSubmitted: (_) => _verifyCode(),
            ),
          ],
          
          // Error message display with icon and styling
          if (_errorMessage != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorResource.danger(isDark).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: ColorResource.danger(isDark).withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: ColorResource.danger(isDark),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: ColorResource.danger(isDark),
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
      actions: [
        // Cancel button (always available unless loading)
        TextButton(
          onPressed: _isRequestingVerification || _isVerifying 
              ? null 
              : () => Navigator.of(context).pop(null),
          child: Text(
            context.tr('common.cancel'),
            style: TextStyle(
              color: ColorResource.textGrey(isDark),
            ),
          ),
        ),
        
        // Resend code button (shown only after initial request fails)
        if (!_isRequestingVerification && !_hasRequestedVerification)
          TextButton(
            onPressed: _isVerifying ? null : _requestVerification,
            child: Text(
              context.tr('verification.resend_code'),
              style: TextStyle(
                color: ColorResource.primary(isDark),
              ),
            ),
          ),
        
        // Verify button (shown only after code input is available)
        if (_hasRequestedVerification)
          ElevatedButton(
            onPressed: _isVerifying || _codeController.text.length != 6 
                ? null 
                : _verifyCode,
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorResource.primary(isDark),
              foregroundColor: ColorResource.onPrimary(isDark),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isVerifying
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      color: ColorResource.onPrimary(isDark),
                      strokeWidth: 2,
                    ),
                  )
                : Text(context.tr('verification.verify')),
          ),
      ],
    );
  }
}

/// Shows the email verification dialog and handles the complete verification flow
/// 
/// This is the main entry point for email verification in the app. It:
/// - Creates and shows the verification dialog
/// - Handles the complete verification flow automatically
/// - Returns the customer ID on successful verification
/// - Returns null if the user cancels or verification fails
/// 
/// The dialog is modal and cannot be dismissed by tapping outside,
/// ensuring the user completes or explicitly cancels the verification process.
/// 
/// Usage Example:
/// ```dart
/// final customerId = await showEmailVerificationDialog(
///   context: context,
///   email: emailController.text.trim(),
///   paymentHandler: _paymentHandler,
/// );
/// 
/// if (customerId != null) {
///   // Verification successful - proceed with payment
///   await processPayment(customerId: customerId);
/// } else {
///   // User cancelled or verification failed
///   showErrorMessage('Verification cancelled or failed');
/// }
/// ```
/// 
/// [context] - Build context for showing the dialog
/// [email] - Email address to verify (must be valid email format)
/// [paymentHandler] - StripePaymentHandler instance for API calls
/// 
/// Returns:
/// - String: Customer ID if verification successful
/// - null: If user cancels or verification fails
Future<String?> showEmailVerificationDialog({
  required BuildContext context,
  required String email,
  required StripePaymentHandler paymentHandler,
}) async {
  return await showDialog<String>(
    context: context,
    barrierDismissible: false, // Prevent dismissing by tapping outside
    builder: (context) => EmailVerificationDialog(
      email: email,
      paymentHandler: paymentHandler,
    ),
  );
}