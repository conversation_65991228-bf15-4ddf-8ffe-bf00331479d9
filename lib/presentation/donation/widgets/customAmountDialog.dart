import 'dart:core';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/resource/color_resource.dart';

class CustomAmountDialog extends StatefulWidget {
  final String? title;
  final String? hintText;
  final String currency;
  final int? initialAmount;
  final Function(int) onAmountSelected;
  final String? confirmText;
  final String? cancelText;

  const CustomAmountDialog({
    Key? key,
    this.title,
    this.hintText,
    this.currency = '\$',
    this.initialAmount,
    required this.onAmountSelected,
    this.confirmText,
    this.cancelText,
  }) : super(key: key);

  @override
  State<CustomAmountDialog> createState() => _CustomAmountDialogState();
}

class _CustomAmountDialogState extends State<CustomAmountDialog> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.initialAmount?.toString() ?? '',
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = context.isDarkMode;

    return AlertDialog(
      backgroundColor: ColorResource.backgroundWhite(isDark),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: ColorResource.border(isDark), width: 1),
      ),
      title: Text(
        widget.title ?? context.tr('home.donation_page.enter_amount'),
        style: TextStyle(color: ColorResource.textDefault(isDark)),
      ),
      content: TextField(
        controller: _controller,
        keyboardType: TextInputType.number,
        autofocus: true,
        decoration: InputDecoration(
          prefixText: '${widget.currency} ',
          hintText:
              widget.hintText ?? context.tr('home.donation_page.enter_amount'),
          prefixStyle: TextStyle(color: ColorResource.textDefault(isDark)),
          hintStyle: TextStyle(color: ColorResource.textGrey(isDark)),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: ColorResource.border(isDark)),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: ColorResource.primary(isDark)),
          ),
        ),
        style: TextStyle(color: ColorResource.textDefault(isDark)),
        cursorColor: ColorResource.textDefault(isDark),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(
            widget.cancelText ?? context.tr('common.cancel'),
            style: TextStyle(color: ColorResource.textDefault(isDark)),
          ),
        ),
        TextButton(
          onPressed: () {
            final String amountText = _controller.text.trim();
            if (amountText.isNotEmpty) {
              final int? parsedAmount = int.tryParse(amountText);
              if (parsedAmount != null && parsedAmount > 0) {
                widget.onAmountSelected(parsedAmount);
              }
            }
            Navigator.pop(context);
          },
          child: Text(
            widget.confirmText ?? context.tr('common.confirm'),
            style: TextStyle(color: ColorResource.primary(isDark)),
          ),
        ),
      ],
    );
  }
}

// Helper function to show the dialog
Future<void> showCustomAmountDialog({
  required BuildContext context,
  String? title,
  String? hintText,
  String currency = '\$',
  int? initialAmount,
  required Function(int) onAmountSelected,
  String? confirmText,
  String? cancelText,
}) {
  return showDialog(
    context: context,
    builder: (context) => CustomAmountDialog(
      title: title,
      hintText: hintText,
      currency: currency,
      initialAmount: initialAmount,
      onAmountSelected: onAmountSelected,
      confirmText: confirmText,
      cancelText: cancelText,
    ),
  );
}
