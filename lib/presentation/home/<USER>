import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/settings/settings_screen.dart';
import 'package:mushafi/presentation/voice_search/voice_search_screen.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/app_update_helper.dart';
import 'package:mushafi/widget/home/<USER>';

class HomePage extends ConsumerStatefulWidget {
  final Function(int pageNumber) onPageSelected;
  final Function({bool stopRecitation}) onNavigateBack;

  const HomePage(this.onPageSelected, this.onNavigateBack, {super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage>
    with WidgetsBindingObserver {
  bool appUpdateAvailable = false;
  bool modelUpdateAvailable = false;
  bool modelCompatible = true;
  bool incompatibleCardShown = false;

  Future<void> onVoiceSearchRequested() async {
    final route = MaterialPageRoute(
      builder: (context) => const VoiceSearchScreen(),
    );
    final verse = await Navigator.of(context).push(route) as QuranVerse?;

    widget.onNavigateBack(stopRecitation: false);

    if (context.mounted && verse != null) {
      ref.read(highlightedVerseKeyProvider.notifier).highlight(verse.verseKey);
      await widget.onPageSelected(verse.pageId);
    }
  }

  Future<void> checkAppUpdate() async {
    AppUpdateHelper.isUpdateAvailable().then((available) {
      setState(() {
        appUpdateAvailable = available;
      });
    });
  }

  Future<void> checkModelUpdate() async {
    final latestModelVersion = await ref
        .read(minioDataRepositoryProvider)
        .updateAndGetLatestModelVersion();
    final currentModelVersion = PreferenceStorage.getLatestTestedModelVersion();
    if (mounted) {
      setState(() {
        modelUpdateAvailable = (latestModelVersion != null)
            ? latestModelVersion != currentModelVersion
            : false;
      });
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);

    modelCompatible = PreferenceStorage.getModelCompatible();
    incompatibleCardShown = PreferenceStorage.getShowIncompatibleCard();

    checkAppUpdate();
    checkModelUpdate();

    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (mounted) {
        checkAppUpdate();
        checkModelUpdate();
      }
    }

    super.didChangeAppLifecycleState(state);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        color: ColorResource.backgroundDefault(context.isDarkMode),
        child: Column(
          children: [
            buildHeader(),
            if (!modelCompatible && incompatibleCardShown)
              buildModelIncompatibleCard(),
            Expanded(
              child: BrowseMushaf((pageNumber, {String? verseKey}) async {
                if (verseKey != null) {
                  ref.read(highlightedVerseKeyProvider.notifier).highlight(verseKey, true);
                }
                await widget.onPageSelected(pageNumber);
              }, () {
                onVoiceSearchRequested();
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildSearchBar() {
    return Expanded(
      child: InkWell(
        onTap: () {
          onVoiceSearchRequested();
        },
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
                color: ColorResource.border(context.isDarkMode), width: 1.0),
            borderRadius: BorderRadius.circular(12.0),
            color: ColorResource.backgroundWhite(context.isDarkMode),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    context.tr('home.search'),
                    style: TextStyle(
                        color: ColorResource.textGrey(context.isDarkMode)),
                  ),
                  const Spacer(),
                  SvgPicture.asset(Assets.svgsIcSearch),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildMushafiBar() {
    return Expanded(
      child: Center(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              context.isDarkMode
                  ? Assets.svgsIcMushafiLogoDarkMode
                  : Assets.svgsIcMushafiLogo,
              width: 24,
              height: 24,
            ),
            const SizedBox(width: 8),
            Text(
              context.tr('home.app_title'),
              style: TextStyle(
                fontSize: 24,
                color: ColorResource.textDefault(context.isDarkMode),
                fontWeight: FontWeight.w600,
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildHeader() {
    return Padding(
      padding: EdgeInsets.only(
        left: (context.isArabic) ? 0 : 16.0,
        top: 16.0,
        bottom: 16.0,
        right: (context.isArabic) ? 16.0 : 0,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (modelCompatible) buildSearchBar() else buildMushafiBar(),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
            icon: Stack(
              children: [
                Icon(
                  Icons.settings_outlined,
                  color: ColorResource.textGrey(context.isDarkMode),
                ),
                if (appUpdateAvailable || modelUpdateAvailable)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildModelIncompatibleCard() {
    return Padding(
      padding: const EdgeInsets.only(left: 16.0, right: 16, bottom: 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Container(
          color: ColorResource.backgroundWhite(context.isDarkMode),
          child: IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.tr('home.ai_incompatible.title'),
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color:
                                ColorResource.textDefault(context.isDarkMode),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          context.tr('home.ai_incompatible.description'),
                          textAlign: TextAlign.justify,
                          style: TextStyle(
                            fontSize: 12,
                            color: ColorResource.textGrey(context.isDarkMode),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.rotationY((context.isArabic) ? pi : 0),
                      child: SvgPicture.asset(Assets.svgsImgPersonFlat),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    setState(() {
                      PreferenceStorage.saveShowIncompatibleCard(false);
                      incompatibleCardShown = false;
                    });
                  },
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: 8.0,
                      right: (context.isArabic) ? 0 : 8.0,
                      left: (context.isArabic) ? 8.0 : 0,
                    ),
                    child: SvgPicture.asset(Assets.svgsIcCloseCircle),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
