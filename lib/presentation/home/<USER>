import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/minio_data_manager.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/activity/activity_page.dart';
import 'package:mushafi/presentation/donation/donation_page.dart';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/ai/asr_model_type.dart';
import 'package:mushafi/presentation/model/ai/cond_model_type.dart';
import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/presentation/model/nullable.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/quran_mode.dart';
import 'package:mushafi/presentation/model/quran/quran_widget_state.dart';
import 'package:mushafi/presentation/model/recitation/word_play_state.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/quran/quran_screen.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:mushafi/utils/quran_text_cache_utils.dart';
import 'package:mushafi/utils/recitation_player.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:uuid/uuid.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  final ValueNotifier<bool> donationPageVisible = ValueNotifier<bool>(false);
  
  late final List<Widget> pages = [
    HomePage(onPageSelected, onNavigateBack),
    ActivityPage(onActivitySelected),
    DonationPage(visibilityNotifier: donationPageVisible),
  ];

  int selectedSectionIndex = 0;

  StreamSubscription<List<ConnectivityResult>>? connectivitySubscription;

  void initInferencePipeline() async {
    final modelVersion = PreferenceStorage.getLatestTestedModelVersion();
    if (modelVersion == null) return;

    final model = InferenceModel.fromType(
      AsrModelType.quantized,
      CondModelType.quantized,
      PreferenceStorage.getSelectedModel(),
      modelVersion,
    );

    if (mounted) {
      final modelsExist =
          await ref.read(minioDataRepositoryProvider).doModelsExist(model);
      if (modelsExist) {
        final success = await InferencePipeline.getInstance().initialize(model);
        if (!success) {
          CommonUtils.showToast('Failed to initialize inference pipeline');
        }
      }
    }
  }

  void stopInference() {
    final inferencePipeline = InferencePipeline.getInstance();
    inferencePipeline.reset();
    inferencePipeline.stop();
  }

  void onNavigateBack(
      {bool activitySelected = false, bool stopRecitation = true}) {
    FirebaseAnalytics.instance.logScreenView(screenName: "home");

    final inferencePipeline = InferencePipeline.getInstance();
    inferencePipeline.reset();
    inferencePipeline.stop();

    resetData(activitySelected, stopRecitation);
  }

  void resetData(bool activitySelected, bool stopRecitation) {
    ref.read(audioSegmentDataInitProvider.notifier).fetchAudioSegmentData();
    if (!activitySelected) {
      ref
          .read(quranImageAssetProvider.notifier)
          .updateMushafDesign(MushafDesign.getDefault(context.isDarkMode));
    }
    ref.read(quranModeProvider.notifier).state = DefaultQuranMode();
    ref.read(selectedTrackProvider.notifier).state = null;
    ref.read(pageNavigationProvider.notifier).reset();
    ref.read(highlightedVerseKeyProvider.notifier).highlight(null);
    ref.read(highlightedWordProvider.notifier).state = null;
    ref.read(transcriptionWordPlayStateProvider.notifier).state =
        WordPlayState.idle;
    ref.read(quranWidgetStateProvider.notifier).state = const QuranWidgetState(
      showTopBar: false,
      showBottomBar: false,
    );
    ref.read(undoAlignmentSnackbarProvider.notifier).show(null);
    ref.read(showAlignmentEndSnackbarProvider.notifier).state = false;
    ref.read(transcriptionProvider.notifier).state = '';
    ref.read(translationSheetHeightProvider.notifier).reset();
    ref.read(recitationPendingProvider.notifier).state = null;
    ref.read(recitationPlayingProvider.notifier).state = false;

    if (stopRecitation) {
      RecitationPlayer.stop().then((_) {
        ref.read(recitationPendingProvider.notifier).state = null;
        ref.read(recitationPlayingProvider.notifier).state = false;
        ref.read(highlightedVerseKeyProvider.notifier).highlight(null);
        ref.read(highlightedWordProvider.notifier).state = null;
      });
    }
  }

  Future<int> getTrackPageNumber(WidgetRef ref, Track track) async {
    String? lastHighlightedVerseKey;
    if (track.type == TrackType.readingHabitual ||
        track.type == TrackType.readingCoverToCover ||
        track.type == TrackType.listening) {
      lastHighlightedVerseKey = track.lastHighlightedVerseKey;
    } else if (track.type == TrackType.memorizing ||
        track.type == TrackType.readingWithAi) {
      final resultList = await ref
          .read(alignmentRepositoryProvider)
          .getAlignmentWordResults(track.id);
      lastHighlightedVerseKey = resultList.lastOrNull?.verseKey;
    }

    if (lastHighlightedVerseKey != null) {
      final verse = await ref
          .read(quranRepositoryProvider)
          .getQuranVerseByVerseKey(lastHighlightedVerseKey);

      if (verse != null) {
        return verse.pageId;
      }
    }

    return track.range.startPageNumber;
  }

  Future<void> onActivitySelected(Track track) async {
    final updatedTrack = track.copyWith(
        openingTimestamp: Nullable(DateTime.now().millisecondsSinceEpoch));
    final pageNumber = await getTrackPageNumber(ref, updatedTrack);

    if (mounted) {
      ref.read(trackRepositoryProvider).updateTrack(updatedTrack);
      ref.read(quranImageAssetProvider.notifier).updateMushafDesign(
            updatedTrack.design ?? MushafDesign.getDefault(context.isDarkMode),
          );
      ref.read(pageNavigationProvider.notifier).navigateToPage(pageNumber);
      ref.read(selectedTrackProvider.notifier).state = updatedTrack;

      final route = MaterialPageRoute(
        builder: (context) => const QuranScreen(),
      );
      final newTrack = await Navigator.of(context).push(route) as Track?;

      await route.completed;
      onNavigateBack(activitySelected: newTrack != null);

      if (newTrack != null) {
        await onActivitySelected(newTrack);
      }
    }
  }

  Future<void> onPageSelected(int pageNumber) async {
    ref.read(pageNavigationProvider.notifier).navigateToPage(pageNumber);

    if (context.mounted) {
      final route = MaterialPageRoute(
        builder: (context) => const QuranScreen(),
      );
      final newTrack = await Navigator.of(context).push(route) as Track?;

      await route.completed;
      onNavigateBack();

      if (newTrack != null) {
        await onActivitySelected(newTrack);
      }
    }
  }

  @override
  void initState() {
    FirebaseAnalytics.instance.logAppOpen();
    FirebaseAnalytics.instance.logScreenView(screenName: "home");

    WidgetsBinding.instance.addObserver(this);

    connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> result) async {
      final newConnected = result.contains(ConnectivityResult.mobile) ||
          result.contains(ConnectivityResult.wifi);
      if (newConnected) {
        await MinioDataManager.resolveDNS();

        final mushafDataInit = ref.read(mushafDataInitProvider);
        if (!mushafDataInit.hasValue || mushafDataInit.isLoading) {
          ref.invalidate(mushafDataInitProvider);
        }

        final audioSegmentDataInit = ref.read(audioSegmentDataInitProvider);
        if (!audioSegmentDataInit.hasValue || audioSegmentDataInit.isLoading) {
          ref.invalidate(audioSegmentDataInitProvider);
        }

        final translationTafsirDataInit =
            ref.read(translationTafsirDataInitProvider);
        if (!translationTafsirDataInit.hasValue ||
            translationTafsirDataInit.isLoading) {
          ref.invalidate(translationTafsirDataInitProvider);
        }
      }

      final isConnected = ref.read(isConnectedProvider);
      if (isConnected != newConnected) {
        ref.read(isConnectedProvider.notifier).state = newConnected;
      }
    });

    ref.read(minioDataRepositoryProvider).updateAllIndexes();

    QuranTextCacheUtils.init();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final micPermissionStatus = await Permission.microphone.status;

      if (micPermissionStatus.isGranted) {
        initInferencePipeline();
      }
    });

    ref.read(mushafDataInitProvider);
    ref.read(audioSegmentDataInitProvider);
    ref.read(translationTafsirDataInitProvider);
    ref.read(quranImageAssetProvider);

    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.inactive ||
        state == AppLifecycleState.paused ||
        state == AppLifecycleState.hidden) {
      stopInference();

      if (mounted) {
        setState(() {});
      }
    } else if (state == AppLifecycleState.resumed) {
      final micPermissionStatus = await Permission.microphone.status;

      if (micPermissionStatus.isGranted) {
        initInferencePipeline();
      }

      if (mounted) {
        ref.read(pageIdProvider.notifier).state = const Uuid().v1();
      }
    } else if (state == AppLifecycleState.detached) {
      stopInference();

      final inferencePipeline = InferencePipeline.getInstance();
      inferencePipeline.delete();
    }

    super.didChangeAppLifecycleState(state);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    connectivitySubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
        selectedFontSize: 12,
        selectedItemColor: ColorResource.primary(context.isDarkMode),
        selectedLabelStyle: const TextStyle(
            fontFamily: 'ProductSans', fontWeight: FontWeight.w500),
        unselectedFontSize: 12,
        unselectedLabelStyle: const TextStyle(fontFamily: 'ProductSans'),
        currentIndex: selectedSectionIndex,
        onTap: (index) {
          setState(() {
            selectedSectionIndex = index;
          });
          
          // Update donation page visibility
          donationPageVisible.value = (index == 2);
        },
        items: [
          BottomNavigationBarItem(
            icon: SvgPicture.asset(
              Assets.svgsIcMushafiLogo,
              width: 24,
              height: 24,
              colorFilter:
                  const ColorFilter.mode(Color(0xFF7D8898), BlendMode.srcIn),
            ),
            activeIcon: SvgPicture.asset(
              context.isDarkMode
                  ? Assets.svgsIcMushafiLogoDarkMode
                  : Assets.svgsIcMushafiLogo,
              width: 24,
              height: 24,
            ),
            label: context.tr('app_name'),
          ),
          BottomNavigationBarItem(
            icon: SvgPicture.asset(Assets.svgsIcReading, width: 24, height: 24),
            activeIcon: SvgPicture.asset(Assets.svgsIcReadingActive,
                width: 24, height: 24),
            label: context.tr('home.activities'),
          ),
          BottomNavigationBarItem(
            icon: SvgPicture.asset(
              Assets.svgsDonation,
              width: 24,
              height: 24,
              colorFilter: const ColorFilter.mode(
                Color(0xFF7D8898), // gray color
                BlendMode.srcIn,
              ),
            ),
            activeIcon: SvgPicture.asset(
              Assets.svgsDonation,
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(
                ColorResource.primary(context.isDarkMode), // green color
                BlendMode.srcIn,
              ),
            ),
            label: context.tr('home.donation_page.donate'),
          ),
        ],
      ),
      body: IndexedStack(
        index: selectedSectionIndex,
        children: pages,
      ),
    );
  }
}
