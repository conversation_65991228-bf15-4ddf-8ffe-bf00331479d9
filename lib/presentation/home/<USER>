import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/generated/flatbuffer_flatbuffer_generated.dart';
import 'package:mushafi/presentation/model/home/<USER>';
import 'package:mushafi/presentation/notifier/juz_list_notifier.dart';
import 'package:mushafi/provider/repository_provider.dart';

final juzListProvider =
    AsyncNotifierProvider<JuzListNotifier, List<Juz>>(() => JuzListNotifier());

final recentPageListProvider = StreamProvider<List<RecentPage>>((ref) {
  final recentPageRepository = ref.watch(recentPageRepositoryProvider);
  return recentPageRepository.watchRecentPageList();
});

final isConnectedProvider = StateProvider<bool>((ref) => true);

final bookmarkListProvider = StreamProvider((ref) {
  final bookmarkRepository = ref.watch(bookmarkRepositoryProvider);
  return bookmarkRepository.watchBookmarkList();
});
