import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/activity/activity_input_screen.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/home/<USER>';

class ActivityPage extends ConsumerWidget {
  final Function(Track) onActivitySelected;

  const ActivityPage(this.onActivitySelected, {super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Container(
        color: ColorResource.backgroundDefault(context.isDarkMode),
        child: Column(
          children: [
            buildHeader(context, ref),
            Expanded(
              child: Activities(
                (track) async {
                  await onActivitySelected(track);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildHeader(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.only(
        left: (context.isArabic) ? 0 : 16.0,
        top: 16.0,
        bottom: 16.0,
        right: (context.isArabic) ? 16.0 : 0,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            context.tr('activity.title'),
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 24,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: () async {
              final track = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ActivityInputScreen(),
                ),
              ) as Track?;

              if (context.mounted && track != null) {
                await onActivitySelected(track);
              }
            },
            child: Text(
              context.tr('activity.create'),
              style: TextStyle(
                color: ColorResource.primary(context.isDarkMode),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
