import 'dart:io';
import 'dart:ui' as ui;

import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/verse_key_parser.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/activity/activity_input_provider.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/nullable.dart';
import 'package:mushafi/presentation/model/quran/mushaf_code.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/presentation/model/recitation/reciter.dart';
import 'package:mushafi/presentation/model/track/picker_state.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/model/track/track_range.dart';
import 'package:mushafi/presentation/model/track/track_range_portion.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/constants.dart';
import 'package:mushafi/widget/activity/picker.dart';
import 'package:mushafi/widget/activity/portion_range_picker.dart';
import 'package:mushafi/widget/activity/portion_time_picker.dart';
import 'package:mushafi/widget/common/choice_button.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ActivityInputScreen extends ConsumerStatefulWidget {
  final Track? track;
  final TrackType? trackType;

  const ActivityInputScreen({super.key, this.track, this.trackType});

  @override
  ConsumerState<ActivityInputScreen> createState() =>
      _ActivityInputScreenState();
}

class _ActivityInputScreenState extends ConsumerState<ActivityInputScreen> {
  bool modelCompatible = true;

  late final Map<String, String> _activityTypeChoiceMap;
  late final Map<String, String> _typeOfReadingChoiceMap;
  late final Map<String, String> _readingTypeInfoMap;
  late final Map<String, String> _portionChoiceMap;

  late final designList = MushafDesign.getList(context.isDarkMode);

  final weekdays = DateFormat(null, 'en_US').dateSymbols.WEEKDAYS;
  List<Reciter> reciterList = [];
  List<String> reciterDisplayedValues = [];

  List<String> juzNameList = [];
  List<String> pageNameList = [];

  final TextEditingController activityNameController = TextEditingController();

  String activityType = TrackType.readingCoverToCover.activityType;
  String activitySubType = TrackType.readingCoverToCover.activitySubType;
  String portion = TrackRangePortion.range.name;
  bool enableHabitualReminder = false;
  int reciteVerseCount = 1;
  bool enableListeningLoop = false;
  bool enableDownloadAudio = false;
  bool enableAutoRecite = false;
  MushafCode designCode = MushafCode.medina;

  PickerState portionPickerState = PickerState.none;
  bool reciterPickerVisible = false;
  bool dayPickerVisible = false;

  bool isLoading = true;
  bool isEdit = false;

  void updateRangeIndex(String id, int index) {
    if (mounted) {
      ref.read(pickerValueProvider(id).notifier).updateValue(index);
    }
  }

  @override
  void initState() {
    FirebaseAnalytics.instance.logScreenView(screenName: "activity_input");

    modelCompatible = PreferenceStorage.getModelCompatible();

    isEdit = widget.track != null && widget.track?.id != 0;

    final trackType = widget.trackType;
    if (trackType != null) {
      activityType = trackType.activityType;
      activitySubType = trackType.activitySubType;
    }

    final track = widget.track;

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      reciterList =
          await ref.read(minioDataRepositoryProvider).getReciterList();
      reciterDisplayedValues = reciterList.map((e) {
        return (mounted && context.isArabic) ? e.arabicName : e.name;
      }).toList();

      if (track == null) {
        await setupInitialRange();
      }

      setState(() {
        if (track != null) {
          final range = track.range;
          final juzIndex = range.startJuzNumber - 1;
          final startPageIndex = range.startPageNumber - 1;
          final startSurahIndex = range.startSurahNumber - 1;
          final startVerseIndex =
              (range.startVerseNumber == 0) ? 0 : range.startVerseNumber - 1;
          final endPageIndex = range.endPageNumber - 1;
          final endSurahIndex = range.endSurahNumber - 1;
          final endVerseIndex = range.endVerseNumber - 1;
          final reciter = track.reciter;
          final reciterIndex =
              (reciter != null) ? reciterList.indexOf(reciter) : 0;

          updateRangeIndex(juzPickerId, juzIndex);
          updateRangeIndex(startSurahPickerId, startSurahIndex);
          updateRangeIndex(startVersePickerId, startVerseIndex);
          updateRangeIndex(endSurahPickerId, endSurahIndex);
          updateRangeIndex(endVersePickerId, endVerseIndex);
          updateRangeIndex(startPagePickerId, startPageIndex);
          updateRangeIndex(endPagePickerId, endPageIndex);
          updateRangeIndex(reciterPickerId, reciterIndex);

          reciteVerseCount = track.reciteVerseCount;
          enableListeningLoop = track.isLoop;
          activityType = track.type?.activityType ??
              TrackType.readingCoverToCover.activityType;
          activitySubType = track.type?.activitySubType ??
              TrackType.readingCoverToCover.activitySubType;
          activityNameController.text = track.name ?? '';
          portion = track.rangePortion.name;
          enableDownloadAudio = track.enableDownloadAudio;
          designCode = track.design?.code ?? MushafCode.medina;
        }

        isLoading = false;
      });
    });

    super.initState();
  }

  Future<void> setupInitialRange() async {
    if (!mounted) return;

    final quranRepository = ref.read(quranRepositoryProvider);

    final prefs = await SharedPreferences.getInstance();
    final currentPageNumber = prefs.getInt(pageNumberKey) ?? 1;

    final page = await quranRepository.getPageByPageNumber(currentPageNumber);
    if (page == null || page.juzId == null || page.surahId == null) return;

    final juz = await quranRepository.getJuzByJuzNumber(page.juzId!.max);
    if (juz == null) return;

    final startVerse =
        await quranRepository.getQuranVerseById(page.firstVerseId);
    final endVerse = await quranRepository.getQuranVerseById(page.lastVerseId);

    final juzIndex = juz.juzId - 1;
    final startPageIndex = currentPageNumber - 1;
    final startSurahIndex = page.surahId!.min - 1;
    final startVerseIndex = (startVerse?.verseKey.parseVerseNumber() ?? 1) - 1;
    final endPageIndex = currentPageNumber - 1;
    final endSurahIndex = page.surahId!.max - 1;
    final endVerseIndex = (endVerse?.verseKey.parseVerseNumber() ?? 1) - 1;

    updateRangeIndex(juzPickerId, juzIndex);
    updateRangeIndex(startSurahPickerId, startSurahIndex);
    updateRangeIndex(startVersePickerId, startVerseIndex);
    updateRangeIndex(endSurahPickerId, endSurahIndex);
    updateRangeIndex(endVersePickerId, endVerseIndex);
    updateRangeIndex(startPagePickerId, startPageIndex);
    updateRangeIndex(endPagePickerId, endPageIndex);
  }

  bool _showEndRange() {
    return activitySubType != TrackType.readingCoverToCover.activitySubType &&
        activitySubType != TrackType.readingHabitual.activitySubType;
  }

  void onStartSurahValueChanged(int surahValue, List<Surah> surahList) {
    final startSurahIndex = ref.read(pickerValueProvider(startSurahPickerId));
    if (startSurahIndex != surahValue) {
      // todo replace all class members to use riverpod state
      final startSurahIndex = surahValue;

      updateRangeIndex(startSurahPickerId, startSurahIndex);
      updateRangeIndex(startVersePickerId, 0);

      final endSurahIndex = ref.read(pickerValueProvider(endSurahPickerId));
      if (startSurahIndex > endSurahIndex) {
        updateRangeIndex(endSurahPickerId, startSurahIndex);

        final endSurah = surahList.elementAtOrNull(startSurahIndex);
        final endVerseIndex =
            (endSurah != null) ? (endSurah.versesCount - 1) : 0;

        updateRangeIndex(endVersePickerId, endVerseIndex);
      }
    }
  }

  void onStartVerseValueChanged(int verseValue) {
    final startVerseIndex = ref.read(pickerValueProvider(startVersePickerId));
    if (startVerseIndex != verseValue) {
      final startVerseIndex = verseValue;

      updateRangeIndex(startVersePickerId, startVerseIndex);

      final startSurahIndex = ref.read(pickerValueProvider(startSurahPickerId));
      final endSurahIndex = ref.read(pickerValueProvider(endSurahPickerId));
      final endVerseIndex = ref.read(pickerValueProvider(endVersePickerId));
      if (startSurahIndex == endSurahIndex && startVerseIndex > endVerseIndex) {
        updateRangeIndex(endVersePickerId, startVerseIndex);
      }
    }
  }

  void onEndSurahValueChanged(int surahValue, List<Surah> surahList) {
    final endSurahIndex = ref.read(pickerValueProvider(endSurahPickerId));
    if (endSurahIndex != surahValue) {
      final endSurahIndex = surahValue;
      updateRangeIndex(endSurahPickerId, endSurahIndex);

      final startVerseIndex = ref.read(pickerValueProvider(startVersePickerId));

      final endSurah = surahList.elementAtOrNull(endSurahIndex);
      final endVerseIndex =
          (endSurah != null) ? (endSurah.versesCount - 1) : startVerseIndex;

      updateRangeIndex(endVersePickerId, endVerseIndex);
    }
  }

  void onEndVerseValueChanged(int verseValue) {
    final endVerseIndex = ref.read(pickerValueProvider(endVersePickerId));
    if (endVerseIndex != verseValue) {
      updateRangeIndex(endVersePickerId, verseValue);
    }
  }

  Future<TrackRange?> getTrackRange() async {
    final quranRepository = ref.read(quranRepositoryProvider);
    final jsonDataRepository = ref.read(jsonDataRepositoryProvider);

    final surahList = await jsonDataRepository.getSurahList();

    final startSurahIndex = ref.read(pickerValueProvider(startSurahPickerId));
    final endSurahIndex = ref.read(pickerValueProvider(endSurahPickerId));

    switch (TrackRangePortion.fromName(portion)) {
      case null:
        return null;
      case TrackRangePortion.surah:
        final surah = surahList[startSurahIndex];

        final startPage =
            await quranRepository.getPageByPageNumber(surah.pages.min);
        if (startPage == null || startPage.juzId == null) return null;

        final startJuz =
            await quranRepository.getJuzByJuzNumber(startPage.juzId!.min);
        if (startJuz == null) return null;

        final endPage =
            await quranRepository.getPageByPageNumber(surah.pages.max);
        if (endPage == null || endPage.juzId == null) return null;

        final endJuz =
            await quranRepository.getJuzByJuzNumber(endPage.juzId!.max);
        if (endJuz == null) return null;

        return TrackRange(
          startJuzNumber: startJuz.juzId,
          startPageNumber: startPage.pageId,
          startSurahNumber: surah.id,
          startVerseNumber: 1,
          endJuzNumber: endJuz.juzId,
          endPageNumber: endPage.pageId,
          endSurahNumber: surah.id,
          endVerseNumber: surah.versesCount,
        );
      case TrackRangePortion.juz:
        final juzIndex = ref.read(pickerValueProvider(juzPickerId));

        final juz = await quranRepository.getJuzByJuzNumber(juzIndex + 1);
        if (juz == null) return null;

        final startSurahNumber = juz.firstVerse?.verseKey?.parseSurahNumber();
        if (startSurahNumber == null) return null;

        final startVerseNumber = juz.firstVerse?.verseKey?.parseVerseNumber();
        if (startVerseNumber == null) return null;

        int? endPageNumber;
        int? endSurahNumber;
        int? endVerseNumber;

        if (juzIndex < 29) {
          final nextJuz = await quranRepository.getJuzByJuzNumber(juzIndex + 2);
          if (nextJuz == null) return null;

          final nextJuzFirstVerse = nextJuz.firstVerse;
          if (nextJuzFirstVerse == null || nextJuzFirstVerse.verseKey == null)
            return null;

          final nextJuzFirstSurahNumber =
              nextJuzFirstVerse.verseKey?.parseSurahNumber();
          if (nextJuzFirstSurahNumber == null) return null;

          final nextJuzFirstVerseNumber =
              nextJuzFirstVerse.verseKey?.parseVerseNumber();
          if (nextJuzFirstVerseNumber == null) return null;

          if (nextJuzFirstVerseNumber == 1) {
            final prevSurahNumber = nextJuzFirstSurahNumber - 1;
            final endSurah = surahList[prevSurahNumber - 1];

            endPageNumber = endSurah.pages.max;
            endSurahNumber = endSurah.id;
            endVerseNumber = endSurah.versesCount;
          } else {
            endVerseNumber = nextJuzFirstVerseNumber - 1;

            final endVerse = await quranRepository.getQuranVerseByVerseKey(
                '$nextJuzFirstSurahNumber:$endVerseNumber');
            if (endVerse == null) return null;

            endPageNumber = endVerse.pageId;
            endSurahNumber = endVerse.surahId;
          }
        } else {
          endPageNumber = 604;
          endSurahNumber = 114;
          endVerseNumber = 6;
        }

        return TrackRange(
          startJuzNumber: juz.juzId,
          startPageNumber: juz.startingPage,
          startSurahNumber: startSurahNumber,
          startVerseNumber: startVerseNumber,
          endJuzNumber: juz.juzId,
          endPageNumber: endPageNumber,
          endSurahNumber: endSurahNumber,
          endVerseNumber: endVerseNumber,
        );
      case TrackRangePortion.range:
        final startVerseIndex =
            ref.read(pickerValueProvider(startVersePickerId));
        final endVerseIndex = ref.read(pickerValueProvider(endVersePickerId));

        final startSurah = surahList[startSurahIndex];

        final startVerseNumber = startVerseIndex + 1;
        final startVerse = await quranRepository
            .getQuranVerseByVerseKey('${startSurah.id}:$startVerseNumber');
        if (startVerse == null) return null;

        final startPage =
            await quranRepository.getPageByPageNumber(startVerse.pageId);
        if (startPage == null || startPage.juzId == null) return null;

        final startJuz =
            await quranRepository.getJuzByJuzNumber(startPage.juzId!.min);
        if (startJuz == null) return null;

        final endSurah = surahList[endSurahIndex];

        final endVerseNumber = endVerseIndex + 1;
        final endVerse = await quranRepository
            .getQuranVerseByVerseKey('${endSurah.id}:$endVerseNumber');
        if (endVerse == null) return null;

        final endPage =
            await quranRepository.getPageByPageNumber(endVerse.pageId);
        if (endPage == null || endPage.juzId == null) return null;

        final endJuz =
            await quranRepository.getJuzByJuzNumber(endPage.juzId!.max);
        if (endJuz == null) return null;

        return TrackRange(
          startJuzNumber: startJuz.juzId,
          startPageNumber: startPage.pageId,
          startSurahNumber: startSurah.id,
          startVerseNumber: (startVerseNumber == 0) ? 1 : startVerseNumber,
          endJuzNumber: endJuz.juzId,
          endPageNumber: endPage.pageId,
          endSurahNumber: endSurah.id,
          endVerseNumber: endVerseNumber,
        );
      case TrackRangePortion.page:
        final startPageIndex = ref.read(pickerValueProvider(startPagePickerId));
        final endPageIndex = ref.read(pickerValueProvider(endPagePickerId));

        final startPage =
            await quranRepository.getPageByPageNumber(startPageIndex + 1);
        if (startPage == null ||
            startPage.juzId == null ||
            startPage.surahId == null) return null;

        final startJuz =
            await quranRepository.getJuzByJuzNumber(startPage.juzId!.min);
        if (startJuz == null) return null;

        final startSurah = surahList[startPage.surahId!.min - 1];

        final startVerse =
            await quranRepository.getQuranVerseById(startPage.firstVerseId);
        if (startVerse == null) return null;

        final endPage =
            await quranRepository.getPageByPageNumber(endPageIndex + 1);
        if (endPage == null || endPage.juzId == null || endPage.surahId == null)
          return null;

        final endJuz =
            await quranRepository.getJuzByJuzNumber(endPage.juzId!.max);
        if (endJuz == null) return null;

        final endSurah = surahList[endPage.surahId!.max - 1];

        final endVerse =
            await quranRepository.getQuranVerseById(endPage.lastVerseId);
        if (endVerse == null) return null;

        startPage.firstVerseId;
        endPage.lastVerseId;
        return TrackRange(
          startJuzNumber: startJuz.juzId,
          startPageNumber: startPage.pageId,
          startSurahNumber: startSurah.id,
          startVerseNumber: startVerse.verseKey.parseVerseNumber(),
          endJuzNumber: endJuz.juzId,
          endPageNumber: endPage.pageId,
          endSurahNumber: endSurah.id,
          endVerseNumber: endVerse.verseKey.parseVerseNumber(),
        );
    }
  }

  Future<void> updateOrCreateActivity() async {
    final trackRepository = ref.read(trackRepositoryProvider);

    final trackRange = await getTrackRange();
    if (trackRange == null) {
      if (mounted) {
        Navigator.pop(context);
      }
      return;
    }

    final rangePortion = TrackRangePortion.fromName(portion);
    if (rangePortion == null) {
      if (mounted) {
        Navigator.pop(context);
      }
      return;
    }

    final reciterIndex = ref.read(pickerValueProvider(reciterPickerId));
    final reciter = (activityType == TrackType.listening.activityType)
        ? reciterList.elementAtOrNull(reciterIndex)
        : null;

    final trackType = TrackType.fromSubType(activitySubType);

    final Track selectedTrack;
    if (isEdit) {
      Nullable<String>? lastHighlightedVerseKey = Nullable(null);
      if (trackType == TrackType.listening &&
          widget.track!.range == trackRange) {
        lastHighlightedVerseKey =
            Nullable(widget.track!.lastHighlightedVerseKey);
      }

      final updatedTrack = widget.track!.copyWith(
        type: Nullable(trackType),
        range: trackRange,
        reciteVerseCount: reciteVerseCount,
        isLoop: enableListeningLoop,
        reciter: Nullable(reciter),
        lastRecitedWord: Nullable(null),
        lastHighlightedVerseKey: lastHighlightedVerseKey,
        name: Nullable(activityNameController.text),
        rangePortion: rangePortion,
        enableDownloadAudio: enableDownloadAudio,
        design: Nullable(MushafDesign.getList(context.isDarkMode)
            .firstWhere((design) => design.code == designCode)),
      );

      await trackRepository.updateTrack(updatedTrack);

      if (trackType == TrackType.readingWithAi ||
          trackType == TrackType.memorizing) {
        await ref
            .read(alignmentRepositoryProvider)
            .deleteTrackResults(updatedTrack.id);
      }

      selectedTrack = updatedTrack;
    } else {
      final newTrack = Track.create(
        range: trackRange,
        reciter: reciter,
        reciteVerseCount: reciteVerseCount,
        isLoop: enableListeningLoop,
        enableEnglishRecitation: false,
        type: trackType,
        name: activityNameController.text,
        rangePortion: rangePortion,
        enableRecitationChecker: false,
        zoomState: ZoomState.defaultZoom,
        enableDownloadAudio: enableDownloadAudio,
        audioSize: 0,
        isAudioDownloaded: false,
        design: MushafDesign.getList(context.isDarkMode)
            .firstWhere((design) => design.code == designCode),
      );

      final track = await trackRepository.addTrack(newTrack);
      selectedTrack = track;
    }

    if (mounted) {
      Navigator.pop(context, selectedTrack);
    }
  }

  Future<void> showDeleteConfirmationDialog(Track track) async {
    if (Platform.isIOS || Platform.isMacOS) {
      return showCupertinoDialog(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(
              context.tr('activity_input.delete_confirm_title'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            content: Text(
              context.tr('activity_input.delete_confirm_message'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  context.tr('activity_input.cancel'),
                  style: TextStyle(
                      color: ColorResource.textBlue(context.isDarkMode)),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  final trackRepository = ref.read(trackRepositoryProvider);
                  trackRepository.deleteTrack(track.id);
                  Navigator.pop(context);
                },
                child: Text(
                  context.tr('common.delete'),
                  style: TextStyle(
                      color: ColorResource.danger(context.isDarkMode)),
                ),
              ),
            ],
          );
        },
      );
    }

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(
          context.tr('activity_input.delete_confirm_title'),
          style:
              TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
        content: Text(
          context.tr('activity_input.delete_confirm_message'),
          style:
              TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              context.tr('activity_input.cancel'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final trackRepository = ref.read(trackRepositoryProvider);
              trackRepository.deleteTrack(track.id);
              Navigator.pop(context);
            },
            child: Text(
              context.tr('activity_input.yes'),
              style:
                  TextStyle(color: ColorResource.textBlue(context.isDarkMode)),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    _activityTypeChoiceMap = {
      TrackType.readingCoverToCover.activityType:
          context.tr('activity.reading'),
      TrackType.listening.activityType: context.tr('activity.listening'),
    };

    if (modelCompatible) {
      _activityTypeChoiceMap[TrackType.memorizing.activityType] =
          context.tr('activity.memorizing');
    }

    _portionChoiceMap = {
      TrackRangePortion.surah.name: context.tr('activity_input.surah'),
      TrackRangePortion.juz.name: context.tr('activity_input.juz'),
      TrackRangePortion.range.name: context.tr('activity_input.range'),
      TrackRangePortion.page.name: context.tr('activity_input.page'),
    };

    _typeOfReadingChoiceMap = {
      TrackType.readingCoverToCover.activitySubType:
          context.tr('activity_input.cover_to_cover'),
      TrackType.readingHabitual.activitySubType:
          context.tr('activity_input.habitual'),
    };

    if (modelCompatible) {
      _typeOfReadingChoiceMap[TrackType.readingWithAi.activitySubType] =
          context.tr('activity_input.recite_with_ai');
    }

    _readingTypeInfoMap = {
      TrackType.readingCoverToCover.activitySubType:
          context.tr('activity_input.cover_to_cover_description'),
      TrackType.readingHabitual.activitySubType:
          context.tr('activity_input.habitual_description'),
      TrackType.readingWithAi.activitySubType:
          context.tr('activity_input.recite_with_ai_description'),
    };

    juzNameList = List.generate(30, (index) {
      final number = (context.isArabic)
          ? NumberFormat('#', 'ar_EG').format(index + 1)
          : (index + 1).toString();
      return number;
    });

    pageNameList = List.generate(604, (index) {
      final number = (context.isArabic)
          ? NumberFormat('#', 'ar_EG').format(index + 1)
          : (index + 1).toString();
      return context.tr('activity_input.page_number', args: [number]);
    });
  }

  @override
  Widget build(BuildContext context) {
    final mushafDataInitAsync = ref.watch(mushafDataInitProvider);

    return Scaffold(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      appBar: _buildAppBar(),
      body: mushafDataInitAsync.when(
        data: (data) {
          if (isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding:
                      const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildActivityNameForm(),
                      const SizedBox(height: 8),
                      _buildTrackTypeChoice(),
                      const SizedBox(height: 8),
                      if (activityType ==
                          TrackType.readingCoverToCover.activityType)
                        _buildReadingOptions()
                      else if (activityType == TrackType.listening.activityType)
                        _buildListeningOptions()
                      else if (activityType ==
                          TrackType.memorizing.activityType)
                        _buildMemorizingOptions(),
                      const SizedBox(height: 8),
                      buildMushafDesignSelection(),
                    ],
                  ),
                ),
              ),
              if (isEdit) ...[
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: InkWell(
                    onTap: () {
                      if (widget.track != null) {
                        showDeleteConfirmationDialog(widget.track!);
                      }
                    },
                    child: Card(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                        side: const BorderSide(
                          color: Colors.red,
                          width: 2,
                        ),
                      ),
                      color: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          context.tr('activity_input.delete_button'),
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.red,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          );
        },
        error: (error, st) {
          talker.handle(error, st);
          return Center(
            child: Text(error.toString()),
          );
        },
        loading: () => Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 8),
              Text(context.tr('quran.downloading')),
            ],
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;
    return AppBar(
      backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
      leading: IconButton(
        icon: SvgPicture.asset(
          (isRtl)
              ? Assets.svgsIcChevronRight24px
              : Assets.svgsIcChevronLeft24px,
          colorFilter: ColorFilter.mode(
            ColorResource.textDefault(context.isDarkMode),
            BlendMode.srcIn,
          ),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Text(
        isEdit
            ? context.tr('activity_input.edit_title')
            : context.tr('activity_input.title'),
        style: TextStyle(
            fontSize: 16, color: ColorResource.textDefault(context.isDarkMode)),
      ),
      centerTitle: true,
      actions: [
        if (Platform.isIOS || Platform.isMacOS)
          TextButton(
            onPressed: updateOrCreateActivity,
            child: Text(
              context.tr('common.save'),
              style:
                  TextStyle(color: ColorResource.primary(context.isDarkMode)),
            ),
          )
        else
          IconButton(
            icon: SvgPicture.asset(
              Assets.svgsIcCheckCircle,
              colorFilter: ColorFilter.mode(
                ColorResource.textDefault(context.isDarkMode),
                BlendMode.srcIn,
              ),
            ),
            onPressed: updateOrCreateActivity,
          )
      ],
    );
  }

  Widget _buildActivityNameForm() {
    return Card(
      color: ColorResource.backgroundWhite(context.isDarkMode),
      elevation: 0,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
        child: TextField(
          controller: activityNameController,
          decoration: InputDecoration(
            labelText: context.tr('activity_input.activity_name'),
            hintText: context.tr('activity_input.activity_name_hint'),
            hintStyle:
                TextStyle(color: Theme.of(context).hintColor.withOpacity(.25)),
            floatingLabelBehavior: FloatingLabelBehavior.always,
            border: InputBorder.none,
          ),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: ColorResource.textDefault(context.isDarkMode),
          ),
        ),
      ),
    );
  }

  Widget _buildTrackTypeChoice() {
    return Card(
      color: ColorResource.backgroundWhite(context.isDarkMode),
      elevation: 0,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              context.tr('activity_input.activity_type'),
              style: TextStyle(
                fontSize: 16,
                color: ColorResource.textDefault(context.isDarkMode),
              ),
            ),
            const SizedBox(height: 8),
            ChoiceButton(
              keyLabelMap: _activityTypeChoiceMap,
              selected: activityType,
              onSelectionChanged: (selection) {
                setState(() {
                  portion = TrackRangePortion.range.name;
                  activityType = selection;
                  activitySubType =
                      TrackType.fromType(selection)?.activitySubType ?? '';
                  portionPickerState = PickerState.none;
                  reciterPickerVisible = false;
                  dayPickerVisible = false;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReadingOptions() {
    return Card(
      color: ColorResource.backgroundWhite(context.isDarkMode),
      elevation: 0,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTypeOfReadingChoice(),
            const SizedBox(height: 16),
            _buildReadingTypeInfo(),
            const Divider(thickness: .2),
            if (activitySubType == TrackType.readingWithAi.activitySubType)
              _buildPortion()
            else
              _buildTrackRangePortionSelection(),
            if (activitySubType == TrackType.readingHabitual.activitySubType)
              _buildHabitualReadingReminderOption()
          ],
        ),
      ),
    );
  }

  Widget _buildListeningOptions() {
    return Card(
      color: ColorResource.backgroundWhite(context.isDarkMode),
      elevation: 0,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildReciterSelection(),
            const Divider(thickness: .2),
            _buildPortion(),
            const Divider(thickness: .2),
            _buildListeningRepeatCounter(),
            const Divider(thickness: .2),
            _buildListeningLoopOption(),
            // const Divider(thickness: .2),
            // _buildDownloadAudioOption(),
            // const Divider(thickness: .2),
          ],
        ),
      ),
    );
  }

  Widget _buildMemorizingOptions() {
    return Card(
      color: ColorResource.backgroundWhite(context.isDarkMode),
      elevation: 0,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: _buildPortion(),
      ),
    );
  }

  Widget _buildListeningRepeatCounter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          context.tr('activity_input.repeat_verse'),
          style: TextStyle(
              color: ColorResource.textGrey(context.isDarkMode), fontSize: 16),
        ),
        Row(
          children: [
            InkWell(
              onTap: () {
                if (reciteVerseCount > 1) {
                  setState(() {
                    reciteVerseCount--;
                  });
                }
              },
              child: SvgPicture.asset(
                Assets.svgsIcMinusCircle,
                colorFilter: ColorFilter.mode(
                  ColorResource.textDefault(context.isDarkMode),
                  BlendMode.srcIn,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                (context.isArabic)
                    ? NumberFormat('#', 'ar_EG').format(reciteVerseCount)
                    : reciteVerseCount.toString(),
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                  color: ColorResource.textDefault(context.isDarkMode),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                setState(() {
                  reciteVerseCount++;
                });
              },
              child: SvgPicture.asset(
                Assets.svgsIcPlusCircle,
                colorFilter: ColorFilter.mode(
                  ColorResource.textDefault(context.isDarkMode),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _buildListeningLoopOption() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          context.tr('activity_input.loop'),
          style: TextStyle(
              color: ColorResource.textGrey(context.isDarkMode), fontSize: 16),
        ),
        Theme(
          data: ThemeData(useMaterial3: false),
          child: Switch.adaptive(
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            value: enableListeningLoop,
            onChanged: (value) {
              setState(() {
                enableListeningLoop = value;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDownloadAudioOption() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                SvgPicture.asset(Assets.svgsIcDownloadCircle),
                const SizedBox(width: 4),
                Text(
                  context.tr('activity_input.download_audio'),
                  style: TextStyle(
                      color: ColorResource.textGrey(context.isDarkMode),
                      fontSize: 16),
                ),
              ],
            ),
            Theme(
              data: ThemeData(useMaterial3: false),
              child: Switch.adaptive(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                value: enableDownloadAudio,
                onChanged: (value) {
                  setState(() {
                    enableDownloadAudio = value;
                  });
                },
              ),
            ),
          ],
        ),
        if (enableDownloadAudio) ...[
          Text(
            context.tr('activity_input.download_audio_description'),
            style: TextStyle(
                color: ColorResource.textGrey(context.isDarkMode),
                fontSize: 12),
          )
        ]
      ],
    );
  }

  Widget _buildAutoReciteOption() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              context.tr('activity_input.auto_recite'),
              style: TextStyle(
                  color: ColorResource.textGrey(context.isDarkMode),
                  fontSize: 16),
            ),
            Theme(
              data: ThemeData(useMaterial3: false),
              child: Switch.adaptive(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                value: enableListeningLoop,
                onChanged: (value) {
                  setState(() {
                    enableListeningLoop = value;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReciterSelection() {
    return Picker(
      id: reciterPickerId,
      title: context.tr('activity_input.reciter'),
      isPickerVisible: reciterPickerVisible,
      minValue: 0,
      maxValue: reciterList.length - 1,
      value: 0,
      displayedValues: reciterDisplayedValues,
      onValueChanged: (reciterValue) {
        final reciterIndex = ref.read(pickerValueProvider(reciterPickerId));
        if (reciterIndex != reciterValue) {
          updateRangeIndex(reciterPickerId, reciterValue);
        }
      },
      onPickerVisibilityChanged: (isVisible) {
        setState(() {
          reciterPickerVisible = isVisible;
        });
      },
    );
  }

  Widget _buildPortion() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          context.tr('activity_input.portion'),
          style: TextStyle(
              fontSize: 16,
              color: ColorResource.textDefault(context.isDarkMode)),
        ),
        const SizedBox(height: 8),
        ChoiceButton(
          keyLabelMap: _portionChoiceMap,
          selected: portion,
          onSelectionChanged: (selection) {
            setState(() {
              portion = selection;
              portionPickerState = PickerState.none;
              reciterPickerVisible = false;
              dayPickerVisible = false;
            });
          },
        ),
        const SizedBox(height: 16),
        switch (TrackRangePortion.fromName(portion)) {
          null => const SizedBox(),
          TrackRangePortion.surah => _buildSurahPortionSelection(),
          TrackRangePortion.juz => _buildJuzPortionSelection(),
          TrackRangePortion.range => _buildTrackRangePortionSelection(),
          TrackRangePortion.page => _buildPagePortionSelection(),
        }
      ],
    );
  }

  Widget _buildSurahPortionSelection() {
    final surahListAsync = ref.watch(surahListProvider);
    return surahListAsync.when(
      data: (surahList) {
        final surahNameList = surahList.map((surah) {
          return (context.isArabic) ? surah.nameArabic : surah.nameComplex;
        }).toList();
        return Picker(
          id: startSurahPickerId,
          title: context.tr('activity_input.surah'),
          isPickerVisible: portionPickerState == PickerState.start,
          minValue: 0,
          maxValue: surahList.length - 1,
          value: 0,
          displayedValues: surahNameList,
          onValueChanged: (surahValue) {
            onStartSurahValueChanged(surahValue, surahList);
          },
          onPickerVisibilityChanged: (isVisible) {
            setState(() {
              portionPickerState =
                  isVisible ? PickerState.start : PickerState.none;
            });
          },
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stack) => Center(
        child: Text(
          context.tr('activity_input.error'),
        ),
      ),
    );
  }

  Widget _buildJuzPortionSelection() {
    return Picker(
      id: juzPickerId,
      title: context.tr('activity_input.juz'),
      isPickerVisible: portionPickerState == PickerState.start,
      minValue: 0,
      maxValue: juzNameList.length - 1,
      value: 0,
      displayedValues: juzNameList,
      onValueChanged: (juzValue) {
        final juzIndex = ref.read(pickerValueProvider(juzPickerId));
        if (juzIndex != juzValue) {
          updateRangeIndex(juzPickerId, juzValue);
        }
      },
      onPickerVisibilityChanged: (isVisible) {
        setState(() {
          portionPickerState = isVisible ? PickerState.start : PickerState.none;
        });
      },
    );
  }

  Widget _buildTrackRangePortionSelection() {
    final surahListAsync = ref.watch(surahListProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('activity_input.range'),
          style: TextStyle(
              fontSize: 16, color: ColorResource.textGrey(context.isDarkMode)),
        ),
        const SizedBox(height: 16),
        surahListAsync.when(
          data: (surahList) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PortionRangePicker(
                  surahRangeId: startSurahPickerId,
                  verseRangeId: startVersePickerId,
                  surahList: surahList,
                  title: context.tr('memorization.start'),
                  isPickerVisible: portionPickerState == PickerState.start,
                  onSurahValueChanged: (surahValue) {
                    onStartSurahValueChanged(surahValue, surahList);
                  },
                  onVerseValueChanged: onStartVerseValueChanged,
                  onPickerVisibilityChanged: (isVisible) {
                    setState(() {
                      portionPickerState =
                          isVisible ? PickerState.start : PickerState.none;
                    });
                  },
                ),
                if (_showEndRange()) ...[
                  const SizedBox(height: 16),
                  PortionRangePicker(
                    surahRangeId: endSurahPickerId,
                    verseRangeId: endVersePickerId,
                    surahList: surahList,
                    title: context.tr('memorization.end'),
                    isPickerVisible: portionPickerState == PickerState.end,
                    onSurahValueChanged: (surahValue) {
                      onEndSurahValueChanged(surahValue, surahList);
                    },
                    onVerseValueChanged: onEndVerseValueChanged,
                    onPickerVisibilityChanged: (isVisible) {
                      setState(() {
                        portionPickerState =
                            isVisible ? PickerState.end : PickerState.none;
                      });
                    },
                  ),
                ]
              ],
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stack) => Center(
            child: Text(
              context.tr('activity_input.error'),
              style: TextStyle(
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPagePortionSelection() {
    return Column(
      children: [
        Picker(
          id: startPagePickerId,
          title: context.tr('memorization.start'),
          minValue: 0,
          maxValue: pageNameList.length - 1,
          value: 0,
          displayedValues: pageNameList,
          isPickerVisible: portionPickerState == PickerState.start,
          onValueChanged: (pageValue) {
            final startPageIndex =
                ref.read(pickerValueProvider(startPagePickerId));
            if (startPageIndex != pageValue) {
              final startPageIndex = pageValue;
              updateRangeIndex(startPagePickerId, startPageIndex);

              final endPageIndex =
                  ref.read(pickerValueProvider(endPagePickerId));
              if (startPageIndex > endPageIndex) {
                updateRangeIndex(endPagePickerId, startPageIndex);
              }
            }
          },
          onPickerVisibilityChanged: (isVisible) {
            setState(() {
              portionPickerState =
                  isVisible ? PickerState.start : PickerState.none;
            });
          },
        ),
        const SizedBox(height: 16),
        Picker(
          id: endPagePickerId,
          title: context.tr('memorization.end'),
          minValue: 0,
          maxValue: pageNameList.length - 1,
          value: 0,
          displayedValues: pageNameList,
          isPickerVisible: portionPickerState == PickerState.end,
          onValueChanged: (pageValue) {
            final endPageIndex = ref.read(pickerValueProvider(endPagePickerId));
            if (endPageIndex != pageValue) {
              updateRangeIndex(endPagePickerId, pageValue);
            }
          },
          onPickerVisibilityChanged: (isVisible) {
            setState(() {
              portionPickerState =
                  isVisible ? PickerState.end : PickerState.none;
            });
          },
        ),
      ],
    );
  }

  Widget _buildTypeOfReadingChoice() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('activity_input.type_of_reading'),
          style: TextStyle(
            fontSize: 16,
            color: ColorResource.textDefault(context.isDarkMode),
          ),
        ),
        const SizedBox(height: 8),
        ChoiceButton(
          keyLabelMap: _typeOfReadingChoiceMap,
          selected: activitySubType,
          onSelectionChanged: (selection) {
            setState(() {
              portion = TrackRangePortion.range.name;
              activitySubType = selection;
              portionPickerState = PickerState.none;
              reciterPickerVisible = false;
              dayPickerVisible = false;
            });
          },
        ),
      ],
    );
  }

  Widget _buildReadingTypeInfo() {
    final info = _readingTypeInfoMap[activitySubType];
    final type = _typeOfReadingChoiceMap[activitySubType] ?? '';
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Icon(Icons.info_outline_rounded, size: 16),
        const SizedBox(width: 4),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: ColorResource.textGrey(context.isDarkMode)),
              children: [
                TextSpan(
                  text: type,
                  style: const TextStyle(
                    decoration: TextDecoration.underline,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(
                  text: info?.replaceAll(type, ''),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHabitualReadingReminderOption() {
    return Container();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Divider(thickness: .2),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              context.tr('activity_input.reminder'),
              style: TextStyle(
                  color: ColorResource.textGrey(context.isDarkMode),
                  fontSize: 16),
            ),
            Theme(
              data: ThemeData(useMaterial3: false),
              child: Switch.adaptive(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                value: enableHabitualReminder,
                onChanged: (value) {
                  setState(() {
                    enableHabitualReminder = value;
                  });
                },
              ),
            ),
          ],
        ),
        if (enableHabitualReminder) ...[
          const SizedBox(height: 8),
          Picker(
            id: dayPickerId,
            title: context.tr('activity_input.day'),
            isPickerVisible: dayPickerVisible,
            minValue: 0,
            maxValue: 6,
            value: 0,
            displayedValues: weekdays,
            onValueChanged: (value) {},
            onPickerVisibilityChanged: (isVisible) {
              setState(() {
                dayPickerVisible = isVisible;
              });
            },
          ),
          const SizedBox(height: 16),
          PortionTimePicker(
            hourInitialValue: 0,
            minuteInitialValue: 0,
            meridiemInitialValue: 0,
            onValueChanged: (hour, minute, meridiem) {},
          )
        ]
      ],
    );
  }

  Widget buildMushafDesignSelection() {
    return Card(
      color: ColorResource.backgroundWhite(context.isDarkMode),
      elevation: 0,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('activity_input.mushaf_design'),
              style: TextStyle(
                  fontSize: 16,
                  color: ColorResource.textDefault(context.isDarkMode)),
            ),
            const SizedBox(height: 8),
            SingleChildScrollView(
              child: Row(
                children: List.generate(
                  designList.length,
                  (index) {
                    final design = designList[index];
                    final colorFilter = design.code != designCode
                        ? const ColorFilter.matrix(grayScaleColorFilter)
                        : null;
                    return Row(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              designCode = design.code;
                            });
                          },
                          child: Stack(
                            children: [
                              SizedBox(
                                width: 40,
                                height: 40,
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: SvgPicture.asset(
                                    design.sampleAsset,
                                    colorFilter: colorFilter,
                                  ),
                                ),
                              ),
                              if (design.code == designCode)
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      width: 4,
                                      color: ColorResource.primary(
                                          context.isDarkMode),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
