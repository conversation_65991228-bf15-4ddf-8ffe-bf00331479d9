import 'dart:io';

import 'package:mushafi/presentation/model/ai/inference_model.dart';
import 'package:mushafi/presentation/model/recitation/reciter.dart';
import 'package:mushafi/presentation/model/translation/translation.dart';

abstract class MinioDataRepository {
  Future<void> updateAllIndexes();

  Future<File?> fetchMushaf(
      {String fileName, Function(double percentage)? onDownloading});

  Future<File?> getMushaf(
      {String fileName, Function(double percentage)? onDownloading});

  Future<File?> getTranslationTafsir({
    String fileName,
    Function(double percentage)? onDownloading,
  });

  Future<File?> getAudioSegment({String fileName});

  Future<bool> audioSegmentExists(String fileName);

  Future<File?> getAudioSegmentIndex();

  Future<File?> getTranslationTafsirIndex();

  Future<List<String>> getCachedAudioSegmentNameList();

  Future<List<String>> getCachedTranslationTafsirNameList();

  Future<List<String>> getCachedAsrModelNameList();

  Future<List<String>> getCachedCondModelNameList();

  Future<List<String>> getCachedVadModelNameList();

  Future<List<Reciter>> getReciterList();

  Future<Reciter> getReciter(String key);

  Future<List<Translation>> getTranslationList();

  Future<Translation> getTranslation(String key);

  Future<void> fetchInferenceModel(
    InferenceModel model, {
    Function(double percentage)? onDownloading,
  });

  Future<bool> doModelsExist(InferenceModel model);

  Future<File?> updateModelCompatIndex();

  Future<File?> getModelCompatIndex();

  Future<String?> updateAndGetLatestModelVersion();

  Future<String?> getLatestModelVersion();

  Future<Map<int, List<String>>> getModelIndexMap();
}
