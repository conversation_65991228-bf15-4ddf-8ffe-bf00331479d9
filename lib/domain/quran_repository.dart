import 'dart:io';

import 'package:mushafi/generated/flatbuffer_flatbuffer_generated.dart';
import 'package:mushafi/presentation/model/quran/quran_page.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';

abstract class QuranRepository {
  Future<void> initQuranData(File file);

  Future<void> initTranslationData(File file);

  Future<void> initAudioSegmentData(File file);

  Future<QuranPage?> getCompleteQuranPage(int pageNumber);

  Future<List<Page>?> getPageList();

  Future<Page?> getPageByPageNumber(int pageNumber);

  Future<Juz?> getJuzByJuzNumber(int juzNumber);

  Future<List<Juz>?> getJuzList();

  Future<QuranVerse?> getQuranVerseById(int id);

  Future<List<QuranVerse>?> getQuranVerseByIdList(List<int> idList);

  Future<QuranVerse?> getQuranVerseByVerseKey(String verseKey);

  Future<List<QuranVerse>?> getQuranVerseList(
      String startVerseKey, String endVerseKey);
}
