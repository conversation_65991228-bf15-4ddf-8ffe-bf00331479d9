import 'package:mushafi/presentation/model/quran/alignment_word_result.dart';

abstract class AlignmentRepository {
  Future<void> addAlignmentWordResults(List<AlignmentWordResult> resultList);

  Future<List<AlignmentWordResult>> getAlignmentWordResults(int trackId);

  Future<void> deleteAlignmentWordResults(
      int trackId, int startTime, int endTime);

  Future<void> deleteTrackResults(int trackId);
}
