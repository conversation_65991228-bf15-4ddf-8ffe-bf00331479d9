// automatically generated by the FlatBuffers compiler, do not modify
// ignore_for_file: unused_import, unused_field, unused_element, unused_local_variable

library flatbuffer;

import 'dart:typed_data' show Uint8List;
import 'package:flat_buffers/flat_buffers.dart' as fb;

class Word {
  Word._(this._bc, this._bcOffset);
  factory Word(List<int> bytes) {
    final rootRef = fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<Word> reader = _WordReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  int get wordId => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 4, 0);
  int get lineNumber => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 6, 0);
  String? get textUthmani =>
      const fb.StringReader().vTableGetNullable(_bc, _bcOffset, 8);
  String? get textImlaei =>
      const fb.StringReader().vTableGetNullable(_bc, _bcOffset, 10);
  String? get codeV1 =>
      const fb.StringReader().vTableGetNullable(_bc, _bcOffset, 12);
  bool get split => const fb.BoolReader().vTableGet(_bc, _bcOffset, 14, false);

  @override
  String toString() {
    return 'Word{wordId: ${wordId}, lineNumber: ${lineNumber}, textUthmani: ${textUthmani}, textImlaei: ${textImlaei}, codeV1: ${codeV1}, split: ${split}}';
  }
}

class _WordReader extends fb.TableReader<Word> {
  const _WordReader();

  @override
  Word createObject(fb.BufferContext bc, int offset) => Word._(bc, offset);
}

class WordBuilder {
  WordBuilder(this.fbBuilder);

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable(6);
  }

  int addWordId(int? wordId) {
    fbBuilder.addInt32(0, wordId);
    return fbBuilder.offset;
  }

  int addLineNumber(int? lineNumber) {
    fbBuilder.addInt32(1, lineNumber);
    return fbBuilder.offset;
  }

  int addTextUthmaniOffset(int? offset) {
    fbBuilder.addOffset(2, offset);
    return fbBuilder.offset;
  }

  int addTextImlaeiOffset(int? offset) {
    fbBuilder.addOffset(3, offset);
    return fbBuilder.offset;
  }

  int addCodeV1Offset(int? offset) {
    fbBuilder.addOffset(4, offset);
    return fbBuilder.offset;
  }

  int addSplit(bool? split) {
    fbBuilder.addBool(5, split);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class WordObjectBuilder extends fb.ObjectBuilder {
  final int? _wordId;
  final int? _lineNumber;
  final String? _textUthmani;
  final String? _textImlaei;
  final String? _codeV1;
  final bool? _split;

  WordObjectBuilder({
    int? wordId,
    int? lineNumber,
    String? textUthmani,
    String? textImlaei,
    String? codeV1,
    bool? split,
  })  : _wordId = wordId,
        _lineNumber = lineNumber,
        _textUthmani = textUthmani,
        _textImlaei = textImlaei,
        _codeV1 = codeV1,
        _split = split;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(fb.Builder fbBuilder) {
    final int? textUthmaniOffset =
        _textUthmani == null ? null : fbBuilder.writeString(_textUthmani!);
    final int? textImlaeiOffset =
        _textImlaei == null ? null : fbBuilder.writeString(_textImlaei!);
    final int? codeV1Offset =
        _codeV1 == null ? null : fbBuilder.writeString(_codeV1!);
    fbBuilder.startTable(6);
    fbBuilder.addInt32(0, _wordId);
    fbBuilder.addInt32(1, _lineNumber);
    fbBuilder.addOffset(2, textUthmaniOffset);
    fbBuilder.addOffset(3, textImlaeiOffset);
    fbBuilder.addOffset(4, codeV1Offset);
    fbBuilder.addBool(5, _split);
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String? fileIdentifier]) {
    final fbBuilder = fb.Builder(deduplicateTables: false);
    fbBuilder.finish(finish(fbBuilder), fileIdentifier);
    return fbBuilder.buffer;
  }
}

class Verse {
  Verse._(this._bc, this._bcOffset);
  factory Verse(List<int> bytes) {
    final rootRef = fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<Verse> reader = _VerseReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  int get verseId => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 4, 0);
  int get surahId => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 6, 0);
  int get pageId => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 8, 0);
  String? get verseKey =>
      const fb.StringReader().vTableGetNullable(_bc, _bcOffset, 10);
  List<Word>? get words => const fb.ListReader<Word>(Word.reader)
      .vTableGetNullable(_bc, _bcOffset, 12);
  List<AudioSegment>? get audioSegments =>
      const fb.ListReader<AudioSegment>(AudioSegment.reader)
          .vTableGetNullable(_bc, _bcOffset, 14);
  String? get translationOrTafseer =>
      const fb.StringReader().vTableGetNullable(_bc, _bcOffset, 16);

  @override
  String toString() {
    return 'Verse{verseId: ${verseId}, surahId: ${surahId}, pageId: ${pageId}, verseKey: ${verseKey}, words: ${words}, audioSegments: ${audioSegments}, translationOrTafseer: ${translationOrTafseer}}';
  }
}

class _VerseReader extends fb.TableReader<Verse> {
  const _VerseReader();

  @override
  Verse createObject(fb.BufferContext bc, int offset) => Verse._(bc, offset);
}

class VerseBuilder {
  VerseBuilder(this.fbBuilder);

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable(7);
  }

  int addVerseId(int? verseId) {
    fbBuilder.addInt32(0, verseId);
    return fbBuilder.offset;
  }

  int addSurahId(int? surahId) {
    fbBuilder.addInt32(1, surahId);
    return fbBuilder.offset;
  }

  int addPageId(int? pageId) {
    fbBuilder.addInt32(2, pageId);
    return fbBuilder.offset;
  }

  int addVerseKeyOffset(int? offset) {
    fbBuilder.addOffset(3, offset);
    return fbBuilder.offset;
  }

  int addWordsOffset(int? offset) {
    fbBuilder.addOffset(4, offset);
    return fbBuilder.offset;
  }

  int addAudioSegmentsOffset(int? offset) {
    fbBuilder.addOffset(5, offset);
    return fbBuilder.offset;
  }

  int addTranslationOrTafseerOffset(int? offset) {
    fbBuilder.addOffset(6, offset);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class VerseObjectBuilder extends fb.ObjectBuilder {
  final int? _verseId;
  final int? _surahId;
  final int? _pageId;
  final String? _verseKey;
  final List<WordObjectBuilder>? _words;
  final List<AudioSegmentObjectBuilder>? _audioSegments;
  final String? _translationOrTafseer;

  VerseObjectBuilder({
    int? verseId,
    int? surahId,
    int? pageId,
    String? verseKey,
    List<WordObjectBuilder>? words,
    List<AudioSegmentObjectBuilder>? audioSegments,
    String? translationOrTafseer,
  })  : _verseId = verseId,
        _surahId = surahId,
        _pageId = pageId,
        _verseKey = verseKey,
        _words = words,
        _audioSegments = audioSegments,
        _translationOrTafseer = translationOrTafseer;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(fb.Builder fbBuilder) {
    final int? verseKeyOffset =
        _verseKey == null ? null : fbBuilder.writeString(_verseKey!);
    final int? wordsOffset = _words == null
        ? null
        : fbBuilder.writeList(
            _words!.map((b) => b.getOrCreateOffset(fbBuilder)).toList());
    final int? audioSegmentsOffset = _audioSegments == null
        ? null
        : fbBuilder.writeList(_audioSegments!
            .map((b) => b.getOrCreateOffset(fbBuilder))
            .toList());
    final int? translationOrTafseerOffset = _translationOrTafseer == null
        ? null
        : fbBuilder.writeString(_translationOrTafseer!);
    fbBuilder.startTable(7);
    fbBuilder.addInt32(0, _verseId);
    fbBuilder.addInt32(1, _surahId);
    fbBuilder.addInt32(2, _pageId);
    fbBuilder.addOffset(3, verseKeyOffset);
    fbBuilder.addOffset(4, wordsOffset);
    fbBuilder.addOffset(5, audioSegmentsOffset);
    fbBuilder.addOffset(6, translationOrTafseerOffset);
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String? fileIdentifier]) {
    final fbBuilder = fb.Builder(deduplicateTables: false);
    fbBuilder.finish(finish(fbBuilder), fileIdentifier);
    return fbBuilder.buffer;
  }
}

class AudioSegment {
  AudioSegment._(this._bc, this._bcOffset);
  factory AudioSegment(List<int> bytes) {
    final rootRef = fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<AudioSegment> reader = _AudioSegmentReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  int get index => const fb.Uint32Reader().vTableGet(_bc, _bcOffset, 4, 0);
  int get startTime => const fb.Uint32Reader().vTableGet(_bc, _bcOffset, 6, 0);
  int get endTime => const fb.Uint32Reader().vTableGet(_bc, _bcOffset, 8, 0);

  @override
  String toString() {
    return 'AudioSegment{index: ${index}, startTime: ${startTime}, endTime: ${endTime}}';
  }
}

class _AudioSegmentReader extends fb.TableReader<AudioSegment> {
  const _AudioSegmentReader();

  @override
  AudioSegment createObject(fb.BufferContext bc, int offset) =>
      AudioSegment._(bc, offset);
}

class AudioSegmentBuilder {
  AudioSegmentBuilder(this.fbBuilder);

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable(3);
  }

  int addIndex(int? index) {
    fbBuilder.addUint32(0, index);
    return fbBuilder.offset;
  }

  int addStartTime(int? startTime) {
    fbBuilder.addUint32(1, startTime);
    return fbBuilder.offset;
  }

  int addEndTime(int? endTime) {
    fbBuilder.addUint32(2, endTime);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class AudioSegmentObjectBuilder extends fb.ObjectBuilder {
  final int? _index;
  final int? _startTime;
  final int? _endTime;

  AudioSegmentObjectBuilder({
    int? index,
    int? startTime,
    int? endTime,
  })  : _index = index,
        _startTime = startTime,
        _endTime = endTime;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(fb.Builder fbBuilder) {
    fbBuilder.startTable(3);
    fbBuilder.addUint32(0, _index);
    fbBuilder.addUint32(1, _startTime);
    fbBuilder.addUint32(2, _endTime);
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String? fileIdentifier]) {
    final fbBuilder = fb.Builder(deduplicateTables: false);
    fbBuilder.finish(finish(fbBuilder), fileIdentifier);
    return fbBuilder.buffer;
  }
}

class Page {
  Page._(this._bc, this._bcOffset);
  factory Page(List<int> bytes) {
    final rootRef = fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<Page> reader = _PageReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  int get firstVerseId =>
      const fb.Int32Reader().vTableGet(_bc, _bcOffset, 4, 0);
  int get lastVerseId => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 6, 0);
  List<int>? get juzId => const fb.ListReader<int>(fb.Int32Reader())
      .vTableGetNullable(_bc, _bcOffset, 8);
  List<int>? get surahId => const fb.ListReader<int>(fb.Int32Reader())
      .vTableGetNullable(_bc, _bcOffset, 10);
  int get pageId => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 12, 0);
  List<int>? get font =>
      const fb.Uint8ListReader().vTableGetNullable(_bc, _bcOffset, 14);

  @override
  String toString() {
    return 'Page{firstVerseId: ${firstVerseId}, lastVerseId: ${lastVerseId}, juzId: ${juzId}, surahId: ${surahId}, pageId: ${pageId}, font: ${font}}';
  }
}

class _PageReader extends fb.TableReader<Page> {
  const _PageReader();

  @override
  Page createObject(fb.BufferContext bc, int offset) => Page._(bc, offset);
}

class PageBuilder {
  PageBuilder(this.fbBuilder);

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable(6);
  }

  int addFirstVerseId(int? firstVerseId) {
    fbBuilder.addInt32(0, firstVerseId);
    return fbBuilder.offset;
  }

  int addLastVerseId(int? lastVerseId) {
    fbBuilder.addInt32(1, lastVerseId);
    return fbBuilder.offset;
  }

  int addJuzIdOffset(int? offset) {
    fbBuilder.addOffset(2, offset);
    return fbBuilder.offset;
  }

  int addSurahIdOffset(int? offset) {
    fbBuilder.addOffset(3, offset);
    return fbBuilder.offset;
  }

  int addPageId(int? pageId) {
    fbBuilder.addInt32(4, pageId);
    return fbBuilder.offset;
  }

  int addFontOffset(int? offset) {
    fbBuilder.addOffset(5, offset);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class PageObjectBuilder extends fb.ObjectBuilder {
  final int? _firstVerseId;
  final int? _lastVerseId;
  final List<int>? _juzId;
  final List<int>? _surahId;
  final int? _pageId;
  final List<int>? _font;

  PageObjectBuilder({
    int? firstVerseId,
    int? lastVerseId,
    List<int>? juzId,
    List<int>? surahId,
    int? pageId,
    List<int>? font,
  })  : _firstVerseId = firstVerseId,
        _lastVerseId = lastVerseId,
        _juzId = juzId,
        _surahId = surahId,
        _pageId = pageId,
        _font = font;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(fb.Builder fbBuilder) {
    final int? juzIdOffset =
        _juzId == null ? null : fbBuilder.writeListInt32(_juzId!);
    final int? surahIdOffset =
        _surahId == null ? null : fbBuilder.writeListInt32(_surahId!);
    final int? fontOffset =
        _font == null ? null : fbBuilder.writeListUint8(_font!);
    fbBuilder.startTable(6);
    fbBuilder.addInt32(0, _firstVerseId);
    fbBuilder.addInt32(1, _lastVerseId);
    fbBuilder.addOffset(2, juzIdOffset);
    fbBuilder.addOffset(3, surahIdOffset);
    fbBuilder.addInt32(4, _pageId);
    fbBuilder.addOffset(5, fontOffset);
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String? fileIdentifier]) {
    final fbBuilder = fb.Builder(deduplicateTables: false);
    fbBuilder.finish(finish(fbBuilder), fileIdentifier);
    return fbBuilder.buffer;
  }
}

class Surah {
  Surah._(this._bc, this._bcOffset);
  factory Surah(List<int> bytes) {
    final rootRef = fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<Surah> reader = _SurahReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  int get surahId => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 4, 0);
  int get startingPage =>
      const fb.Int32Reader().vTableGet(_bc, _bcOffset, 6, 0);
  String? get name =>
      const fb.StringReader().vTableGetNullable(_bc, _bcOffset, 8);
  String? get surahType =>
      const fb.StringReader().vTableGetNullable(_bc, _bcOffset, 10);

  @override
  String toString() {
    return 'Surah{surahId: ${surahId}, startingPage: ${startingPage}, name: ${name}, surahType: ${surahType}}';
  }
}

class _SurahReader extends fb.TableReader<Surah> {
  const _SurahReader();

  @override
  Surah createObject(fb.BufferContext bc, int offset) => Surah._(bc, offset);
}

class SurahBuilder {
  SurahBuilder(this.fbBuilder);

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable(4);
  }

  int addSurahId(int? surahId) {
    fbBuilder.addInt32(0, surahId);
    return fbBuilder.offset;
  }

  int addStartingPage(int? startingPage) {
    fbBuilder.addInt32(1, startingPage);
    return fbBuilder.offset;
  }

  int addNameOffset(int? offset) {
    fbBuilder.addOffset(2, offset);
    return fbBuilder.offset;
  }

  int addSurahTypeOffset(int? offset) {
    fbBuilder.addOffset(3, offset);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class SurahObjectBuilder extends fb.ObjectBuilder {
  final int? _surahId;
  final int? _startingPage;
  final String? _name;
  final String? _surahType;

  SurahObjectBuilder({
    int? surahId,
    int? startingPage,
    String? name,
    String? surahType,
  })  : _surahId = surahId,
        _startingPage = startingPage,
        _name = name,
        _surahType = surahType;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(fb.Builder fbBuilder) {
    final int? nameOffset =
        _name == null ? null : fbBuilder.writeString(_name!);
    final int? surahTypeOffset =
        _surahType == null ? null : fbBuilder.writeString(_surahType!);
    fbBuilder.startTable(4);
    fbBuilder.addInt32(0, _surahId);
    fbBuilder.addInt32(1, _startingPage);
    fbBuilder.addOffset(2, nameOffset);
    fbBuilder.addOffset(3, surahTypeOffset);
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String? fileIdentifier]) {
    final fbBuilder = fb.Builder(deduplicateTables: false);
    fbBuilder.finish(finish(fbBuilder), fileIdentifier);
    return fbBuilder.buffer;
  }
}

class Juz {
  Juz._(this._bc, this._bcOffset);
  factory Juz(List<int> bytes) {
    final rootRef = fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<Juz> reader = _JuzReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  int get juzId => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 4, 0);
  int get startingPage =>
      const fb.Int32Reader().vTableGet(_bc, _bcOffset, 6, 0);
  Verse? get firstVerse => Verse.reader.vTableGetNullable(_bc, _bcOffset, 8);
  int get rubElHizb => const fb.Int32Reader().vTableGet(_bc, _bcOffset, 10, 0);

  @override
  String toString() {
    return 'Juz{juzId: ${juzId}, startingPage: ${startingPage}, firstVerse: ${firstVerse}, rubElHizb: ${rubElHizb}}';
  }
}

class _JuzReader extends fb.TableReader<Juz> {
  const _JuzReader();

  @override
  Juz createObject(fb.BufferContext bc, int offset) => Juz._(bc, offset);
}

class JuzBuilder {
  JuzBuilder(this.fbBuilder);

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable(4);
  }

  int addJuzId(int? juzId) {
    fbBuilder.addInt32(0, juzId);
    return fbBuilder.offset;
  }

  int addStartingPage(int? startingPage) {
    fbBuilder.addInt32(1, startingPage);
    return fbBuilder.offset;
  }

  int addFirstVerseOffset(int? offset) {
    fbBuilder.addOffset(2, offset);
    return fbBuilder.offset;
  }

  int addRubElHizb(int? rubElHizb) {
    fbBuilder.addInt32(3, rubElHizb);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class JuzObjectBuilder extends fb.ObjectBuilder {
  final int? _juzId;
  final int? _startingPage;
  final VerseObjectBuilder? _firstVerse;
  final int? _rubElHizb;

  JuzObjectBuilder({
    int? juzId,
    int? startingPage,
    VerseObjectBuilder? firstVerse,
    int? rubElHizb,
  })  : _juzId = juzId,
        _startingPage = startingPage,
        _firstVerse = firstVerse,
        _rubElHizb = rubElHizb;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(fb.Builder fbBuilder) {
    final int? firstVerseOffset = _firstVerse?.getOrCreateOffset(fbBuilder);
    fbBuilder.startTable(4);
    fbBuilder.addInt32(0, _juzId);
    fbBuilder.addInt32(1, _startingPage);
    fbBuilder.addOffset(2, firstVerseOffset);
    fbBuilder.addInt32(3, _rubElHizb);
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String? fileIdentifier]) {
    final fbBuilder = fb.Builder(deduplicateTables: false);
    fbBuilder.finish(finish(fbBuilder), fileIdentifier);
    return fbBuilder.buffer;
  }
}

class FontFile {
  FontFile._(this._bc, this._bcOffset);
  factory FontFile(List<int> bytes) {
    final rootRef = fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<FontFile> reader = _FontFileReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  String? get name =>
      const fb.StringReader().vTableGetNullable(_bc, _bcOffset, 4);
  List<int>? get data =>
      const fb.Uint8ListReader().vTableGetNullable(_bc, _bcOffset, 6);

  @override
  String toString() {
    return 'FontFile{name: ${name}, data: ${data}}';
  }
}

class _FontFileReader extends fb.TableReader<FontFile> {
  const _FontFileReader();

  @override
  FontFile createObject(fb.BufferContext bc, int offset) =>
      FontFile._(bc, offset);
}

class FontFileBuilder {
  FontFileBuilder(this.fbBuilder);

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable(2);
  }

  int addNameOffset(int? offset) {
    fbBuilder.addOffset(0, offset);
    return fbBuilder.offset;
  }

  int addDataOffset(int? offset) {
    fbBuilder.addOffset(1, offset);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class FontFileObjectBuilder extends fb.ObjectBuilder {
  final String? _name;
  final List<int>? _data;

  FontFileObjectBuilder({
    String? name,
    List<int>? data,
  })  : _name = name,
        _data = data;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(fb.Builder fbBuilder) {
    final int? nameOffset =
        _name == null ? null : fbBuilder.writeString(_name!);
    final int? dataOffset =
        _data == null ? null : fbBuilder.writeListUint8(_data!);
    fbBuilder.startTable(2);
    fbBuilder.addOffset(0, nameOffset);
    fbBuilder.addOffset(1, dataOffset);
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String? fileIdentifier]) {
    final fbBuilder = fb.Builder(deduplicateTables: false);
    fbBuilder.finish(finish(fbBuilder), fileIdentifier);
    return fbBuilder.buffer;
  }
}

class Quran {
  Quran._(this._bc, this._bcOffset);
  factory Quran(List<int> bytes) {
    final rootRef = fb.BufferContext.fromBytes(bytes);
    return reader.read(rootRef, 0);
  }

  static const fb.Reader<Quran> reader = _QuranReader();

  final fb.BufferContext _bc;
  final int _bcOffset;

  List<Verse>? get verses => const fb.ListReader<Verse>(Verse.reader)
      .vTableGetNullable(_bc, _bcOffset, 4);
  List<Page>? get pages => const fb.ListReader<Page>(Page.reader)
      .vTableGetNullable(_bc, _bcOffset, 6);
  List<Surah>? get surahs => const fb.ListReader<Surah>(Surah.reader)
      .vTableGetNullable(_bc, _bcOffset, 8);
  List<Juz>? get juzs => const fb.ListReader<Juz>(Juz.reader)
      .vTableGetNullable(_bc, _bcOffset, 10);
  List<FontFile>? get otherFonts =>
      const fb.ListReader<FontFile>(FontFile.reader)
          .vTableGetNullable(_bc, _bcOffset, 12);
  List<int>? get globalFont =>
      const fb.Uint8ListReader().vTableGetNullable(_bc, _bcOffset, 14);

  @override
  String toString() {
    return 'Quran{verses: ${verses}, pages: ${pages}, surahs: ${surahs}, juzs: ${juzs}, otherFonts: ${otherFonts}, globalFont: ${globalFont}}';
  }
}

class _QuranReader extends fb.TableReader<Quran> {
  const _QuranReader();

  @override
  Quran createObject(fb.BufferContext bc, int offset) => Quran._(bc, offset);
}

class QuranBuilder {
  QuranBuilder(this.fbBuilder);

  final fb.Builder fbBuilder;

  void begin() {
    fbBuilder.startTable(6);
  }

  int addVersesOffset(int? offset) {
    fbBuilder.addOffset(0, offset);
    return fbBuilder.offset;
  }

  int addPagesOffset(int? offset) {
    fbBuilder.addOffset(1, offset);
    return fbBuilder.offset;
  }

  int addSurahsOffset(int? offset) {
    fbBuilder.addOffset(2, offset);
    return fbBuilder.offset;
  }

  int addJuzsOffset(int? offset) {
    fbBuilder.addOffset(3, offset);
    return fbBuilder.offset;
  }

  int addOtherFontsOffset(int? offset) {
    fbBuilder.addOffset(4, offset);
    return fbBuilder.offset;
  }

  int addGlobalFontOffset(int? offset) {
    fbBuilder.addOffset(5, offset);
    return fbBuilder.offset;
  }

  int finish() {
    return fbBuilder.endTable();
  }
}

class QuranObjectBuilder extends fb.ObjectBuilder {
  final List<VerseObjectBuilder>? _verses;
  final List<PageObjectBuilder>? _pages;
  final List<SurahObjectBuilder>? _surahs;
  final List<JuzObjectBuilder>? _juzs;
  final List<FontFileObjectBuilder>? _otherFonts;
  final List<int>? _globalFont;

  QuranObjectBuilder({
    List<VerseObjectBuilder>? verses,
    List<PageObjectBuilder>? pages,
    List<SurahObjectBuilder>? surahs,
    List<JuzObjectBuilder>? juzs,
    List<FontFileObjectBuilder>? otherFonts,
    List<int>? globalFont,
  })  : _verses = verses,
        _pages = pages,
        _surahs = surahs,
        _juzs = juzs,
        _otherFonts = otherFonts,
        _globalFont = globalFont;

  /// Finish building, and store into the [fbBuilder].
  @override
  int finish(fb.Builder fbBuilder) {
    final int? versesOffset = _verses == null
        ? null
        : fbBuilder.writeList(
            _verses!.map((b) => b.getOrCreateOffset(fbBuilder)).toList());
    final int? pagesOffset = _pages == null
        ? null
        : fbBuilder.writeList(
            _pages!.map((b) => b.getOrCreateOffset(fbBuilder)).toList());
    final int? surahsOffset = _surahs == null
        ? null
        : fbBuilder.writeList(
            _surahs!.map((b) => b.getOrCreateOffset(fbBuilder)).toList());
    final int? juzsOffset = _juzs == null
        ? null
        : fbBuilder.writeList(
            _juzs!.map((b) => b.getOrCreateOffset(fbBuilder)).toList());
    final int? otherFontsOffset = _otherFonts == null
        ? null
        : fbBuilder.writeList(
            _otherFonts!.map((b) => b.getOrCreateOffset(fbBuilder)).toList());
    final int? globalFontOffset =
        _globalFont == null ? null : fbBuilder.writeListUint8(_globalFont!);
    fbBuilder.startTable(6);
    fbBuilder.addOffset(0, versesOffset);
    fbBuilder.addOffset(1, pagesOffset);
    fbBuilder.addOffset(2, surahsOffset);
    fbBuilder.addOffset(3, juzsOffset);
    fbBuilder.addOffset(4, otherFontsOffset);
    fbBuilder.addOffset(5, globalFontOffset);
    return fbBuilder.endTable();
  }

  /// Convenience method to serialize to byte list.
  @override
  Uint8List toBytes([String? fileIdentifier]) {
    final fbBuilder = fb.Builder(deduplicateTables: false);
    fbBuilder.finish(finish(fbBuilder), fileIdentifier);
    return fbBuilder.buffer;
  }
}
