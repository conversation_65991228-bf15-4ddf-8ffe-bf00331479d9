import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/widget/navigation/juz_navigation_item.dart';
import 'package:scrollview_observer/scrollview_observer.dart';

class JuzNavigationList extends ConsumerStatefulWidget {
  final bool scrollable;
  final int? initialPosition;
  final Function(int pageNumber) onNavigationItemSelected;

  const JuzNavigationList({
    super.key,
    required this.scrollable,
    this.initialPosition,
    required this.onNavigationItemSelected,
  });

  @override
  ConsumerState<JuzNavigationList> createState() => _JuzNavigationListState();
}

class _JuzNavigationListState extends ConsumerState<JuzNavigationList> {
  final ScrollController scrollController = ScrollController();
  late final ListObserverController observerController = ListObserverController(
    controller: scrollController,
  );

  @override
  void initState() {
    if (widget.initialPosition != null) {
      Future(() {
        observerController.jumpTo(
            index: widget.initialPosition!, isFixedHeight: true);
      });
    }

    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final surahListAsync = ref.watch(surahListProvider);
    final juzListAsync = ref.watch(juzListProvider);

    if (surahListAsync.hasError || juzListAsync.hasError) {
      return Center(
        child: Text(
          surahListAsync.error?.toString() ??
              juzListAsync.error?.toString() ??
              '',
        ),
      );
    } else if (surahListAsync.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    } else if (juzListAsync.isLoading) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 8),
            Text(context.tr('quran.downloading')),
          ],
        ),
      );
    }

    final surahList = surahListAsync.value!;
    final juzList = juzListAsync.value!;

    return ListViewObserver(
      controller: observerController,
      child: ListView.builder(
        controller: scrollController,
        padding: const EdgeInsets.only(top: 16, bottom: 16),
        itemCount: juzList.length,
        physics: widget.scrollable
            ? const AlwaysScrollableScrollPhysics()
            : const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final juz = juzList[index];
          final startingSurahId = juz.firstVerse?.surahId;
          final startingSurah =
              (startingSurahId != null) ? surahList[startingSurahId - 1] : null;
          String startingVerseText =
              juz.firstVerse?.words?.map((word) => word.codeV1).join('') ?? '';
          startingVerseText =
              startingVerseText.substring(0, min(5, startingVerseText.length));

          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: JuzNavigationItem(
              juz: juz,
              startingSurahName: startingSurah?.nameComplex ?? '',
              startingVerseText: startingVerseText,
              isSelected: (widget.initialPosition != null)
                  ? index == widget.initialPosition
                  : null,
              onTap: widget.onNavigationItemSelected,
            ),
          );
        },
      ),
    );
  }
}
