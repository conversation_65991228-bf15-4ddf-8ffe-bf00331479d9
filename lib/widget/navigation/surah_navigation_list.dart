import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/widget/navigation/surah_navigation_item.dart';
import 'package:scrollview_observer/scrollview_observer.dart';

class SurahNavigationList extends ConsumerStatefulWidget {
  final bool scrollable;
  final int? initialPosition;
  final Function(int pageNumber) onNavigationItemSelected;

  const SurahNavigationList({
    super.key,
    required this.scrollable,
    this.initialPosition,
    required this.onNavigationItemSelected,
  });

  @override
  ConsumerState<SurahNavigationList> createState() =>
      _SurahNavigationListState();
}

class _SurahNavigationListState extends ConsumerState<SurahNavigationList> {
  final ScrollController scrollController = ScrollController();
  late final ListObserverController observerController = ListObserverController(
    controller: scrollController,
  );

  @override
  void initState() {
    if (widget.initialPosition != null) {
      Future(() {
        observerController.jumpTo(
            index: widget.initialPosition!, isFixedHeight: true);
      });
    }

    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final surahListAsync = ref.watch(surahListProvider);

    return surahListAsync.when(
      data: (surahList) {
        return ListViewObserver(
          controller: observerController,
          child: ListView.builder(
            controller: scrollController,
            padding: const EdgeInsets.only(top: 16, bottom: 16),
            itemCount: surahList.length,
            physics: widget.scrollable
                ? const AlwaysScrollableScrollPhysics()
                : const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final surah = surahList[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: SurahNavigationItem(
                  number: index + 1,
                  surah: surah,
                  isSelected: (widget.initialPosition != null)
                      ? index == widget.initialPosition
                      : null,
                  onTap: widget.onNavigationItemSelected,
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
        ),
      ),
    );
  }
}
