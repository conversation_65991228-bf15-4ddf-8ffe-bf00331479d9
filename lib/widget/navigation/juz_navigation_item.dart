import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/int_extension.dart';
import 'package:mushafi/extension/juz_number_formatter.dart';
import 'package:mushafi/generated/flatbuffer_flatbuffer_generated.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/constants.dart';

class JuzNavigationItem extends ConsumerWidget {
  final Juz juz;
  final String startingSurahName;
  final String startingVerseText;
  final bool? isSelected;
  final Function(int pageNumber) onTap;

  const JuzNavigationItem({
    super.key,
    required this.juz,
    required this.startingSurahName,
    required this.startingVerseText,
    this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);

    final design = mushafDesignAsync.value?.design;
    final highlightColor = design?.highLightFadedColor ??
        ColorResource.defaultHighlightFaded(context.isDarkMode);

    return InkWell(
      onTap: () {
        onTap(juz.startingPage);
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
              color: ColorResource.border(context.isDarkMode), width: 1.0),
          borderRadius: BorderRadius.circular(12.0),
          color: (isSelected == true)
              ? highlightColor
              : ColorResource.backgroundWhite(context.isDarkMode),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
          child: Row(
            children: [
              Visibility(
                visible: !context.isArabic,
                child: Text(
                  'Juz ${juz.juzId}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  String.fromCharCode(juzCharCode) +
                      juz.juzId.toJuzNumberCharCode(),
                  textDirection: ui.TextDirection.rtl,
                  style: TextStyle(
                    fontSize: 24,
                    fontFamily: 'QuranCalligraphy',
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Stack(
                children: [
                  const Visibility(
                    visible: false,
                    maintainSize: true,
                    maintainAnimation: true,
                    maintainState: true,
                    child: Text(
                      '9999',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                  Positioned.fill(
                    child: Align(
                      alignment: (context.isArabic)
                          ? Alignment.centerLeft
                          : Alignment.centerRight,
                      child: Text(
                        juz.startingPage.toLocaleString(context.locale),
                        style: TextStyle(
                          fontSize: 12,
                          color: ColorResource.textGrey(context.isDarkMode),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
