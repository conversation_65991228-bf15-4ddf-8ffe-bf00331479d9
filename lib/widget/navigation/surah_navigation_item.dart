import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/int_extension.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/resource/color_resource.dart';

class SurahNavigationItem extends ConsumerWidget {
  final int number;
  final Surah surah;
  final bool? isSelected;
  final Function(int pageNumber) onTap;

  const SurahNavigationItem({
    super.key,
    required this.number,
    required this.surah,
    this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);

    final design = mushafDesignAsync.value?.design;
    final highlightColor = design?.highLightFadedColor ??
        ColorResource.defaultHighlightFaded(context.isDarkMode);

    final formattedSurahNumber = surah.id.toString().padLeft(3, '0');

    return InkWell(
      onTap: () {
        onTap(surah.pages.first);
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
              color: ColorResource.border(context.isDarkMode), width: 1.0),
          borderRadius: BorderRadius.circular(12.0),
          color: (isSelected == true)
              ? highlightColor
              : ColorResource.backgroundWhite(context.isDarkMode),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
          child: Row(
            children: [
              Stack(
                children: [
                  const Visibility(
                    visible: false,
                    maintainSize: true,
                    maintainAnimation: true,
                    maintainState: true,
                    child: Text(
                      '999',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                  Text(
                    number.toLocaleString(context.locale),
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorResource.textGrey(context.isDarkMode),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 8),
              Visibility(
                visible: !context.isArabic,
                child: Text(
                  surah.nameComplex,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
              Expanded(
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    formattedSurahNumber,
                    style: TextStyle(
                      fontFamily: 'SurahNames',
                      fontSize: 24,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Stack(
                children: [
                  const Visibility(
                    visible: false,
                    maintainSize: true,
                    maintainAnimation: true,
                    maintainState: true,
                    child: Text(
                      '9999',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                  Positioned.fill(
                    child: Align(
                      alignment: (context.isArabic)
                          ? Alignment.centerLeft
                          : Alignment.centerRight,
                      child: Text(
                        surah.pages.first.toLocaleString(context.locale),
                        style: TextStyle(
                          fontSize: 12,
                          color: ColorResource.textGrey(context.isDarkMode),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
