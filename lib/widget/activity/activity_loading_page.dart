import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/resource/color_resource.dart';

class ActivityLoadingPage extends StatelessWidget {
  final bool isDownloading;

  const ActivityLoadingPage(this.isDownloading, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: ColorResource.backgroundDefault(context.isDarkMode),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          (isDownloading)
              ? Text(context.tr('quran.downloading'))
              : Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(text: context.tr('activity.loading.title')),
                      const TextSpan(text: ',\n'),
                      TextSpan(
                          text: context.tr('activity.loading.description')),
                    ],
                  ),
                  style: TextStyle(
                    color: ColorResource.textDefault(context.isDarkMode),
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
        ],
      ),
    );
  }
}
