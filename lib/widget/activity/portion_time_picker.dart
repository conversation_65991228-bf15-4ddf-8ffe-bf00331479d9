import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/common/number_picker.dart';

class PortionTimePicker extends StatefulWidget {
  final int hourInitialValue;
  final int minuteInitialValue;
  final int meridiemInitialValue;
  final void Function(int hour, int minute, String meridiem) onValueChanged;

  const PortionTimePicker({
    super.key,
    required this.hourInitialValue,
    required this.minuteInitialValue,
    required this.meridiemInitialValue,
    required this.onValueChanged,
  });

  @override
  State<PortionTimePicker> createState() => _PortionTimePickerState();
}

class _PortionTimePickerState extends State<PortionTimePicker> {
  bool isPickerVisible = false;

  // todo remove, use widget values instead
  int selectedHourValue = 0;
  int selectedMinuteValue = 0;
  int selectedMeridiemValue = 0;

  final hourList = List.generate(12, (index) => index + 1);
  final minuteList = List.generate(60, (index) => index);
  final meridiemList = DateFormat().dateSymbols.AMPMS;

  late final hourDisplayedValues = hourList.map((e) => e.toString()).toList();
  late final minuteDisplayedValues =
      minuteList.map((e) => e.toString().padLeft(2, '0')).toList();
  late final meridiemDisplayedValues =
      meridiemList.map((e) => e.toString()).toList();

  @override
  void initState() {
    super.initState();

    selectedHourValue = widget.hourInitialValue;
    selectedMinuteValue = widget.minuteInitialValue;
    selectedMeridiemValue = widget.meridiemInitialValue;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => setState(() => isPickerVisible = !isPickerVisible),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Time',
                style: TextStyle(
                  color: ColorResource.textGrey(context.isDarkMode),
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  '${hourDisplayedValues[selectedHourValue]}:${minuteDisplayedValues[selectedMinuteValue]} ${meridiemDisplayedValues[selectedMeridiemValue]}',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (isPickerVisible) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: NumberPicker(
                  minValue: 0,
                  maxValue: hourList.length - 1,
                  value: widget.hourInitialValue,
                  displayedValues: hourDisplayedValues,
                  onValueChanged: (value) {
                    setState(() => selectedHourValue = value);
                  },
                ),
              ),
              Expanded(
                child: NumberPicker(
                  minValue: 0,
                  maxValue: minuteList.length - 1,
                  value: widget.minuteInitialValue,
                  displayedValues: minuteDisplayedValues,
                  onValueChanged: (value) {
                    setState(() => selectedMinuteValue = value);
                  },
                ),
              ),
              Expanded(
                child: NumberPicker(
                  minValue: 0,
                  maxValue: meridiemList.length - 1,
                  value: widget.meridiemInitialValue,
                  displayedValues: meridiemDisplayedValues,
                  onValueChanged: (value) {
                    setState(() => selectedMeridiemValue = value);
                  },
                ),
              ),
            ],
          ),
        ]
      ],
    );
  }
}
