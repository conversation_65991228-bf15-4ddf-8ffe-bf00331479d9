import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/int_extension.dart';
import 'package:mushafi/extension/verse_key_parser.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/model/track/track_range_portion.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/resource/color_resource.dart';

class NamelessActivityItem extends StatelessWidget {
  final Track track;
  final List<Surah> surahList;
  final Function(Track track) onTap;
  final VoidCallback onMorePressed;
  final VoidCallback onEditPressed;

  const NamelessActivityItem({
    super.key,
    required this.track,
    required this.surahList,
    required this.onTap,
    required this.onMorePressed,
    required this.onEditPressed,
  });

  @override
  Widget build(BuildContext context) {
    final range = track.range;

    final startSurah = surahList.elementAtOrNull(range.startSurahNumber - 1);
    final startSurahName =
        (context.isArabic) ? startSurah?.nameArabic : startSurah?.nameComplex;

    final endSurah = surahList.elementAtOrNull(range.endSurahNumber - 1);
    final endSurahName =
        (context.isArabic) ? endSurah?.nameArabic : endSurah?.nameComplex;

    final title = startSurahName;

    final detail = switch (track.type) {
      null => '',
      TrackType.listening => switch (track.rangePortion) {
          TrackRangePortion.surah => title,
          TrackRangePortion.juz =>
            context.tr('activity_input.juz_number', args: [
              range.startJuzNumber.toLocaleString(context.locale),
            ]),
          TrackRangePortion.range =>
            (range.endSurahNumber > range.startSurahNumber)
                ? context.tr('activity_range.verse_range_with_surah', args: [
                    startSurahName ?? '',
                    range.startVerseNumber.toLocaleString(context.locale),
                    endSurahName ?? '',
                    range.endVerseNumber.toLocaleString(context.locale),
                  ])
                : context.tr('activity.range.ayah', args: [
                    range.startVerseNumber.toLocaleString(context.locale),
                    range.endVerseNumber.toLocaleString(context.locale),
                  ]),
          TrackRangePortion.page =>
            (range.endPageNumber > range.startPageNumber)
                ? context.tr('activity_range.page_range', args: [
                    range.startPageNumber.toLocaleString(context.locale),
                    range.endPageNumber.toLocaleString(context.locale),
                  ])
                : context.tr('activity_range.page', args: [
                    range.startPageNumber.toLocaleString(context.locale),
                  ]),
        },
      TrackType.readingHabitual => context.tr('activity_input.ayah', args: [
          range.startVerseNumber.toLocaleString(context.locale),
        ]),
      TrackType.readingCoverToCover =>
        context.tr('activity.range.last_page', args: [
          range.startPageNumber.toLocaleString(context.locale),
        ]),
      TrackType.readingWithAi || TrackType.memorizing => switch (
            track.rangePortion) {
          TrackRangePortion.surah => title,
          TrackRangePortion.juz =>
            context.tr('activity_input.juz_number', args: [
              range.startJuzNumber.toLocaleString(context.locale),
            ]),
          TrackRangePortion.range =>
            (range.endSurahNumber > range.startSurahNumber)
                ? context.tr('activity_range.verse_range_with_surah', args: [
                    startSurahName ?? '',
                    range.startVerseNumber.toLocaleString(context.locale),
                    endSurahName ?? '',
                    range.endVerseNumber.toLocaleString(context.locale),
                  ])
                : context.tr('activity.range.ayah', args: [
                    range.startVerseNumber.toLocaleString(context.locale),
                    range.endVerseNumber.toLocaleString(context.locale),
                  ]),
          TrackRangePortion.page =>
            (range.endPageNumber > range.startPageNumber)
                ? context.tr('activity_range.page_range', args: [
                    range.startPageNumber.toLocaleString(context.locale),
                    range.endPageNumber.toLocaleString(context.locale),
                  ])
                : context.tr('activity_range.page', args: [
                    range.startPageNumber.toLocaleString(context.locale),
                  ]),
        },
    };

    final typeText = switch (track.type) {
      null => '',
      TrackType.listening => context.tr('activity.type.listening'),
      TrackType.readingHabitual => context.tr('activity.type.habitual'),
      TrackType.readingCoverToCover =>
        context.tr('activity.type.cover_to_cover'),
      TrackType.readingWithAi => context.tr('activity.type.recite_with_ai'),
      TrackType.memorizing => context.tr('activity.type.memorizing'),
    };

    final icon = switch (track.type) {
      null => null,
      TrackType.listening => Assets.svgsIcHeadphone,
      TrackType.readingHabitual => Assets.svgsIcHabitual,
      TrackType.readingCoverToCover => Assets.svgsIcReading,
      TrackType.readingWithAi => Assets.svgsIcAi,
      TrackType.memorizing => Assets.svgsIcColumn,
    };

    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () {
              onTap(track);
            },
            child: Container(
              decoration: BoxDecoration(
                color: track.design?.activityCardBackgroundColor,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: track.design?.activityCardBorderColor ??
                      ColorResource.border(context.isDarkMode),
                  width: 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          title ?? '',
                          style: TextStyle(
                            fontSize: 16,
                            color:
                                ColorResource.textDefault(context.isDarkMode),
                          ),
                        ),
                        const Spacer(),
                        InkWell(
                          onTap: onEditPressed,
                          child: SvgPicture.asset(
                            Assets.svgsIcEdit,
                            width: 24,
                            height: 24,
                            colorFilter: ColorFilter.mode(
                              ColorResource.textDefault(context.isDarkMode),
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Text(
                      detail ?? '',
                      style: TextStyle(
                        fontSize: 12,
                        color: ColorResource.textGrey(context.isDarkMode),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      children: [
                        if (icon != null) ...[
                          SvgPicture.asset(icon),
                        ],
                        Padding(
                          padding: EdgeInsets.only(
                            left: (context.isArabic) ? 0 : 4,
                            right: (context.isArabic) ? 4 : 0,
                          ),
                          child: Text(
                            typeText,
                            style: TextStyle(
                              fontSize: 12,
                              color: ColorResource.textGrey(context.isDarkMode),
                            ),
                          ),
                        ),
                        ...buildAdditionalDetails(context),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> buildAdditionalDetails(BuildContext context) {
    final reciterName = (context.isArabic)
        ? track.reciter?.arabicShortName
        : track.reciter?.shortName;

    switch (track.type) {
      case TrackType.listening:
        final List<Widget> widgetList = [];
        if (reciterName != null) {
          widgetList.addAll([
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 4,
              ),
              child: Text(
                context.tr('activity.bullet'),
                style: TextStyle(
                  color: ColorResource.textGrey(context.isDarkMode),
                ),
              ),
            ),
            Text(
              reciterName,
              style: TextStyle(
                fontSize: 12,
                color: ColorResource.textGrey(context.isDarkMode),
              ),
            ),
          ]);
        }

        if (track.reciteVerseCount > 1) {
          widgetList.addAll([
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 4,
              ),
              child: Text(
                context.tr('activity.bullet'),
                style: TextStyle(
                  color: ColorResource.textGrey(context.isDarkMode),
                ),
              ),
            ),
            Text(
              context.tr(
                'activity.verse_repeats',
                args: [track.reciteVerseCount.toLocaleString(context.locale)],
              ),
              style: TextStyle(
                fontSize: 12,
                color: ColorResource.textGrey(context.isDarkMode),
              ),
            ),
          ]);
        }

        if (track.isLoop) {
          widgetList.addAll(
            [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 4,
                ),
                child: Text(
                  context.tr('activity.bullet'),
                  style: TextStyle(
                    color: ColorResource.textGrey(context.isDarkMode),
                  ),
                ),
              ),
              Text(
                context.tr('activity.loop'),
                style: TextStyle(
                  fontSize: 12,
                  color: ColorResource.textGrey(context.isDarkMode),
                ),
              ),
            ],
          );
        }

        return widgetList;
      case TrackType.readingCoverToCover:
      case TrackType.readingHabitual:
        final List<Widget> widgetList = [];
        if (track.lastHighlightedVerseKey != null) {
          final verseKey = track.lastHighlightedVerseKey!;
          final surahNumber = verseKey.parseSurahNumber();
          final verseNumber = verseKey.parseVerseNumber();
          final surah = surahList[surahNumber - 1];
          final surahName =
              (context.isArabic) ? surah.nameArabic : surah.nameComplex;

          widgetList.addAll(
            [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 4,
                ),
                child: Text(
                  context.tr('activity.bullet'),
                  style: TextStyle(
                    color: ColorResource.textGrey(context.isDarkMode),
                  ),
                ),
              ),
              Text(
                context.tr(
                  'activity.detail.last_verse_read',
                  args: [surahName, verseNumber.toLocaleString(context.locale)],
                ),
                style: TextStyle(
                  fontSize: 12,
                  color: ColorResource.textGrey(context.isDarkMode),
                ),
              ),
            ],
          );
        }

        return widgetList;
      case TrackType.readingWithAi:
      case TrackType.memorizing:
      case null:
        return [];
    }
  }
}
