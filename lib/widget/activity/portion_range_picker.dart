import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/activity/activity_input_provider.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/constants.dart';
import 'package:mushafi/widget/common/number_picker.dart';

class PortionRangePicker extends ConsumerStatefulWidget {
  final String surahRangeId;
  final String verseRangeId;
  final List<Surah> surahList;
  final String title;
  final bool isPickerVisible;
  final void Function(int surahValue) onSurahValueChanged;
  final void Function(int verseValue) onVerseValueChanged;
  final void Function(bool isVisible) onPickerVisibilityChanged;

  const PortionRangePicker({
    super.key,
    required this.surahRangeId,
    required this.verseRangeId,
    required this.surahList,
    required this.title,
    required this.isPickerVisible,
    required this.onSurahValueChanged,
    required this.onVerseValueChanged,
    required this.onPickerVisibilityChanged,
  });

  @override
  ConsumerState<PortionRangePicker> createState() => _PortionRangePickerState();
}

class _PortionRangePickerState extends ConsumerState<PortionRangePicker> {
  List<String> surahDisplayedValues = [];
  List<String> verseDisplayedValues = [];

  int surahMinValue = 0;
  int verseMinValue = 0;
  int verseMaxValue = 0;

  late FixedExtentScrollController surahScrollController;
  late FixedExtentScrollController verseScrollController;

  @override
  void initState() {
    super.initState();

    final surahValue = ref.read(pickerValueProvider(widget.surahRangeId));
    final verseValue = ref.read(pickerValueProvider(widget.verseRangeId));

    surahScrollController =
        FixedExtentScrollController(initialItem: surahValue);
    verseScrollController =
        FixedExtentScrollController(initialItem: verseValue);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        surahDisplayedValues = widget.surahList.map((surah) {
          return (context.isArabic) ? surah.nameArabic : surah.nameComplex;
        }).toList();
      });
    });

    ref.listenManual(pickerValueProvider(widget.surahRangeId),
        (prevSurahValue, nextSurahValue) {
      if (widget.surahRangeId == endSurahPickerId) {
        final startVerseValue =
            ref.read(pickerValueProvider(startVersePickerId));

        final endSurah = widget.surahList.elementAtOrNull(nextSurahValue);
        final endVerseValue =
            (endSurah != null) ? (endSurah.versesCount - 1) : startVerseValue;

        verseScrollController.jumpToItem(endVerseValue);
      } else {
        verseScrollController.jumpToItem(0);
      }
    });
  }

  @override
  void didUpdateWidget(PortionRangePicker oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isPickerVisible != oldWidget.isPickerVisible &&
        widget.isPickerVisible) {
      final surahValue = ref.read(pickerValueProvider(widget.surahRangeId));
      final verseValue = ref.read(pickerValueProvider(widget.verseRangeId));

      surahScrollController.dispose();
      verseScrollController.dispose();

      surahScrollController =
          FixedExtentScrollController(initialItem: surahValue);
      verseScrollController =
          FixedExtentScrollController(initialItem: verseValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    final surahValue = ref.watch(pickerValueProvider(widget.surahRangeId));
    final verseValue = ref.watch(pickerValueProvider(widget.verseRangeId));

    final surah = widget.surahList[surahValue];
    verseDisplayedValues = List.generate(surah.versesCount, (index) {
      final number = (context.isArabic)
          ? NumberFormat('#', 'ar_EG').format(index + 1)
          : (index + 1).toString();
      return context.tr('activity_input.ayah', args: [number]);
    });

    verseMaxValue = surah.versesCount;

    if (widget.surahRangeId == endSurahPickerId) {
      final startSurahValue = ref.read(pickerValueProvider(startSurahPickerId));
      final startVerseValue = ref.read(pickerValueProvider(startVersePickerId));
      surahMinValue = startSurahValue;
      verseMinValue = (startSurahValue == surahValue) ? startVerseValue : 0;
    }

    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            final isPickerVisible = !widget.isPickerVisible;
            widget.onPickerVisibilityChanged(isPickerVisible);
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  color: ColorResource.textGrey(context.isDarkMode),
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  (surahDisplayedValues.isNotEmpty &&
                          verseDisplayedValues.isNotEmpty)
                      ? '${surahDisplayedValues[surahValue]}, ${verseDisplayedValues[verseValue]}'
                      : '',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (widget.isPickerVisible) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: NumberPicker(
                  minValue: surahMinValue,
                  maxValue: widget.surahList.length - 1,
                  value: surahValue,
                  displayedValues: surahDisplayedValues,
                  scrollController: surahScrollController,
                  onValueChanged: (value) => widget.onSurahValueChanged(value),
                ),
              ),
              Expanded(
                child: NumberPicker(
                  minValue: verseMinValue,
                  maxValue: verseMaxValue,
                  value: verseValue,
                  displayedValues: verseDisplayedValues,
                  scrollController: verseScrollController,
                  onValueChanged: (value) => widget.onVerseValueChanged(value),
                ),
              ),
            ],
          ),
        ]
      ],
    );
  }

  @override
  void dispose() {
    surahScrollController.dispose();
    verseScrollController.dispose();

    super.dispose();
  }
}
