import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/activity/activity_input_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/constants.dart';
import 'package:mushafi/widget/common/number_picker.dart';

class Picker extends ConsumerStatefulWidget {
  final String id;
  final String title;
  final int minValue;
  final int maxValue;
  final int value;
  final List<String> displayedValues;
  final bool isPickerVisible;
  final FixedExtentScrollController? scrollController;
  final void Function(int value) onValueChanged;
  final void Function(bool isVisible) onPickerVisibilityChanged;

  const Picker({
    super.key,
    required this.id,
    required this.title,
    required this.minValue,
    required this.maxValue,
    required this.value,
    required this.displayedValues,
    required this.isPickerVisible,
    this.scrollController,
    required this.onValueChanged,
    required this.onPickerVisibilityChanged,
  });

  @override
  ConsumerState<Picker> createState() => _PortionPickerState();
}

class _PortionPickerState extends ConsumerState<Picker> {
  late FixedExtentScrollController scrollController;

  int minValue = 0;

  @override
  void initState() {
    super.initState();

    minValue = widget.minValue;

    final value = ref.read(pickerValueProvider(widget.id));
    scrollController = FixedExtentScrollController(initialItem: value);
  }

  @override
  void didUpdateWidget(covariant Picker oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isPickerVisible != oldWidget.isPickerVisible &&
        widget.isPickerVisible) {
      final value = ref.read(pickerValueProvider(widget.id));

      scrollController.dispose();
      scrollController = FixedExtentScrollController(initialItem: value);
    }
  }

  @override
  Widget build(BuildContext context) {
    final value = ref.watch(pickerValueProvider(widget.id));
    final displayedValue =
        (value >= 0) ? widget.displayedValues.elementAtOrNull(value) : '';

    if (widget.id == endPagePickerId) {
      final startPageValue = ref.read(pickerValueProvider(startPagePickerId));
      minValue = startPageValue;
    }

    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            final isPickerVisible = !widget.isPickerVisible;
            widget.onPickerVisibilityChanged(isPickerVisible);
          },
          child: Row(
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  color: ColorResource.textGrey(context.isDarkMode),
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  displayedValue ?? '',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (widget.isPickerVisible) ...[
          const SizedBox(height: 8),
          NumberPicker(
            minValue: minValue,
            maxValue: widget.maxValue,
            value: value,
            displayedValues: widget.displayedValues,
            scrollController: widget.scrollController,
            onValueChanged: (value) {
              setState(() => widget.onValueChanged(value));
            },
          ),
        ]
      ],
    );
  }

  @override
  void dispose() {
    scrollController.dispose();

    super.dispose();
  }
}
