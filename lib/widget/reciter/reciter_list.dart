import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/nullable.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/recitation/reciter.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';

class ReciterList extends ConsumerStatefulWidget {
  final bool applyMushafTheme;

  const ReciterList({super.key, this.applyMushafTheme = true});

  @override
  ConsumerState<ReciterList> createState() => _ReciterListState();
}

class _ReciterListState extends ConsumerState<ReciterList> {
  String selectedReciterKey = '';

  List<Reciter> allReciterList = [];
  List<Reciter> favoriteReciterList = [];
  List<Reciter> otherReciterList = [];

  void updateSelectedReciter([String? reciterKey]) {
    if (mounted) {
      final audioSegmentData = ref.read(audioSegmentDataInitProvider);
      selectedReciterKey = reciterKey ??
          audioSegmentData.value?.data?.key ??
          PreferenceStorage.getDefaultReciterKey();
    }
  }

  void filterReciterList() {
    final List<String> favoriteReciterKeyList =
        PreferenceStorage.getFavoriteReciterKeys();

    favoriteReciterList = [];
    otherReciterList = [];

    for (final reciter in allReciterList) {
      if (favoriteReciterKeyList.contains(reciter.key)) {
        favoriteReciterList.add(reciter);
      } else {
        otherReciterList.add(reciter);
      }
    }

    favoriteReciterList.sort((a, b) => a.name.compareTo(b.name));
    otherReciterList.sort((a, b) => a.name.compareTo(b.name));
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      allReciterList =
          await ref.read(minioDataRepositoryProvider).getReciterList();

      updateSelectedReciter();
      filterReciterList();

      if (mounted) {
        setState(() {});
      }
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (selectedReciterKey.isNotEmpty) ...[
          buildSelectedReciter(),
          const SizedBox(height: 8),
        ],
        if (favoriteReciterList.isNotEmpty) ...[
          buildFavoriteReciterList(),
          const SizedBox(height: 8),
        ],
        buildOtherReciterList(),
      ],
    );
  }

  Widget buildSelectedReciter() {
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final design = mushafDesignAsync.value?.design;
    final backgroundColor = (widget.applyMushafTheme)
        ? design?.backgroundColor ??
            MushafDesign.getDefault(context.isDarkMode).backgroundColor
        : ColorResource.backgroundWhite(context.isDarkMode);

    final reciter = allReciterList
        .firstWhereOrNull((reciter) => reciter.key == selectedReciterKey);
    if (reciter == null) return Container();

    final isFavorite =
        favoriteReciterList.any((reciter) => reciter.key == selectedReciterKey);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          context.tr('reciter.current_selection'),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: ColorResource.textDefault(context.isDarkMode),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            color: backgroundColor,
          ),
          child: buildReciterItem(reciter, isFavorite, true),
        ),
      ],
    );
  }

  Widget buildFavoriteReciterList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          context.tr('reciter.favorite_reciters'),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: ColorResource.textDefault(context.isDarkMode),
          ),
        ),
        const SizedBox(height: 8),
        buildReciterList(favoriteReciterList, true),
      ],
    );
  }

  Widget buildOtherReciterList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (favoriteReciterList.isNotEmpty) ...[
          Text(
            context.tr('reciter.other_reciters'),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
        ],
        buildReciterList(otherReciterList, false),
      ],
    );
  }

  Widget buildReciterList(List<Reciter> reciterList, bool isFavorite) {
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final design = mushafDesignAsync.value?.design;
    final backgroundColor = (widget.applyMushafTheme)
        ? design?.backgroundColor ??
            MushafDesign.getDefault(context.isDarkMode).backgroundColor
        : ColorResource.backgroundWhite(context.isDarkMode);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        color: backgroundColor,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.all(0),
        itemCount: reciterList.length,
        itemBuilder: (context, index) {
          final reciter = reciterList[index];
          final selected = reciter.key == selectedReciterKey;
          return buildReciterItem(reciter, isFavorite, selected);
        },
        separatorBuilder: (context, index) {
          return const Divider(thickness: .2, height: 1);
        },
      ),
    );
  }

  Widget buildReciterItem(Reciter reciter, bool isFavorite, bool selected) {
    return InkWell(
      onTap: () async {
        final currentTrack = ref.read(selectedTrackProvider);
        if (currentTrack?.type == TrackType.listening) {
          final newTrack = currentTrack!.copyWith(reciter: Nullable(reciter));
          if (mounted) {
            ref.read(trackRepositoryProvider).updateTrack(newTrack);
            ref.read(selectedTrackProvider.notifier).state = newTrack;
          }
        } else {
          await PreferenceStorage.saveDefaultReciterKey(reciter.key);
        }

        updateSelectedReciter(reciter.key);

        if (mounted) {
          setState(() {});
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          children: [
            Expanded(
              child: Text(
                (context.isArabic) ? reciter.arabicName : reciter.name,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: ColorResource.textDefault(context.isDarkMode),
                ),
              ),
            ),
            IconButton(
              style:
                  IconButton.styleFrom(splashFactory: NoSplash.splashFactory),
              icon: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: isFavorite ? Colors.amber : Colors.grey,
              ),
              onPressed: () async {
                final newFavoriteList = favoriteReciterList.toSet();
                if (isFavorite) {
                  newFavoriteList.remove(reciter);
                } else {
                  newFavoriteList.add(reciter);
                }
                await PreferenceStorage.saveFavoriteReciterKeys(
                    newFavoriteList.map((e) {
                  return e.key;
                }).toList());

                filterReciterList();

                setState(() {});
              },
            ),
            Visibility(
              visible: selected,
              maintainSize: true,
              maintainAnimation: true,
              maintainState: true,
              child: const Icon(Icons.check),
            ),
          ],
        ),
      ),
    );
  }
}
