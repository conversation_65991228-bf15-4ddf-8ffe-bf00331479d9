import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/recitation/word_play_state.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/quran/quran_screen.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/inference_pipeline.dart';

class TranscriptionBar extends ConsumerStatefulWidget {
  const TranscriptionBar({super.key});

  @override
  ConsumerState<TranscriptionBar> createState() => _TranscriptionBarState();
}

class _TranscriptionBarState extends ConsumerState<TranscriptionBar> {
  bool barReady = false;
  final double barWidth = 250;
  final double barHeight = 120;
  double alignmentBarHeight = 0.0;

  Offset offset = Offset.zero;

  double maxWidth = 0.0;
  double maxHeight = 0.0;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listenManual(
        recitationAlignmentBarHeightProvider,
        fireImmediately: true,
        (previousHeight, nextHeight) {
          alignmentBarHeight = nextHeight;
        },
      );

      ref.listenManual(alignmentHighlightRectProvider, fireImmediately: true,
          (prevRect, nextRect) {
        if (nextRect != null &&
            (nextRect != prevRect || offset == Offset.zero)) {
          setState(() {
            final centerHorizontal =
                nextRect.left + (nextRect.width - barWidth) / 2;
            final maxBottom = maxHeight - alignmentBarHeight;
            final verticalPosition = (nextRect.bottom + barHeight <= maxBottom)
                ? nextRect.bottom + nextRect.height
                : nextRect.top - barHeight;

            offset = Offset(centerHorizontal, verticalPosition);
            offset = getClampedOffset();
            barReady = true;
          });
        }
      });
    });

    super.initState();
  }

  Offset getClampedOffset() {
    final clampedDx = offset.dx.clamp(0.0, maxWidth - barWidth);
    final clampedDy =
        offset.dy.clamp(0.0, maxHeight - barHeight - alignmentBarHeight);
    return Offset(clampedDx, clampedDy);
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      maxWidth = constraints.maxWidth;
      maxHeight = constraints.maxHeight;
      return barReady
          ? Stack(
              children: [
                Positioned(
                  left: offset.dx,
                  top: offset.dy,
                  child: GestureDetector(
                    onPanUpdate: (details) {
                      setState(() {
                        offset += details.delta;
                        offset = getClampedOffset();
                      });
                    },
                    child: _buildBar(),
                  ),
                )
              ],
            )
          : const SizedBox();
    });
  }

  Widget _buildBar() {
    final transcription = ref.watch(transcriptionProvider);
    final wordPlayState = ref.watch(transcriptionWordPlayStateProvider);

    return SizedBox(
      width: barWidth,
      height: barHeight,
      child: Card(
        elevation: 8,
        color: ColorResource.backgroundBlackAlpha90(context.isDarkMode),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              RotatedBox(
                quarterTurns: 1,
                child: SvgPicture.asset(
                  Assets.svgsIcElipsisDouble,
                  colorFilter: ColorFilter.mode(
                    ColorResource.onPrimary(context.isDarkMode),
                    BlendMode.srcIn,
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: Text(
                    transcription,
                    style: TextStyle(
                      fontSize: 32,
                      color: (context.isDarkMode) ? Colors.black : Colors.white,
                      fontFamily: 'NotoNaskhArabic',
                    ),
                    textDirection: ui.TextDirection.rtl,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: Center(
                      child: InkWell(
                        onTap: () {
                          final inferencePipeline =
                              InferencePipeline.getInstance();
                          inferencePipeline.reset();
                          ref.read(transcriptionProvider.notifier).state = '';
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(
                              Assets.svgsIcRefresh,
                              width: 14,
                              height: 14,
                              colorFilter: ColorFilter.mode(
                                ColorResource.onPrimary(context.isDarkMode),
                                BlendMode.srcIn,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              context.tr('transcription.reset'),
                              style: TextStyle(
                                color:
                                    ColorResource.onPrimary(context.isDarkMode),
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: switch (wordPlayState) {
                        WordPlayState.idle => InkWell(
                            onTap: () {
                              final provider = ref.read(
                                  transcriptionWordPlayStateProvider.notifier);
                              if (provider.state == WordPlayState.idle) {
                                provider.state = WordPlayState.loading;
                              }
                            },
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SvgPicture.asset(
                                  Assets.svgsIcVolume,
                                  colorFilter: ColorFilter.mode(
                                    ColorResource.onPrimary(context.isDarkMode),
                                    BlendMode.srcIn,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  context.tr('transcription.play_sound'),
                                  style: TextStyle(
                                    color: ColorResource.onPrimary(
                                        context.isDarkMode),
                                    fontSize: 10,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        WordPlayState.loading => const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation(Colors.grey),
                            ),
                          ),
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
