import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart' as intl;
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/verse_key_parser.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/quran/quran_widget_state.dart';
import 'package:mushafi/presentation/model/translation/lang_code.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/settings/translation_option_bottom_sheet.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/common/verse_key_card.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class TranslationSheet extends ConsumerStatefulWidget {
  final Animation<double> draggingAnimation;
  final Function(double delta) onDragging;
  final Function() onCloseIconClicked;

  const TranslationSheet({
    super.key,
    required this.draggingAnimation,
    required this.onDragging,
    required this.onCloseIconClicked,
  });

  @override
  ConsumerState<TranslationSheet> createState() => _TranslationSheetState();
}

class _TranslationSheetState extends ConsumerState<TranslationSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController translationOptionAnimationController;
  List<QuranVerse> translationList = [];

  final verseScrollController = AutoScrollController();

  @override
  void initState() {
    translationOptionAnimationController =
        BottomSheet.createAnimationController(this);
    translationOptionAnimationController.addStatusListener((status) {
      if (status == AnimationStatus.dismissed) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final translationDataInitProvider =
              ref.read(translationTafsirDataInitProvider.notifier);
          translationDataInitProvider.fetchTranslationTafsirData();
        });
      } else if (status == AnimationStatus.completed) {}
    });
    translationOptionAnimationController.addListener(() {
      ref
          .read(translationSheetHeightProvider.notifier)
          .setHeight(translationOptionAnimationController.value);
    });

    ref.listenManual(quranPageDataProvider, fireImmediately: true,
        (prevData, nextData) async {
      final rightVerseList = nextData.data.currentRightPage?.verseList ?? [];
      final leftVerseList = nextData.data.currentLeftPage?.verseList ?? [];
      final verseList = rightVerseList + leftVerseList;

      final filteredList = verseList
          .where((verse) => verse.verseKey.parseVerseNumber() > 0)
          .toList();

      translationList = filteredList;

      if (widget.draggingAnimation.value > 0) {
        setState(() {});
      }
    });

    ref.listenManual(highlightedVerseKeyProvider, (prevVerseKey, nextVerseKey) {
      if (nextVerseKey != prevVerseKey) {
        if (translationList.isEmpty) return;

        int index = translationList.indexWhere(
            (translation) => translation.verseKey == nextVerseKey?.verseKey);
        index = index.clamp(0, translationList.length - 1);

        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            verseScrollController.scrollToIndex(index,
                preferPosition: AutoScrollPosition.begin);
          }
        });
      }
    });

    ref.listenManual(translationSheetHeightProvider, (prevHeight, nextHeight) {
      if (nextHeight == 0) {
        final currentWidgetState = ref.read(quranWidgetStateProvider);
        ref.read(quranWidgetStateProvider.notifier).state = QuranWidgetState(
          showTopBar: currentWidgetState.showTopBar,
          showBottomBar: true,
        );
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    translationOptionAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final translationDataInitAsync =
        ref.watch(translationTafsirDataInitProvider);
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);

    final design = mushafDesignAsync.value?.design;
    final drawerColor = design?.drawer1Color ??
        MushafDesign.getDefault(context.isDarkMode).drawer1Color;
    final highlightColor = design?.highLightFadedColor ??
        MushafDesign.getDefault(context.isDarkMode).highLightFadedColor;
    final backgroundColor = design?.backgroundColor ??
        MushafDesign.getDefault(context.isDarkMode).backgroundColor;

    return GestureDetector(
      onVerticalDragUpdate: (DragUpdateDetails details) {
        widget.onDragging(details.primaryDelta ?? 0);
      },
      child: AnimatedBuilder(
        animation: widget.draggingAnimation,
        builder: (context, child) {
          return Container(
            width: double.infinity,
            height: widget.draggingAnimation.value,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              color: drawerColor,
            ),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    IconButton(
                      onPressed: () {
                        widget.onCloseIconClicked();
                      },
                      icon: SvgPicture.asset(
                        Assets.svgsIcCloseCircle,
                        width: 24,
                        height: 24,
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: translationDataInitAsync.when(
                          data: (translation) {
                            final langCode = (translation != null)
                                ? LangCode.fromName(translation.language)
                                : null;
                            final text = (translation != null)
                                ? (translation.nativeTranslatedName.isNotEmpty)
                                    ? translation.nativeTranslatedName
                                    : translation.name
                                : '';

                            return Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                if (langCode != null)
                                  SvgPicture.asset(langCode.flagAsset),
                                const SizedBox(width: 8),
                                Text(
                                  text,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    color: ColorResource.textDefault(
                                        context.isDarkMode),
                                  ),
                                )
                              ],
                            );
                          },
                          error: (error, stackTrace) => Center(
                            child: Text(
                              error.toString(),
                              style: TextStyle(
                                color: ColorResource.textDefault(
                                    context.isDarkMode),
                              ),
                            ),
                          ),
                          loading: () => const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          backgroundColor: drawerColor,
                          transitionAnimationController:
                              translationOptionAnimationController,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                                top: Radius.circular(12.0)),
                          ),
                          builder: (context) {
                            return TranslationOptionBottomSheet(
                              translationOptionAnimationController,
                            );
                          },
                        );
                      },
                      icon: SvgPicture.asset(
                        Assets.svgsIcTranslation,
                        width: 24,
                        height: 24,
                        colorFilter: ColorFilter.mode(
                          ColorResource.textGrey(context.isDarkMode),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
                translationDataInitAsync.when(
                  data: (translation) => Expanded(
                    child: ListView.builder(
                      controller: verseScrollController,
                      itemCount: translationList.length,
                      itemBuilder: (context, index) {
                        final verse = translationList[index];
                        final isArabic =
                            translation?.language.toLowerCase() == 'arabic';
                        return AutoScrollTag(
                          key: ValueKey(index),
                          controller: verseScrollController,
                          index: index,
                          child: Builder(builder: (context) {
                            if (index < translationList.length) {
                              // Safe to access translationList[index]
                              return buildTranslationItem(
                                verse,
                                highlightColor,
                                backgroundColor,
                                isArabic,
                              );
                            } else {
                              // Return a placeholder or empty widget when index is out of bounds
                              return const SizedBox.shrink();
                            }
                          }),
                        );
                      },
                    ),
                  ),
                  error: (error, stackTrace) => Center(
                    child: Text(error.toString()),
                  ),
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget buildTranslationItem(
    QuranVerse verse,
    Color highlightColor,
    Color backgroundColor,
    bool isArabic,
  ) {
    final highlightedVerseKey = ref.watch(highlightedVerseKeyProvider);
    final cardColor = (verse.verseKey == highlightedVerseKey?.verseKey)
        ? highlightColor
        : backgroundColor;

    return InkWell(
      onTapUp: (details) {
        ref
            .read(highlightedVerseKeyProvider.notifier)
            .highlight(verse.verseKey, true);
      },
      child: Card(
        color: cardColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 4),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildTranslationText(verse.translationOrTafseer,
                  verse.verseKey.parseVerseNumber(), isArabic),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildVerseKey(String verseKey) {
    return VerseKeyCard(verseKey: verseKey);
  }

  Widget buildTranslationText(
      String translation, int verseNumber, bool isArabic) {
    final isRtl = intl.Bidi.detectRtlDirectionality(translation);
    final verseNumberText = (isArabic)
        ? NumberFormat('#', 'ar_EG').format(verseNumber)
        : verseNumber.toString();

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(top: 2.0),
        child: Directionality(
          textDirection: (isRtl) ? ui.TextDirection.rtl : ui.TextDirection.ltr,
          child: Text.rich(
            TextSpan(
              style: TextStyle(
                fontSize: 16.0,
                color: ColorResource.textDefault(context.isDarkMode),
              ),
              children: [
                TextSpan(
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    text: '$verseNumberText. '),
                TextSpan(text: translation),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
