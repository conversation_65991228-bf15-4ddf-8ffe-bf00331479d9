import 'dart:async';
import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/verse_key_extension.dart';
import 'package:mushafi/extension/verse_key_parser.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/home/<USER>';
import 'package:mushafi/presentation/model/nullable.dart';
import 'package:mushafi/presentation/model/quran/quran_mode.dart';
import 'package:mushafi/presentation/model/quran/quran_verse.dart';
import 'package:mushafi/presentation/model/quran/quran_widget_state.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/presentation/model/quran/recitation_pending_data.dart';
import 'package:mushafi/presentation/model/quran/verse_tap_type.dart';
import 'package:mushafi/presentation/model/quran_canvas/page_position.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_spread_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/spread_position.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/presentation/model/recitation/word_play_state.dart';
import 'package:mushafi/presentation/model/scale_state.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/model/track/track_range.dart';
import 'package:mushafi/presentation/model/track/track_range_portion.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/presentation/notifier/quran_page_data_notifier.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/quran/quran_screen.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/constants.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:mushafi/utils/math_helper.dart';
import 'package:mushafi/utils/quran_text_cache_utils.dart';
import 'package:mushafi/utils/recitation_alignment_helper.dart';
import 'package:mushafi/utils/recitation_helper.dart';
import 'package:mushafi/utils/recitation_player.dart';
import 'package:mushafi/utils/verse_tap_helper.dart';
import 'package:mushafi/utils/word_image_render_helper.dart';
import 'package:mushafi/widget/quran/quran_custom_paint.dart';
import 'package:mushafi/widget/quran/verse_tooltip.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:toastification/toastification.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class QuranLayout extends ConsumerStatefulWidget {
  final QuranCanvasData canvasData;
  final int initialPageNumber;

  const QuranLayout(this.canvasData, this.initialPageNumber, {super.key});

  @override
  ConsumerState<QuranLayout> createState() => QuranLayoutState();

  static const quranGestureDetectorKey = ValueKey('quranGestureDetector');
}

@visibleForTesting
class QuranLayoutState extends ConsumerState<QuranLayout>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  OverlayEntry? _bookmarkTooltipEntry;

  late AnimationController scaleAnimationController;
  late Animation<double> scaleCurvedAnimation;
  late Tween<double> scaleTween;
  late Animation<double> scaleRightTweenAnimation;
  late Animation<double> scaleLeftTweenAnimation;

  late AnimationController scrollAnimationController;
  late Animation<double> scrollCurvedAnimation;
  late Tween<double> scrollTween;
  late Animation<double> scrollTweenAnimation;

  late AnimationController swipeAnimationController;
  late Animation<double> swipeCurvedAnimation;
  late Tween<double> swipeTween;
  late Animation<double> swipeTweenAnimation;

  final painterNotifier = ValueNotifier(0);

  final defaultAnimationDuration = const Duration(milliseconds: 500);
  final scrollAnimationDuration = const Duration(milliseconds: 300);
  final swipeAnimationDuration = const Duration(milliseconds: 400);

  final double _swipeVelocityThreshold = 500.0;
  final scrollAngleToleranceValue = 45;
  double _lastX = 0.0;
  double _lastY = 0.0;
  double _pinchScaleFactor = 1.0;
  final double _pinchBaseScaleFactor = 1.0;
  double initialVerticalDragGlobalY = 0;
  bool isScrolling = false;
  bool determineScrolling = false;
  bool isSwiping = false;
  bool swipeOutOfRange = false;
  bool determineSwiping = false;

  final verseTapHelper = VerseTapHelper();
  final recitationAlignmentHelper = RecitationAlignmentHelper();

  late QuranCanvasData canvasData;

  Timer? undoAlignmentSnackbarTimer;
  Timer? showAlignmentEndSnackbarTimer;

  ToastificationItem? outOfRangeToast;

  void removeBookmarkTooltip() {
    _bookmarkTooltipEntry?.remove();
    _bookmarkTooltipEntry = null;
  }

  void refreshPainter() {
    painterNotifier.value += 1;
  }

  double _getNormalizedAnimationValue(PagePosition position) {
    final begin = scaleTween.begin ?? 1;
    final end = scaleTween.end ?? 1;

    final animationValue = switch (position) {
      PagePosition.left => scaleLeftTweenAnimation.value,
      PagePosition.right => scaleRightTweenAnimation.value,
    };
    final result = (animationValue - begin) / (end - begin);
    return (result.isNaN || result.isInfinite) ? 1 : result;
  }

  void backgroundCacheQuranText(QuranCanvasSpreadData spreadData) {
    final quranMode = ref.read(quranModeProvider.notifier).state;

    if (quranMode is DefaultQuranMode &&
        canvasData.orientation == Orientation.portrait) {
      if (spreadData.rightPageData.quranPage != null) {
        QuranTextCacheUtils.cacheText(
          Size(canvasData.canvasWidth, canvasData.canvasHeight),
          canvasData,
          spreadData.rightPageData,
          quranMode,
          recitationAlignmentHelper,
        );
      }

      if (spreadData.leftPageData.quranPage != null) {
        QuranTextCacheUtils.cacheText(
          Size(canvasData.canvasWidth, canvasData.canvasHeight),
          canvasData,
          spreadData.leftPageData,
          quranMode,
          recitationAlignmentHelper,
        );
      }
    }
  }

  @override
  void initState() {
    WakelockPlus.enable();
    canvasData = widget.canvasData;
    canvasData.devicePixelRatio =
        WidgetsBinding.instance.platformDispatcher.views.first.devicePixelRatio;

    QuranTextCacheUtils.setCallback((
      pageNumber,
      image,
      lineBreakImage,
      wordToHighlightRectMap,
      wordToHighlightLineBreakRectMap,
    ) {
      if (mounted) {
        final pageData = canvasData.getPageData(pageNumber);
        if (pageData != null) {
          if (image != null) {
            pageData.textImage = image;
          }

          if (lineBreakImage != null) {
            pageData.lineBreakTextImage = lineBreakImage;
          }

          if (wordToHighlightRectMap != null) {
            pageData.wordToHighlightRectMap = wordToHighlightRectMap;
          }

          if (wordToHighlightLineBreakRectMap != null) {
            pageData.wordToHighlightLineBreakRectMap =
                wordToHighlightLineBreakRectMap;
          }

          refreshPainter();
        }
      }
    });

    final juzNameScript = PreferenceStorage.getJuzNameScript();
    if (canvasData.juzNameScript != juzNameScript) {
      canvasData.setJuzNameScript(juzNameScript);
    }

    final track = ref.read(selectedTrackProvider);
    if (track != null) {
      prepareSelectedTrack(track);
    }

    canvasData.zoomState = track?.zoomState ?? PreferenceStorage.getZoomState();

    final highlightedVerseKey = ref.read(highlightedVerseKeyProvider);
    if (highlightedVerseKey != null) {
      canvasData.highlightedTapType = VerseTapType.single;
    }

    setupScaleAnimation();
    setupScrollAnimation();
    setupSwipeAnimation();

    ref.listenManual<QuranPageDataState>(quranPageDataProvider,
        (previousData, nextData) {
      final newData = nextData.data;

      if (previousData == null ||
          previousData.data.isEmpty() ||
          nextData.isInitial) {
        canvasData.cacheQuranTextDirectly = true;
        canvasData.resetCachedText();

        canvasData.setPreviousData(
          rightQuranPage: newData.previousRightPage,
          leftQuranPage: newData.previousLeftPage,
        );
        canvasData.setCurrentData(
          rightQuranPage: newData.currentRightPage,
          leftQuranPage: newData.currentLeftPage,
        );
        canvasData.setNextData(
          rightQuranPage: newData.nextRightPage,
          leftQuranPage: newData.nextLeftPage,
        );

        refreshScale();
      } else {
        final previousPageNumber =
            previousData.data.currentRightPage?.pageNumber;
        final nextPageNumber = newData.currentRightPage?.pageNumber;
        if (previousPageNumber == null || nextPageNumber == null) {
          return;
        }

        canvasData.cacheQuranTextDirectly =
            WordImageRenderHelper.preRenderedWordPageNumbers.any((value) {
          return value == newData.previousRightPage?.pageNumber ||
              value == newData.previousLeftPage?.pageNumber ||
              value == newData.nextRightPage?.pageNumber ||
              value == newData.nextLeftPage?.pageNumber;
        });

        final doQuranTextBackgroundCaching = !nextData.requestListeningTrack &&
            !canvasData.cacheQuranTextDirectly;

        // shifting to prevent unnecessary data creation
        if (nextPageNumber > previousPageNumber) {
          // move to the next page
          canvasData.shiftDataToNext(
            nextRightQuranPage: newData.nextRightPage,
            nextLeftQuranPage: newData.nextLeftPage,
          );

          if (doQuranTextBackgroundCaching) {
            backgroundCacheQuranText(canvasData.nextSpreadData);
          }
        } else if (nextPageNumber < previousPageNumber) {
          // move to the previous page
          canvasData.shiftDataToPrevious(
            previousRightQuranPage: newData.previousRightPage,
            previousLeftQuranPage: newData.previousLeftPage,
          );

          if (doQuranTextBackgroundCaching) {
            backgroundCacheQuranText(canvasData.previousSpreadData);
          }
        }
      }

      if (mounted) {
        updatePageNumberTextSize();
        updateCoverToCoverTrack();

        if (nextData.requestListeningTrack) {
          final track = ref.read(selectedTrackProvider);
          if (track?.type == TrackType.listening) {
            prepareListeningTrackRecitation(track!);
          }
        }

        final recitationPendingData = ref.read(recitationPendingProvider);
        if (recitationPendingData != null) {
          ref.read(recitationPendingProvider.notifier).state = null;

          if (recitationPendingData.isListeningTrack) {
            final track = ref.read(selectedTrackProvider);
            if (track?.type == TrackType.listening) {
              prepareListeningTrackRecitation(
                  track!, recitationPendingData.recitingVerseKey);
            }
          } else {
            prepareRecitation(recitationPendingData.recitingVerseKey);
          }
        }

        if (canvasData.zoomState == ZoomState.zoomedInLevel1) {
          canvasData.updateZoomInLevel1Scale();
        }
      }

      refreshPainter();
    });

    ref.listenManual(highlightedVerseKeyProvider, fireImmediately: true,
        (previousVerseKey, nextVerseKey) {
      if (nextVerseKey != previousVerseKey) {
        canvasData.highlightedVerseKey = nextVerseKey?.verseKey;
        updateTrackHighlightedVerse(nextVerseKey?.verseKey);

        if (nextVerseKey?.quranFocused == true) {
          focusToHighlightedVerseKey();
        }

        if (nextVerseKey == null) {
          removeBookmarkTooltip();
        }

        refreshPainter();
      }
    });

    ref.listenManual(focusHighlightProvider, (prev, next) {
      focusToHighlightedVerseKey();
    });

    ref.listenManual<QuranWord?>(highlightedWordProvider,
        (previousWord, nextWord) {
      if (nextWord != previousWord) {
        canvasData.highlightedWord = nextWord;

        final rect = canvasData.getCurrentSpreadWordRect(nextWord);
        if (rect != null) {
          scrollAnimationController.duration = scrollAnimationDuration;

          double end = getGlobalPositionFromWordRect(rect).dy;

          final translationSheetHeight =
              ref.read(translationSheetHeightProvider);
          final currentVisibleHeight =
              canvasData.canvasHeight - translationSheetHeight;
          end = end - currentVisibleHeight / 2;

          end = limitScroll(end);

          startScrollAnimation(begin: canvasData.scrollY, end: end);
        }

        refreshPainter();
      }
    });

    recitationAlignmentHelper.setOnHighlightChange((highlight, isResultUpdated,
        {isInitial = false}) {
      if (canvasData.currentPageNumber != null &&
          highlight != null &&
          !isInitial) {
        if (canvasData.currentPageNumber != highlight.pageNumber) {
          final isPageDiffMoreThanOne =
              (highlight.pageNumber - canvasData.currentPageNumber!).abs() > 1;
          final pageNumber = highlight.pageNumber;
          final spreadNumber = (pageNumber / 2).round();
          final pagerNumber =
              (canvasData.isSpread()) ? spreadNumber : pageNumber;

          moveToDesiredPager(
            currentPager: canvasData.currentPagerNumber!.toDouble(),
            nextPager: pagerNumber.toDouble(),
            isInitial: isPageDiffMoreThanOne,
          );
        }
      }

      if (isResultUpdated) {
        canvasData.resetCachedText();
        canvasData.resetCachedPage();

        final resultList = recitationAlignmentHelper.resultMap.values.toList();

        if (mounted) {
          ref
              .read(alignmentRepositoryProvider)
              .addAlignmentWordResults(resultList);
        }
      }

      if (highlight?.isCorrect == false) {
        canvasData.setHighlightColor(
            Colors.red.withOpacity(.3), Colors.red.withOpacity(.5));
      } else {
        canvasData.applyThemeHighlightColor();
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        final wordRect = canvasData.getCurrentSpreadWordRect(highlight?.word);
        if (wordRect != null) {
          if (mounted) {
            ref.read(alignmentHighlightRectProvider.notifier).state = wordRect;
          }
        }
      });

      if (mounted) {
        ref.read(highlightedWordProvider.notifier).state = highlight?.word;
        ref.read(currentAlignmentHighlightProvider.notifier).state = highlight;
      }

      if (recitationAlignmentHelper.isAlignmentEnd()) {
        if (mounted) {
          ref.read(showAlignmentEndSnackbarProvider.notifier).state = true;
        }
        showAlignmentEndSnackbarTimer?.cancel();
        showAlignmentEndSnackbarTimer = Timer(const Duration(seconds: 3), () {
          if (mounted) {
            ref.read(showAlignmentEndSnackbarProvider.notifier).state = false;
          }
        });
      }

      refreshPainter();
    });

    recitationAlignmentHelper.setOnManualHighlight((highlight) {
      final inferencePipeline = InferencePipeline.getInstance();
      if (inferencePipeline.isRunning && highlight != null) {
        inferencePipeline.setAlignerCursor(
          ayahId: highlight.verseId,
          wordIndex: highlight.wordIndex,
        );
      }
    });

    recitationAlignmentHelper.setOnPreRestart((trackId, startTime, endTime) {
      if (mounted) {
        ref
            .read(alignmentRepositoryProvider)
            .deleteAlignmentWordResults(trackId, startTime, endTime);
      }
    });

    ref.listenManual(alignmentResultProvider, (prevResult, nextResult) {
      recitationAlignmentHelper.updateCurrentHighlight(nextResult);
    });

    ref.listenManual(revealWordProvider, (prev, next) {
      recitationAlignmentHelper.revealWord();
    });

    ref.listenManual(revealVerseProvider, (prev, next) {
      recitationAlignmentHelper.revealVerse();
    });

    ref.listenManual(restartActivityProvider, (prev, next) {
      recitationAlignmentHelper.restart();

      ref.read(undoAlignmentSnackbarProvider.notifier).show(
            recitationAlignmentHelper.track!.range.startVerseNumber,
          );
      undoAlignmentSnackbarTimer?.cancel();
      undoAlignmentSnackbarTimer = Timer(const Duration(seconds: 3), () {
        if (mounted) {
          ref.read(undoAlignmentSnackbarProvider.notifier).show(null);
        }
      });
    });

    ref.listenManual(undoWordProvider, (prev, next) {
      recitationAlignmentHelper.undoWord();
    });

    Timer? showHintTimer;
    ref.listenManual(showHintProvider, (prev, next) {
      canvasData.resetCachedPage();
      canvasData.resetCachedText();
      ref.read(quranModeProvider.notifier).state = MemorizationQuranMode(true);

      showHintTimer?.cancel();
      showHintTimer = Timer(const Duration(milliseconds: 1000), () {
        canvasData.resetCachedPage();
        canvasData.resetCachedText();
        ref.read(quranModeProvider.notifier).state =
            MemorizationQuranMode(false);
      });
    });

    ref.listenManual(reciterTranslationOptionDialogShownProvider, (prev, next) {
      stopRecitationAndClearHighlight();
    });

    ref.listenManual(translationTafsirDataInitProvider, (prev, next) {
      next.whenData((data) {
        final spreadNumber = canvasData.currentSpreadNumber ??
            (widget.initialPageNumber / 2).round();
        final pageNumber =
            canvasData.currentPageNumber ?? widget.initialPageNumber;

        final quranPageDataNotifier = ref.read(quranPageDataProvider.notifier);

        if (canvasData.isSpread()) {
          quranPageDataNotifier.updatePageData(
            spreadNumber: spreadNumber,
            isInitial: false,
          );
        } else {
          quranPageDataNotifier.updatePageData(
            pageNumber: pageNumber,
            isInitial: false,
          );
        }
      });
    });

    ref.listenManual(audioSegmentDataInitProvider, (prev, next) {
      next.whenData((data) {
        final spreadNumber = canvasData.currentSpreadNumber ??
            (widget.initialPageNumber / 2).round();
        final pageNumber =
            canvasData.currentPageNumber ?? widget.initialPageNumber;

        final quranPageDataNotifier = ref.read(quranPageDataProvider.notifier);

        // needed for updating the word segments to the verse data
        if (canvasData.isSpread()) {
          quranPageDataNotifier.updatePageData(
            spreadNumber: spreadNumber,
            isInitial: true,
            requestListeningTrack: data?.requestListeningTrack ?? false,
          );
        } else {
          quranPageDataNotifier.updatePageData(
            pageNumber: pageNumber,
            isInitial: true,
            requestListeningTrack: data?.requestListeningTrack ?? false,
          );
        }
      });
    });

    ref.listenManual(transcriptionWordPlayStateProvider,
        (prevState, nextState) async {
      if (nextState == WordPlayState.loading) {
        final highlightedWord = canvasData.highlightedWord;
        if (highlightedWord == null) return;

        final leftVerseList =
            canvasData.currentSpreadData.leftPageData.quranPage?.verseList ??
                [];
        final rightVerseList =
            canvasData.currentSpreadData.rightPageData.quranPage?.verseList ??
                [];
        final verseList = leftVerseList + rightVerseList;

        final verse = verseList.firstWhereOrNull(
            (verse) => verse.verseKey == highlightedWord.verseKey);
        if (verse == null) return;

        final wordIndex =
            verse.words.indexWhere((word) => word.id == highlightedWord.id);
        final audioSegment = verse.audioSegments
            .firstWhereOrNull((segment) => segment.index == wordIndex);

        if (audioSegment == null) return;

        final initDataAsync = ref.read(audioSegmentDataInitProvider);
        initDataAsync.whenData((state) async {
          if (state?.data == null) return;

          final surahList =
              await ref.read(jsonDataRepositoryProvider).getSurahList();
          RecitationPlayer.playWord(
            surahList,
            verse.verseKey,
            audioSegment.startTime,
            audioSegment.endTime,
            state!.data!,
            onComplete: () {
              if (mounted) {
                ref.read(transcriptionWordPlayStateProvider.notifier).state =
                    WordPlayState.idle;
              }
            },
          );
        });
      }
    });

    ref.listenManual(pageNavigationProvider, (previousPageNumber, pageNumber) {
      if (pageNumber != null) {
        stopRecitationAndClearHighlight();

        WidgetsBinding.instance.addPostFrameCallback((_) {
          final spreadNumber = (pageNumber / 2).round();
          final pagerNumber =
              (canvasData.isSpread()) ? spreadNumber : pageNumber;

          moveToDesiredPager(
            currentPager: pagerNumber.toDouble(),
            nextPager: pagerNumber.toDouble(),
            isInitial: true,
          );
        });

        ref.read(pageNavigationProvider.notifier).reset();
      }
    });

    ref.listenManual(revertUndoAlignmentProvider, (prev, next) {
      undoAlignmentSnackbarTimer?.cancel();
      ref.read(undoAlignmentSnackbarProvider.notifier).show(null);
      recitationAlignmentHelper.revertUndo();
    });

    ref.listenManual(requestRecitationProvider, (prev, next) {
      final currentSpread = canvasData.currentSpreadData;
      final rightVerseList =
          currentSpread.rightPageData.quranPage?.verseList ?? [];
      final recitedVerseKey = canvasData.highlightedVerseKey ??
          rightVerseList.firstOrNull?.verseKey;

      if (recitedVerseKey != null) {
        final currentTrack = ref.read(selectedTrackProvider);
        if (currentTrack?.type == TrackType.listening) {
          final range = currentTrack?.range;
          if (range == null) return;

          final startVerseKey = (range.startVerseNumber == 1)
              ? '${range.startSurahNumber}:0'
              : range.startVerseKey;

          if (recitedVerseKey.verseKeyInt < startVerseKey.verseKeyInt ||
              recitedVerseKey.verseKeyInt > range.endVerseKey.verseKeyInt) {
            CommonUtils.showToast(
                context.tr('quran.cannot_recite_outside_range'));
            return;
          }

          prepareListeningTrackRecitation(currentTrack!, recitedVerseKey);
          return;
        }

        prepareRecitation(recitedVerseKey);
      }
    });

    ref.listenManual(stopRecitationProvider, (prev, next) async {
      await RecitationPlayer.stop();
      ref.read(highlightedWordProvider.notifier).state = null;
      canvasData.highlightedTapType = VerseTapType.single;

      ref.read(recitationPlayingProvider.notifier).state = false;
    });

    ref.listenManual(requestNextRecitationProvider, (prev, next) async {
      RecitationPlayer.next();
    });

    ref.listenManual(translationSheetHeightProvider, (prevHeight, nextHeight) {
      final dy = nextHeight - (prevHeight ?? 0);
      if (dy >= 0) return;

      final end = canvasData.scrollY + dy;
      final limitedEnd = limitScroll(canvasData.scrollY + dy);
      if (end >= getScrollLimit() || limitedEnd == 0) {
        scrollAnimationController.duration = Duration.zero;
        startScrollAnimation(begin: canvasData.scrollY, end: limitedEnd);
      }
    });

    WidgetsBinding.instance.addObserver(this);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final jsonDataRepository = ref.read(jsonDataRepositoryProvider);
      canvasData.surahList = await jsonDataRepository.getSurahList();

      if (mounted) {
        final currentOrientation = MediaQuery.of(context).orientation;
        if (canvasData.orientation != currentOrientation) {
          setState(() {
            final zoomState = (currentOrientation == Orientation.portrait)
                ? track?.zoomState
                : PreferenceStorage.getZoomState();
            setupInitialState(zoomState: zoomState);
          });
        }
      }
    });

    super.initState();
  }

  void showBookmarkTooltip(Offset position) {
    // Account for scroll position
    final scrolledPosition =
        Offset(position.dx, position.dy + canvasData.scrollY);

    Offset adjustedPosition;
    if (canvasData.zoomState == ZoomState.zoomedInLevel2) {
      // Use regular scaling for zoomedInLevel2, matching drawQuranPage behavior
      adjustedPosition = Offset(
        scrolledPosition.dx / canvasData.rightZoomScale,
        scrolledPosition.dy / canvasData.rightZoomScale,
      );
    } else {
      // Keep center pivot scaling for other zoom states
      final center =
          Offset(canvasData.canvasWidth / 2, canvasData.canvasHeight / 2);
      adjustedPosition = Offset(
        center.dx +
            (scrolledPosition.dx - center.dx) / canvasData.rightZoomScale,
        center.dy +
            (scrolledPosition.dy - center.dy) / canvasData.rightZoomScale,
      );
    }

    final rectList = canvasData.getCurrentWordRectMap().entries.toList();
    final tappedEntry = rectList
        .firstWhereOrNull((entry) => entry.value.contains(adjustedPosition));

    if (tappedEntry == null) return;
    if (tappedEntry.key.verseKey.parseVerseNumber() == -1) return;

    final rect = tappedEntry.value;
    final overlay = Overlay.of(context);

    // Remove previous tooltip if exists
    removeBookmarkTooltip();

    final RenderBox box = context.findRenderObject() as RenderBox;
    final Offset globalWordBottomCenter = box.localToGlobal(rect.bottomCenter);

    // Convert adjustedPosition to global coordinates to center tooltip horizontally
    final Offset globalAdjusted = box.localToGlobal(adjustedPosition);

    // Calculate tooltip position - centered horizontally at the tap position
    _bookmarkTooltipEntry = OverlayEntry(
      builder: (context) => Positioned(
        // Clamp the tooltip to stay within the screen
        left: ((globalAdjusted.dx - 60)
            .clamp(0.0, MediaQuery.of(context).size.width - 120)),
        // Center by offsetting by half of tooltip width (120/2 = 60)
        top: globalWordBottomCenter.dy,
        child: VerseTooltip(
          onTap: () async {
            final verseKey = tappedEntry.key.verseKey;
            final verse = await ref
                .read(quranRepositoryProvider)
                .getQuranVerseByVerseKey(verseKey);
            if (verse == null) return;

            ref
                .read(bookmarkRepositoryProvider)
                .addBookmark(verseKey, verse.pageId);
            removeBookmarkTooltip();
            CommonUtils.showToast('bookmark_added'.tr());
          },
        ),
      ),
    );
    overlay.insert(_bookmarkTooltipEntry!);
    highlightVerse(tappedEntry.key.verseKey);
  }

  void focusToHighlightedVerseKey() {
    final verseKey = ref.read(highlightedVerseKeyProvider);
    if (verseKey == null) return;

    final rect = canvasData.getFirstWordRectByVerseKey(verseKey.verseKey);
    if (rect != null) {
      scrollAnimationController.duration = scrollAnimationDuration;

      double end = getGlobalPositionFromWordRect(rect).dy;

      final translationSheetHeight = ref.read(translationSheetHeightProvider);
      final currentVisibleHeight =
          canvasData.canvasHeight - translationSheetHeight;
      end = end - currentVisibleHeight / 2;

      end = limitScroll(end);

      startScrollAnimation(begin: canvasData.scrollY, end: end);
    }
  }

  double getScrollLimit() {
    final zoomedTextHeight = canvasData.getZoomedTextHeight(
      SpreadPosition.current,
      PagePosition.right,
    );
    final initialScrollHeight =
        canvasData.getPageHeight(zoomedTextHeight) - canvasData.canvasHeight;
    final translationSheetHeight = ref.read(translationSheetHeightProvider);
    return initialScrollHeight +
        canvasData.startScrollY +
        translationSheetHeight;
  }

  double limitScroll(double value) {
    final minScroll = canvasData.startScrollY;
    final maxScroll = math.max(minScroll, getScrollLimit());
    return value.clamp(minScroll, maxScroll);
  }

  void setupInitialState({ZoomState? zoomState}) {
    resetData(zoomState: zoomState);
    refreshScale();
    setupInitialScrollY();
  }

  void clearStateOnSwipe(double nextPager) {
    final quranMode = ref.read(quranModeProvider.notifier).state;
    if (canvasData.currentPagerNumber != nextPager &&
        quranMode is DefaultQuranMode) {
      stopRecitationAndClearHighlight();
    }
  }

  String? getLastHighlightedVerseKey(Track track) {
    if (track.type == TrackType.readingCoverToCover ||
        track.type == TrackType.listening) {
      return track.lastHighlightedVerseKey;
    }

    if (track.type == TrackType.readingHabitual) {
      return track.lastHighlightedVerseKey ?? track.range.startVerseKey;
    }

    return null;
  }

  void prepareSelectedTrack(Track track) async {
    if (track.type == TrackType.memorizing) {
      prepareMemorizationTrack(track);
    }

    if (track.type == TrackType.readingWithAi) {
      prepareReadingWithAiTrack(track);
    }

    final lastHighlightedVerseKey = getLastHighlightedVerseKey(track);
    if (lastHighlightedVerseKey != null) {
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          highlightVerse(lastHighlightedVerseKey);
        });
      }
    }

    if (track.type == TrackType.listening) {
      prepareListeningTrack(track);
    }
  }

  void prepareListeningTrack(Track track) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(quranWidgetStateProvider.notifier).state =
          const QuranWidgetState(
        showTopBar: false,
        showBottomBar: true,
      );
    });

    if (track.reciterKey != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        String recitingStartVerseKey = (track.range.startVerseNumber == 1)
            ? "${track.range.startSurahNumber}:0"
            : track.range.startVerseKey;

        if (track.lastHighlightedVerseKey != null) {
          recitingStartVerseKey = track.lastHighlightedVerseKey!;
        }

        onRecitationSetup(recitingStartVerseKey);

        if (mounted) {
          final audioSegmentExists = await ref
              .read(minioDataRepositoryProvider)
              .audioSegmentExists('${track.reciterKey}.fb');
          if (!audioSegmentExists) {
            showDownloadingWordHighlightToast();
          }

          ref.read(audioSegmentDataInitProvider.notifier).fetchAudioSegmentData(
              reciterKey: track.reciterKey!, requestListeningTrack: true);
        }
      });
    }
  }

  void prepareMemorizationTrack(Track track) async {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(quranModeProvider.notifier).state =
            MemorizationQuranMode(false);
        prepareAlignmentTrack(track);
      });
    }
  }

  void prepareReadingWithAiTrack(Track track) {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(quranModeProvider.notifier).state =
            RecitationCheckerQuranMode();
        prepareAlignmentTrack(track);
      });
    }
  }

  void prepareAlignmentTrack(Track track) async {
    ref.read(quranWidgetStateProvider.notifier).state = const QuranWidgetState(
      showTopBar: false,
      showBottomBar: true,
    );

    canvasData.zoomState = ZoomState.defaultZoom;
    refreshScale();

    canvasData.setPositionOffsetLimit(
      track.range.startPageNumber.toDouble(),
      track.range.endPageNumber.toDouble(),
      MediaQuery.of(context).orientation,
    );
    canvasData.rightFrameAlpha = 0;

    String startVerseKey =
        '${track.range.startSurahNumber}:${track.range.startVerseNumber}';
    String endVerseKey =
        '${track.range.endSurahNumber}:${track.range.endVerseNumber}';

    // to fix the activity input invalid data
    if (track.rangePortion == TrackRangePortion.page) {
      final jsonDataRepository = ref.read(jsonDataRepositoryProvider);
      final surahList = await jsonDataRepository.getSurahList();

      final quranRepository = ref.read(quranRepositoryProvider);
      final startPage = await quranRepository
          .getPageByPageNumber(track.range.startPageNumber);

      if (startPage != null) {
        final startJuz =
            await quranRepository.getJuzByJuzNumber(startPage.juzId!.first);

        if (startJuz != null) {
          final startSurah = surahList[startPage.surahId!.first - 1];
          final newRange = track.range.copyWith(
            startJuzNumber: startJuz.juzId,
            startSurahNumber: startSurah.id,
          );
          final newTrack = track.copyWith(range: newRange);

          startVerseKey = '${startSurah.id}:${track.range.startVerseNumber}';
          ref.read(trackRepositoryProvider).updateTrack(newTrack);
          ref.read(selectedTrackProvider.notifier).state = newTrack;
        }
      }
    } else if (track.rangePortion == TrackRangePortion.juz) {
      final jsonDataRepository = ref.read(jsonDataRepositoryProvider);
      final surahList = await jsonDataRepository.getSurahList();

      final quranRepository = ref.read(quranRepositoryProvider);
      final startPage = await quranRepository
          .getPageByPageNumber(track.range.startPageNumber);

      if (startPage != null) {
        final startSurah = surahList[startPage.surahId!.first - 1];
        final newRange = track.range.copyWith(
          startSurahNumber: startSurah.id,
        );
        final newTrack = track.copyWith(range: newRange);

        startVerseKey = '${startSurah.id}:${track.range.startVerseNumber}';
        ref.read(trackRepositoryProvider).updateTrack(newTrack);
        ref.read(selectedTrackProvider.notifier).state = newTrack;
      }
    }

    final alignmentVerseList = await ref
        .read(quranRepositoryProvider)
        .getQuranVerseList(startVerseKey, endVerseKey);
    if (alignmentVerseList == null || alignmentVerseList.isEmpty) return;

    final resultList = await ref
        .read(alignmentRepositoryProvider)
        .getAlignmentWordResults(track.id);

    recitationAlignmentHelper.setup(track, alignmentVerseList, resultList);

    refreshPainter();
  }

  void moveToDesiredPager({
    required double currentPager,
    required double nextPager,
    required bool isInitial,
  }) {
    if (mounted) {
      startSwipeAnimation(begin: currentPager, end: nextPager);

      // todo this is affect the app performance, do in the background thread instead
      final quranPageDataNotifier = ref.read(quranPageDataProvider.notifier);
      if (canvasData.isSpread()) {
        quranPageDataNotifier.updatePageData(
            spreadNumber: nextPager.toInt(), isInitial: isInitial);
      } else {
        quranPageDataNotifier.updatePageData(
            pageNumber: nextPager.toInt(), isInitial: isInitial);
      }
      //

      updateCachedPageNumber(nextPager.toInt());
      updateRecentPage(nextPager.toInt());
    }
  }

  void updateRecentPage(int pagerNumber) {
    final pageNumber =
        (canvasData.isSpread()) ? (pagerNumber * 2 - 1) : pagerNumber;

    final pageId = ref.read(pageIdProvider);
    final recentPage = RecentPage(
      id: pageId,
      pageNumber: pageNumber,
      time: DateTime.now().millisecondsSinceEpoch,
    );
    ref.read(recentPageRepositoryProvider).addRecentPage(recentPage);
  }

  void updatePageNumberTextSize() {
    if (mounted) {
      ref.read(navigationTextSizeProvider.notifier).state =
          canvasData.getCurrentPageNumberTextSize();
    }
  }

  void updatePageNavigationHeight() {
    if (mounted) {
      final scale =
          (canvasData.scaleWholePage()) ? canvasData.rightZoomScale : 1;
      ref
          .read(quranNavigationHeightNotifierProvider.notifier)
          .updateValue(canvasData.navigationHeight * scale);
    }
  }

  void updatePageNavigationTop() {
    if (mounted) {
      final centerVertical = canvasData.canvasHeight / 2;
      final top = canvasData.scaleWholePage()
          ? (0 - centerVertical) * canvasData.rightZoomScale + centerVertical
          : 0.0;
      ref.read(quranPageNavigationTopProvider.notifier).state = top;
    }
  }

  void updateZoomState() {
    if (canvasData.orientation == Orientation.landscape) return;

    final track = ref.read(selectedTrackProvider);
    if (track != null) {
      final newTrack = track.copyWith(zoomState: canvasData.zoomState);
      ref.read(trackRepositoryProvider).updateTrack(newTrack);
      ref.read(selectedTrackProvider.notifier).state = newTrack;
    } else {
      PreferenceStorage.saveZoomState(canvasData.zoomState);
    }
  }

  void updateCoverToCoverTrack() async {
    final container = ProviderScope.containerOf(navigatorKey.currentContext!);

    final selectedTrack = container.read(selectedTrackProvider);
    if (selectedTrack != null &&
        selectedTrack.type == TrackType.readingCoverToCover) {
      final updatedSelectedTrack = await container
          .read(trackRepositoryProvider)
          .getTrack(selectedTrack.id);
      if (updatedSelectedTrack == null) return;

      final currentSpreadData = canvasData.currentSpreadData;
      final rightQuranPage = currentSpreadData.rightPageData.quranPage;
      final leftQuranPage = currentSpreadData.leftPageData.quranPage;

      if (rightQuranPage == null) return;

      final firstJuzNumber = rightQuranPage.juzNumber;
      final lastJuzNumber = leftQuranPage?.juzNumber;
      final rightVerseList = rightQuranPage.verseList;
      final leftVerseList = leftQuranPage?.verseList ?? [];
      final verseList = leftVerseList + rightVerseList;

      final firstVerse = verseList.first;
      final lastVerse = verseList.last;

      final newTrackRange = TrackRange(
        startJuzNumber: firstJuzNumber,
        startPageNumber: rightQuranPage.pageNumber,
        startSurahNumber: firstVerse.surahId,
        startVerseNumber: firstVerse.verseKey.parseVerseNumber(),
        endJuzNumber: lastJuzNumber ?? firstJuzNumber,
        endPageNumber: leftQuranPage?.pageNumber ?? rightQuranPage.pageNumber,
        endSurahNumber: lastVerse.surahId,
        endVerseNumber: lastVerse.verseKey.parseVerseNumber(),
      );

      final updatedTrack = updatedSelectedTrack.copyWith(range: newTrackRange);

      if (mounted) {
        container.read(trackRepositoryProvider).updateTrack(updatedTrack);
        ref.read(selectedTrackProvider.notifier).state = updatedTrack;
      }
    }
  }

  void setupScaleAnimation() {
    scaleAnimationController = AnimationController(
      vsync: this,
      duration: defaultAnimationDuration,
    );
    scaleCurvedAnimation = CurvedAnimation(
      parent: scaleAnimationController,
      curve: Curves.linearToEaseOut,
    );

    scaleTween = Tween<double>(begin: 1.0, end: 1.0);
    scaleRightTweenAnimation = scaleTween.animate(scaleCurvedAnimation);
    scaleRightTweenAnimation.addListener(() {
      canvasData.rightZoomScale = scaleRightTweenAnimation.value;
      canvasData.currentSpreadData.currentSinglePageData.zoomInLevel1Scale =
          scaleRightTweenAnimation.value;

      final isScaleOutToDefaultZoom =
          canvasData.isDefaultZoom() && canvasData.isScaleOut();
      if (isScaleOutToDefaultZoom) {
        final normalizedValue =
            _getNormalizedAnimationValue(PagePosition.right);
        canvasData.rightFrameAlpha = normalizedValue;
        return;
      }

      final isScaleInToZoomedInLevel1 =
          canvasData.isZoomedInLevel1() && canvasData.isScaleIn();
      final isScaleInToZoomedInLevel2 =
          canvasData.isZoomedInLevel2() && canvasData.isScaleIn();
      if (isScaleInToZoomedInLevel1 || isScaleInToZoomedInLevel2) {
        final normalizedValue =
            _getNormalizedAnimationValue(PagePosition.right);
        canvasData.rightFrameAlpha = 1 - normalizedValue;
        return;
      }
    });
    scaleRightTweenAnimation.addStatusListener((status) {
      final isZoomFromDefaultToLevel1 =
          canvasData.previousZoomState == ZoomState.defaultZoom &&
              canvasData.zoomState == ZoomState.zoomedInLevel1;
      final isZoomFromLevel1ToDefault =
          canvasData.previousZoomState == ZoomState.zoomedInLevel1 &&
              canvasData.zoomState == ZoomState.defaultZoom;
      final isZoomFromLevel1ToLevel2 =
          canvasData.previousZoomState == ZoomState.zoomedInLevel1 &&
              canvasData.zoomState == ZoomState.zoomedInLevel2;
      final isZoomFromLevel2ToLevel1 =
          canvasData.previousZoomState == ZoomState.zoomedInLevel2 &&
              canvasData.zoomState == ZoomState.zoomedInLevel1;

      if (status == AnimationStatus.completed) {
        canvasData.resetCachedPage();
      }

      canvasData.setUseCachedPage(
        !isZoomFromDefaultToLevel1 &&
            !isZoomFromLevel1ToDefault &&
            !isZoomFromLevel1ToLevel2 &&
            !isZoomFromLevel2ToLevel1,
      );
    });

    scaleLeftTweenAnimation = scaleTween.animate(scaleCurvedAnimation);
    scaleLeftTweenAnimation.addListener(() {
      canvasData.leftZoomScale = scaleLeftTweenAnimation.value;

      final isScaleOutToDefaultZoom =
          canvasData.isDefaultZoom() && canvasData.isScaleOut();
      if (isScaleOutToDefaultZoom) {
        final normalizedValue = _getNormalizedAnimationValue(PagePosition.left);
        canvasData.leftFrameAlpha = normalizedValue;
        return;
      }

      final isScaleInToZoomedInLevel1 =
          canvasData.isZoomedInLevel1() && canvasData.isScaleIn();
      if (isScaleInToZoomedInLevel1) {
        final normalizedValue = _getNormalizedAnimationValue(PagePosition.left);
        canvasData.leftFrameAlpha = 1 - normalizedValue;
        return;
      }
    });
  }

  void setupScrollAnimation() {
    scrollAnimationController = AnimationController(
      vsync: this,
      duration: scrollAnimationDuration,
    );
    scrollCurvedAnimation = CurvedAnimation(
        parent: scrollAnimationController, curve: Curves.decelerate);
    scrollTween = Tween<double>(begin: 0.0, end: 0.0);
    scrollTweenAnimation = scrollTween.animate(scrollCurvedAnimation);
    scrollTweenAnimation.addListener(() {
      canvasData.scrollY = scrollTweenAnimation.value;
    });
  }

  void setupSwipeAnimation() {
    swipeAnimationController = AnimationController(
      vsync: this,
      duration: swipeAnimationDuration,
    );
    swipeCurvedAnimation = CurvedAnimation(
        parent: swipeAnimationController, curve: Curves.decelerate);
    swipeTween = Tween<double>(
        begin: canvasData.currentPositionOffset,
        end: canvasData.currentPositionOffset);
    swipeTweenAnimation = swipeTween.animate(swipeCurvedAnimation);
    swipeTweenAnimation.addListener(() {
      canvasData.currentPositionOffset = swipeTweenAnimation.value;
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      refreshPainter();

      final container = ProviderScope.containerOf(navigatorKey.currentContext!);
      final highlightedVerseKey = container.read(highlightedVerseKeyProvider);

      if (highlightedVerseKey != null && RecitationPlayer.player.playing) {
        final verse = RecitationPlayer.currentVerseList.firstWhereOrNull(
            (verse) => verse.verseKey == highlightedVerseKey.verseKey);
        if (verse == null) return;

        final spreadNumber = (verse.pageId / 2).round();
        final pagerNumber =
            (canvasData.isSpread()) ? spreadNumber : verse.pageId;
        moveToDesiredPager(
          currentPager: pagerNumber.toDouble(),
          nextPager: pagerNumber.toDouble(),
          isInitial: true,
        );
      }
    }

    super.didChangeAppLifecycleState(state);
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    removeBookmarkTooltip();
    scaleAnimationController.dispose();
    scrollAnimationController.dispose();
    swipeAnimationController.dispose();
    painterNotifier.dispose();
    QuranTextCacheUtils.disposeCacheCallback();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        removeBookmarkTooltip();
      },
      child: GestureDetector(
        key: QuranLayout.quranGestureDetectorKey,
        onTapDown: (details) {
          ref.read(showPageNavigationBarProvider.notifier).state = false;
          removeBookmarkTooltip();
        },
        onScaleStart: (details) {
          ref.read(showPageNavigationBarProvider.notifier).state = false;
          removeBookmarkTooltip();

          _pinchScaleFactor = _pinchBaseScaleFactor;
          _lastX = details.focalPoint.dx;
          _lastY = details.focalPoint.dy;
          initialVerticalDragGlobalY = details.focalPoint.dy;
          isScrolling = false;
          determineScrolling = true;
          isSwiping = false;
          swipeOutOfRange = false;
          determineSwiping = true;
          // todo: _controller!.stop();?
        },
        onScaleUpdate: (details) {
          _pinchScaleFactor = _pinchBaseScaleFactor * details.scale;

          final curX = details.focalPoint.dx;
          final curY = details.focalPoint.dy;
          final isHorizontalValueChanged = curX != _lastX;
          final angle = MathHelper.getAngle(_lastX, _lastY, curX, curY);
          _lastX = curX;
          _lastY = curY;

          final scrollUpEnabled =
              MathHelper.withinTolerance(angle, 270, scrollAngleToleranceValue);
          final scrollDownEnabled =
              MathHelper.withinTolerance(angle, 90, scrollAngleToleranceValue);
          final dragLeftEnabled =
              MathHelper.withinTolerance(angle, 180, scrollAngleToleranceValue);
          final dragRightEnabled = MathHelper.withinTolerance(
                  angle, 360, scrollAngleToleranceValue) ||
              MathHelper.withinTolerance(angle, 0, scrollAngleToleranceValue);

          final isPinching = details.pointerCount > 1;
          final dragEnabled = dragLeftEnabled || dragRightEnabled;

          if (dragEnabled &&
              !isPinching &&
              determineSwiping &&
              isHorizontalValueChanged) {
            isSwiping = true;

            if (dragRightEnabled &&
                canvasData.currentPositionOffset >=
                    (canvasData.positionOffsetUpperLimit ??
                        canvasData.maxPositionOffset)) {
              isSwiping = false;
              swipeOutOfRange = true;
            }
            if (dragLeftEnabled &&
                canvasData.currentPositionOffset <=
                    canvasData.positionOffsetLowerLimit) {
              isSwiping = false;
              swipeOutOfRange = true;
            }

            isScrolling = false;
            determineScrolling = false;
          }

          if ((scrollUpEnabled || scrollDownEnabled) &&
              !isPinching &&
              determineScrolling) {
            isScrolling = true;
            isSwiping = false;
            swipeOutOfRange = false;
            determineSwiping = false;
          }

          if (isPinching) {
            determineScrolling = false;
            determineSwiping = false;
          }

          if (isScrolling) {
            handleScrolling(
                details.focalPointDelta.dy, canvasData.canvasHeight);
          } else if (isSwiping) {
            handleSwiping(details.focalPointDelta.dx, canvasData.canvasWidth);
          }

          if (swipeOutOfRange) {
            final toastRunning = outOfRangeToast?.isStarted == true ||
                outOfRangeToast?.isRunning == true;
            final currentTrack = ref.read(selectedTrackProvider);
            final isAiTrack = currentTrack?.type == TrackType.memorizing ||
                currentTrack?.type == TrackType.readingWithAi;
            if (!toastRunning && isAiTrack) {
              outOfRangeToast = CommonUtils.createToast(
                  context.tr('quran.cannot_go_out_of_range'));
            }
          }
        },
        onScaleEnd: (details) {
          if (isScrolling) {
            handleSmoothScrolling(
                details.velocity.pixelsPerSecond.dy, canvasData.canvasHeight);
          } else if (isSwiping) {
            handleSmoothSwipe(
                details.velocity.pixelsPerSecond.dx, canvasData.canvasWidth);
          } else {
            final quranMode = ref.read(quranModeProvider.notifier).state;

            final isPortrait = canvasData.orientation == Orientation.portrait;
            final isDefaultMode = quranMode is DefaultQuranMode;

            if (isPortrait && isDefaultMode) {
              handleZooming(
                canvasData.canvasWidth,
                canvasData.frameWidth,
                canvasData.frameHeight,
              );
            }
          }
        },
        onTapUp: (details) {
          final quranMode = ref.read(quranModeProvider.notifier).state;
          if (quranMode is DefaultQuranMode) {
            final isHighlightingVerse = handleVerseTap(details.localPosition);

            final pageNavigationSuccess =
                handlePageNumberNavigationTap(details.localPosition);
            if (pageNavigationSuccess) return; // to prevent overlapping click

            final surahJuzNavigationSuccess =
                handleSurahJuzNavigationTap(details.localPosition);
            if (surahJuzNavigationSuccess) {
              return; // to prevent overlapping click
            }

            if (isHighlightingVerse) {
              setQuranWidgetState(true, true);
            } else {
              toggleQuranWidgetState();
            }
          } else {
            handleAlignmentVerseTap(details.localPosition);
          }
        },
        onLongPressStart: (details) {
          showBookmarkTooltip(details.localPosition);
        },
        child: OrientationBuilder(
          builder: (context, orientation) {
            if (canvasData.orientation != orientation) {
              canvasData.renderMushaf = false;

              WidgetsBinding.instance.addPostFrameCallback((_) {
                canvasData.renderMushaf = true;
                if (mounted) {
                  final track = ref.read(selectedTrackProvider);
                  final zoomState = (orientation == Orientation.portrait)
                      ? track?.zoomState ?? PreferenceStorage.getZoomState()
                      : null;
                  setupInitialState(zoomState: zoomState);
                  refreshPainter();
                }
              });
            }
            return AnimatedBuilder(
              animation: Listenable.merge([
                scaleRightTweenAnimation,
                scrollAnimationController,
                swipeAnimationController,
              ]),
              builder: (context, child) {
                return QuranCustomPaint(
                  notifier: painterNotifier,
                  width: canvasData.canvasWidth,
                  height: canvasData.canvasHeight,
                  canvasData: canvasData,
                  recitationAlignmentHelper: recitationAlignmentHelper,
                );
              },
            );
          },
        ),
      ),
    );
  }

  void zoomFromZoomedOutToDefaultZoom() {
    canvasData.zoomState = ZoomState.defaultZoom;
    // todo handle left zooming
    startZoomAnimation(beginRight: canvasData.rightZoomScale, endRight: 1);
    canvasData.rightZoomScale = 1;
  }

  void zoomFromDefaultZoomToZoomedInLevel1(int frameHeight) {
    canvasData.zoomState = ZoomState.zoomedInLevel1;
    final newZoomScale =
        canvasData.getZoomInLevel1Scale(SpreadPosition.current);

    startZoomAnimation(
        beginRight: canvasData.rightZoomScale, endRight: newZoomScale);
    canvasData.rightZoomScale = newZoomScale;

    canvasData.updateZoomInLevel1Scale();
  }

  void zoomFromZoomedInLevel1ToZoomedInLevel2(int frameHeight) {
    canvasData.cacheQuranTextDirectly = true;

    canvasData.zoomState = ZoomState.zoomedInLevel2;
    canvasData.rightZoomScale =
        canvasData.getZoomInLevel2Scale(SpreadPosition.current);
    scaleAnimationController.duration = Duration.zero; // disable the animation
    startZoomAnimation(
        beginRight: canvasData.rightZoomScale,
        endRight: canvasData.rightZoomScale);
  }

  void zoomFromDefaultZoomToZoomedOut() {
    canvasData.zoomState = ZoomState.zoomedOut;
    final newZoomScale = canvasData.getZoomOutScale();

    startZoomAnimation(
        beginRight: canvasData.rightZoomScale, endRight: newZoomScale);
    canvasData.rightZoomScale = newZoomScale;
  }

  void zoomFromZoomedInLevel1ToDefault() {
    canvasData.zoomState = ZoomState.defaultZoom;
    startZoomAnimation(beginRight: canvasData.rightZoomScale, endRight: 1);
    canvasData.rightZoomScale = 1;
  }

  void zoomFromZoomedInLevel2ToZoomedInLevel1(int frameHeight) {
    canvasData.cacheQuranTextDirectly = true;

    canvasData.zoomState = ZoomState.zoomedInLevel1;
    final newZoomScale =
        canvasData.getZoomInLevel1Scale(SpreadPosition.current);

    scaleAnimationController.duration = Duration.zero; // disable the animation
    startZoomAnimation(
        beginRight: canvasData.rightZoomScale, endRight: newZoomScale);
    // set back to default after the animation
    scaleAnimationController.duration = defaultAnimationDuration;
    canvasData.rightZoomScale = newZoomScale;

    canvasData.updateZoomInLevel1Scale();
  }

  void startZoomAnimation({
    required double beginRight,
    required double endRight,
    double? beginLeft,
    double? endLeft,
  }) {
    scaleTween
      ..begin = beginRight
      ..end = endRight;
    scaleRightTweenAnimation = scaleTween.animate(scaleCurvedAnimation);

    if (beginLeft != null && endLeft != null) {
      // scaleTween
      //   ..begin = beginLeft
      //   ..end = endLeft;
    }
    // scaleLeftTweenAnimation = scaleTween.animate(scaleCurvedAnimation);

    scaleAnimationController.reset();
    scaleAnimationController.forward();
  }

  void handleZooming(double canvasWidth, int frameWidth, int frameHeight) {
    canvasData.previousZoomState = canvasData.zoomState;

    if (_pinchScaleFactor > _pinchBaseScaleFactor) {
      canvasData.scaleState = ScaleState.scaleIn; // zooming in
      canvasData.scrollY = 0;

      if (canvasData.zoomState == ZoomState.zoomedOut &&
          canvasData.isMobile == true) {
        zoomFromZoomedOutToDefaultZoom();
      } else if (canvasData.zoomState == ZoomState.defaultZoom) {
        zoomFromDefaultZoomToZoomedInLevel1(frameHeight);
      } else if (canvasData.zoomState == ZoomState.zoomedInLevel1 &&
          canvasData.isMobile == true) {
        zoomFromZoomedInLevel1ToZoomedInLevel2(frameHeight);
      }
    } else if (_pinchScaleFactor < _pinchBaseScaleFactor) {
      canvasData.scaleState = ScaleState.scaleOut; // zooming out
      canvasData.scrollY = 0;

      if (canvasData.zoomState == ZoomState.defaultZoom &&
          canvasData.isMobile == true) {
        zoomFromDefaultZoomToZoomedOut();
      } else if (canvasData.zoomState == ZoomState.zoomedInLevel1) {
        zoomFromZoomedInLevel1ToDefault();
      } else if (canvasData.zoomState == ZoomState.zoomedInLevel2 &&
          canvasData.isMobile == true) {
        zoomFromZoomedInLevel2ToZoomedInLevel1(frameHeight);
      }
    }

    updateZoomState();
    updatePageNumberTextSize();
    updatePageNavigationHeight();
    updatePageNavigationTop();
  }

  void handleScrolling(double dy, double maxHeight) {
    double value = canvasData.scrollY;
    value -= dy;
    value = limitScroll(value);

    // disable the animation when user dragging
    scrollAnimationController.duration = Duration.zero;
    startScrollAnimation(begin: value, end: value);
  }

  void handleSmoothScrolling(double velocity, double maxHeight) {
    final distance = velocity * .3;

    double endScrollY = canvasData.scrollY - distance;
    endScrollY = limitScroll(endScrollY);

    // enable smooth scroll animation
    scrollAnimationController.duration = scrollAnimationDuration;
    startScrollAnimation(begin: canvasData.scrollY, end: endScrollY);
  }

  void startScrollAnimation({required double begin, required double end}) {
    scrollTween
      ..begin = begin
      ..end = end;
    scrollTweenAnimation = scrollTween.animate(scrollCurvedAnimation);

    scrollAnimationController.reset();
    scrollAnimationController.forward();
  }

  void handleSwiping(double deltaX, double canvasWidth) {
    double currentPager = swipeTweenAnimation.value;
    currentPager += deltaX / canvasWidth;

    // disable the animation when user dragging
    swipeAnimationController.duration = Duration.zero;
    startSwipeAnimation(begin: currentPager, end: currentPager);
  }

  void handleSmoothSwipe(double velocity, double canvasWidth,
      {bool toNextPager = false}) {
    double currentPager = swipeTweenAnimation.value;

    // enable smooth swipe animation
    swipeAnimationController.duration = swipeAnimationDuration;

    final double nextPager;
    if (toNextPager) {
      nextPager = (currentPager + 1).truncateToDouble();
    } else if (velocity.abs() > _swipeVelocityThreshold) {
      if (velocity < 0) {
        // Swipe left, move to previous page
        nextPager = (currentPager - 1).ceilToDouble();
      } else {
        // Swipe right, move to next page
        nextPager = (currentPager + 1).floorToDouble();
      }
    } else {
      nextPager = currentPager.roundToDouble();
    }

    clearStateOnSwipe(nextPager);
    moveToDesiredPager(
        currentPager: currentPager, nextPager: nextPager, isInitial: false);
  }

  void updateCachedPageNumber(int pageNumber) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setInt(pageNumberKey, pageNumber);
  }

  void prepareListeningTrackRecitation(Track track,
      [String? tappedVerseKey]) async {
    final range = track.range;

    final startVerseKey = (range.startVerseNumber == 1)
        ? '${range.startSurahNumber}:0'
        : range.startVerseKey;
    final endVerseKey = range.endVerseKey;

    final String recitingStartVerseKey;
    if (tappedVerseKey != null) {
      recitingStartVerseKey = tappedVerseKey;
    } else if (track.lastHighlightedVerseKey != null) {
      recitingStartVerseKey = track.lastHighlightedVerseKey!;
    } else {
      recitingStartVerseKey = startVerseKey;
    }

    List<QuranVerse> filteredAllPreviousVerseList = [];
    if (canvasData.currentPageNumber != null &&
        canvasData.currentPageNumber! > range.startPageNumber) {
      final quranRepository = ref.read(quranRepositoryProvider);
      for (int pageNumber = range.startPageNumber;
          pageNumber < canvasData.currentPageNumber!;
          pageNumber++) {
        final page = await quranRepository.getCompleteQuranPage(pageNumber);
        final verseList = page?.verseList
            .skipWhile((verse) =>
                verse.verseKey.verseKeyInt < startVerseKey.verseKeyInt)
            .toList();
        filteredAllPreviousVerseList.addAll(verseList ?? []);
      }
    }

    final currentVerseList = canvasData.getCurrentSpreadVerseList();
    final filteredCurrentVerseList = currentVerseList
        .skipWhile(
            (verse) => verse.verseKey.verseKeyInt < startVerseKey.verseKeyInt)
        .takeWhile(
            (verse) => verse.verseKey.verseKeyInt <= endVerseKey.verseKeyInt)
        .toList();

    final nextVerseList = canvasData.getNextSpreadVerseList();
    final filteredNextVerseList = nextVerseList
        .takeWhile(
            (verse) => verse.verseKey.verseKeyInt <= endVerseKey.verseKeyInt)
        .toList();

    final filteredVerseList = filteredAllPreviousVerseList +
        filteredCurrentVerseList +
        filteredNextVerseList;

    startTrackRecitation(track, recitingStartVerseKey, filteredVerseList);
  }

  void startTrackRecitation(
      Track track, String recitingStartVerseKey, List<QuranVerse> verseList) {
    if (verseList.isEmpty) return;

    final asyncValue = ref.read(audioSegmentDataInitProvider);
    if (asyncValue.isLoading) {
      ref.read(recitationPendingProvider.notifier).state =
          RecitationPendingData(recitingStartVerseKey, true);
      showDownloadingWordHighlightToast();
      return;
    }

    RecitationHelper.startTrackRecitation(
      track,
      recitingStartVerseKey,
      verseList,
      onSetup: onRecitationSetup,
      onPosition: onRecitationPosition,
      onIndex: onRecitationIndex,
      onDownloading: showDownloadingAudioToast,
      onLoop: () {
        stopRecitationAndClearHighlight();

        final pageNumber = track.range.startPageNumber;
        final spreadNumber = (pageNumber / 2).round();
        final pagerNumber = (canvasData.isSpread()) ? spreadNumber : pageNumber;

        moveToDesiredPager(
          currentPager: pagerNumber.toDouble(),
          nextPager: pagerNumber.toDouble(),
          isInitial: true,
        );

        statusListener(status) {
          if (status == AnimationStatus.completed) {
            prepareSelectedTrack(track);
            swipeTweenAnimation.removeStatusListener(statusListener);
          }
        }

        swipeTweenAnimation.addStatusListener(statusListener);
      },
      onPlaying: () {
        if (mounted) {
          ref.read(recitationPlayingProvider.notifier).state = true;
        }
      },
      onPause: () {
        if (mounted) {
          ref.read(recitationPlayingProvider.notifier).state = false;
        }
      },
      onStop: () {
        final container =
            ProviderScope.containerOf(navigatorKey.currentContext!);
        container.read(stopRecitationProvider).stop();
      },
      onNextPager: () {},
    );
  }

  void showDownloadingAudioToast() {
    if (mounted) {
      CommonUtils.showToast(context.tr('quran.downloading_audio'));
    }
  }

  void showDownloadingWordHighlightToast() {
    if (mounted) {
      CommonUtils.showToast(context.tr('quran.downloading_word_highlight'));
    }
  }

  void highlightVerse(String verseKey) {
    ref.read(highlightedVerseKeyProvider.notifier).highlight(verseKey);
    canvasData.highlightedTapType = VerseTapType.single;
  }

  void updateTrackHighlightedVerse(String? verseKey) {
    if (mounted) {
      final track = ref.read(selectedTrackProvider);
      if (track?.type == TrackType.readingCoverToCover ||
          track?.type == TrackType.readingHabitual ||
          track?.type == TrackType.listening) {
        ref
            .read(trackRepositoryProvider)
            .updateHighlightedVerse(track!.id, verseKey);
        final updatedTrack =
            track.copyWith(lastHighlightedVerseKey: Nullable(verseKey));
        ref.read(selectedTrackProvider.notifier).state = updatedTrack;
      }
    }
  }

  Future<void> stopRecitationAndClearHighlight() async {
    await RecitationPlayer.stop();
    ref.read(recitationPendingProvider.notifier).state = null;
    ref.read(recitationPlayingProvider.notifier).state = false;

    canvasData.highlightedTapType = null;

    ref.read(highlightedVerseKeyProvider.notifier).highlight(null);

    final quranMode = ref.read(quranModeProvider.notifier).state;
    if (quranMode is DefaultQuranMode) {
      ref.read(highlightedWordProvider.notifier).state = null;
    }

    ref.read(transcriptionWordPlayStateProvider.notifier).state =
        WordPlayState.idle;
  }

  bool handleSurahJuzNavigationTap(Offset position) {
    final currentDirection = Directionality.of(context);
    final bool isRtl = currentDirection == ui.TextDirection.rtl;

    final center =
        Offset(canvasData.canvasWidth / 2, canvasData.canvasHeight / 2);

    // Account for scroll position
    final scrolledPosition =
        Offset(position.dx, position.dy + canvasData.scrollY);

    Offset adjustedPosition;
    if (canvasData.zoomState == ZoomState.zoomedOut) {
      adjustedPosition = Offset(
        center.dx +
            (scrolledPosition.dx - center.dx) / canvasData.rightZoomScale,
        center.dy +
            (scrolledPosition.dy - center.dy) / canvasData.rightZoomScale,
      );
    } else {
      adjustedPosition = Offset(
        center.dx + (scrolledPosition.dx - center.dx),
        center.dy + (scrolledPosition.dy - center.dy),
      );
    }

    final currentSurahRectList =
        canvasData.getCurrentSpreadSurahNavigationRectList();
    for (final rect in currentSurahRectList) {
      if (rect.contains(adjustedPosition)) {
        if (isRtl) {
          Scaffold.of(context).openEndDrawer();
        } else {
          Scaffold.of(context).openDrawer();
        }

        return true;
      }
    }

    final currentJuzRectList =
        canvasData.getCurrentSpreadJuzNavigationRectList();
    for (final rect in currentJuzRectList) {
      if (rect.contains(adjustedPosition)) {
        if (isRtl) {
          Scaffold.of(context).openDrawer();
        } else {
          Scaffold.of(context).openEndDrawer();
        }

        return true;
      }
    }

    return false;
  }

  bool handlePageNumberNavigationTap(Offset position) {
    final center =
        Offset(canvasData.canvasWidth / 2, canvasData.canvasHeight / 2);

    // Account for scroll position
    final scrolledPosition =
        Offset(position.dx, position.dy + canvasData.scrollY);

    Offset adjustedPosition;
    if (canvasData.zoomState == ZoomState.zoomedOut) {
      adjustedPosition = Offset(
        center.dx +
            (scrolledPosition.dx - center.dx) / canvasData.rightZoomScale,
        center.dy +
            (scrolledPosition.dy - center.dy) / canvasData.rightZoomScale,
      );
    } else {
      adjustedPosition = Offset(
        center.dx + (scrolledPosition.dx - center.dx),
        center.dy + (scrolledPosition.dy - center.dy),
      );
    }

    final currentRectList = canvasData.getCurrentSpreadPageNumberRectList();
    for (final rect in currentRectList) {
      if (rect.contains(adjustedPosition) == true) {
        ref.read(showPageNavigationBarProvider.notifier).state = true;
        return true;
      }
    }

    return false;
  }

  void toggleQuranWidgetState() {
    final quranMode = ref.read(quranModeProvider.notifier).state;
    final currentState = ref.read(quranWidgetStateProvider.notifier).state;

    setQuranWidgetState(
      !currentState.showTopBar,
      (quranMode is DefaultQuranMode && !RecitationPlayer.player.playing)
          ? !currentState.showBottomBar
          : true,
    );
  }

  void setQuranWidgetState(bool showTopBar, bool showBottomBar) {
    ref.read(quranWidgetStateProvider.notifier).state = QuranWidgetState(
      showTopBar: showTopBar,
      showBottomBar: showBottomBar,
    );
  }

  bool handleVerseTap(Offset position) {
    // Account for scroll position
    final scrolledPosition =
        Offset(position.dx, position.dy + canvasData.scrollY);

    Offset adjustedPosition;
    if (canvasData.zoomState == ZoomState.zoomedInLevel2) {
      // Use regular scaling for zoomedInLevel2, matching drawQuranPage behavior
      adjustedPosition = Offset(
        scrolledPosition.dx / canvasData.rightZoomScale,
        scrolledPosition.dy / canvasData.rightZoomScale,
      );
    } else {
      // Keep center pivot scaling for other zoom states
      final center =
          Offset(canvasData.canvasWidth / 2, canvasData.canvasHeight / 2);
      adjustedPosition = Offset(
        center.dx +
            (scrolledPosition.dx - center.dx) / canvasData.rightZoomScale,
        center.dy +
            (scrolledPosition.dy - center.dy) / canvasData.rightZoomScale,
      );
    }

    final rectList = canvasData.getCurrentWordRectMap().entries.toList();

    for (var entry in rectList) {
      if (entry.value.contains(adjustedPosition)) {
        final tappedVerseKey = entry.key.verseKey;

        final tapType = verseTapHelper.tapping(tappedVerseKey);
        if (tappedVerseKey == canvasData.highlightedVerseKey &&
            tapType == canvasData.highlightedTapType) {
          stopRecitationAndClearHighlight();
          return false;
        }

        final updateSingleTapHighlight = (tapType == VerseTapType.single &&
            canvasData.highlightedTapType != VerseTapType.double);
        final updateDoubleTapHighlight = tapType == VerseTapType.double;

        if (updateSingleTapHighlight) {
          highlightVerse(tappedVerseKey);
        } else if (updateDoubleTapHighlight) {
          final currentTrack = ref.read(selectedTrackProvider);
          if (currentTrack?.type == TrackType.listening) {
            final range = currentTrack?.range;
            if (range == null) return true;

            final startVerseKey = (range.startVerseNumber == 1)
                ? '${range.startSurahNumber}:0'
                : range.startVerseKey;

            if (tappedVerseKey.verseKeyInt < startVerseKey.verseKeyInt ||
                tappedVerseKey.verseKeyInt > range.endVerseKey.verseKeyInt) {
              CommonUtils.showToast(
                  context.tr('quran.cannot_recite_outside_range'));
              return true;
            }

            prepareListeningTrackRecitation(currentTrack!, tappedVerseKey);
            return true;
          }

          prepareRecitation(tappedVerseKey);
        } else {
          final currentHighlightedVerseKey =
              ref.read(highlightedVerseKeyProvider)?.verseKey;
          if (currentHighlightedVerseKey == tappedVerseKey) return true;

          return false;
        }

        return true;
      }
    }

    return false;
  }

  void onRecitationSetup(String verseKey) {
    final container = ProviderScope.containerOf(navigatorKey.currentContext!);
    container.read(recitationPlayingProvider.notifier).state = true;
    container.read(highlightedVerseKeyProvider.notifier).highlight(verseKey);
    canvasData.highlightedTapType = VerseTapType.double;
  }

  void onRecitationPosition(
      List<QuranVerse> verseList, int reciteVerseCount, milliseconds, index) {
    if (verseList.isEmpty || index < 0) return;

    final container = ProviderScope.containerOf(navigatorKey.currentContext!);
    final verse = verseList.elementAtOrNull(index);
    container
        .read(highlightedVerseKeyProvider.notifier)
        .highlight(verse?.verseKey, true);

    final segment = verse?.audioSegments.firstWhereOrNull((segment) {
      return segment.endTime >= milliseconds;
    });

    if (segment == null) {
      container.read(highlightedWordProvider.notifier).state = null;
      return;
    }

    final word = verse?.words.elementAtOrNull(segment.index);
    container.read(highlightedWordProvider.notifier).state = word;
  }

  void onRecitationIndex(List<QuranVerse> verseList, int prevIndex, int index) {
    final verse = verseList.elementAtOrNull(index);
    if (verse != null) {
      final currentPageNumber = canvasData.currentPageNumber;
      if (verse.pageId != currentPageNumber) {
        final spreadNumber = (verse.pageId / 2).round();
        final pagerNumber =
            (canvasData.isSpread()) ? spreadNumber : verse.pageId;
        moveToDesiredPager(
          currentPager: pagerNumber.toDouble(),
          nextPager: pagerNumber.toDouble(),
          isInitial: true,
        );

        statusListener(status) {
          if (status == AnimationStatus.completed) {
            final nextVerseList = canvasData.getNextSpreadVerseList();
            final currentTrack = ref.read(selectedTrackProvider);
            final endVerseKey = (currentTrack != null &&
                    currentTrack.type == TrackType.listening)
                ? currentTrack.range.endVerseKey
                : "114:6";

            final filteredNextVerseList = nextVerseList
                .takeWhile((verse) =>
                    verse.verseKey.verseKeyInt <= endVerseKey.verseKeyInt)
                .toList();
            RecitationPlayer.addPlaylist(filteredNextVerseList);
            swipeTweenAnimation.removeStatusListener(statusListener);
          }
        }

        swipeTweenAnimation.addStatusListener(statusListener);
      }
    }
  }

  void prepareRecitation(String startVerseKey) {
    final currentVerseList = canvasData.getCurrentSpreadVerseList();
    final filteredCurrentVerseList = currentVerseList
        .skipWhile(
            (verse) => verse.verseKey.verseKeyInt < startVerseKey.verseKeyInt)
        .toList();

    final nextVerseList = canvasData.getNextSpreadVerseList();

    startRecitation(filteredCurrentVerseList + nextVerseList);
  }

  void startRecitation(List<QuranVerse> verseList) {
    if (verseList.isEmpty) return;

    onRecitationSetup(verseList.first.verseKey);

    final asyncValue = ref.read(audioSegmentDataInitProvider);
    if (asyncValue.isLoading) {
      ref.read(recitationPendingProvider.notifier).state =
          RecitationPendingData(verseList.first.verseKey, false);
      showDownloadingWordHighlightToast();
      return;
    }

    RecitationHelper.startRecitation(
      verseList,
      onPosition: onRecitationPosition,
      onIndex: onRecitationIndex,
      onDownloading: showDownloadingAudioToast,
      onPlaying: () {
        if (mounted) {
          ref.read(recitationPlayingProvider.notifier).state = true;
        }
      },
      onPause: () {
        if (mounted) {
          ref.read(recitationPlayingProvider.notifier).state = false;
        }
      },
      onStop: () {
        final container =
            ProviderScope.containerOf(navigatorKey.currentContext!);
        container.read(stopRecitationProvider).stop();
      },
      onNextPager: () {},
    );
  }

  Offset getGlobalPositionFromWordRect(Rect wordRect) {
    // Get center point of the rect
    final rectCenter = wordRect.bottomCenter;

    Offset zoomedPosition = wordRect.bottomCenter;
    if (canvasData.zoomState == ZoomState.zoomedInLevel2) {
      // Use regular scaling for zoomedInLevel2
      zoomedPosition = Offset(
        rectCenter.dx * canvasData.rightZoomScale,
        rectCenter.dy * canvasData.rightZoomScale,
      );
    } else if (canvasData.orientation == Orientation.landscape) {
      // Apply center pivot scaling for other zoom states
      final center =
          Offset(canvasData.canvasWidth / 2, canvasData.canvasHeight / 2);
      zoomedPosition = Offset(
        center.dx + (rectCenter.dx - center.dx) * canvasData.rightZoomScale,
        center.dy + (rectCenter.dy - center.dy) * canvasData.rightZoomScale,
      );
    }

    // Account for scroll position
    return Offset(zoomedPosition.dx, zoomedPosition.dy);
  }

  void handleAlignmentVerseTap(Offset position) {
    toggleQuranWidgetState();

    // Account for scroll position
    final scrolledPosition =
        Offset(position.dx, position.dy + canvasData.scrollY);

    Offset adjustedPosition;
    if (canvasData.zoomState == ZoomState.zoomedInLevel2) {
      // Use regular scaling for zoomedInLevel2, matching drawQuranPage behavior
      adjustedPosition = Offset(
        scrolledPosition.dx / canvasData.rightZoomScale,
        scrolledPosition.dy / canvasData.rightZoomScale,
      );
    } else {
      // Keep center pivot scaling for other zoom states
      final center =
          Offset(canvasData.canvasWidth / 2, canvasData.canvasHeight / 2);
      adjustedPosition = Offset(
        center.dx +
            (scrolledPosition.dx - center.dx) / canvasData.rightZoomScale,
        center.dy +
            (scrolledPosition.dy - center.dy) / canvasData.rightZoomScale,
      );
    }

    final rectList = canvasData.getCurrentWordRectMap().entries.toList();

    for (var entry in rectList) {
      if (entry.value.contains(adjustedPosition)) {
        final tappedVerseKey = entry.key.verseKey;

        final tapType = verseTapHelper.tapping(tappedVerseKey);
        if (tapType == VerseTapType.double) {
          final undo = recitationAlignmentHelper.undoToVerse(tappedVerseKey);
          if (undo) {
            final verseNumber = tappedVerseKey.parseVerseNumber();
            ref.read(undoAlignmentSnackbarProvider.notifier).show(verseNumber);
            undoAlignmentSnackbarTimer?.cancel();
            undoAlignmentSnackbarTimer = Timer(const Duration(seconds: 3), () {
              ref.read(undoAlignmentSnackbarProvider.notifier).show(null);
            });
          }
        }

        break;
      }
    }
  }

  void startSwipeAnimation({required double begin, required double end}) {
    swipeTween
      ..begin = begin
      ..end = end;
    swipeTweenAnimation = swipeTween.animate(swipeCurvedAnimation);

    swipeAnimationController.reset();
    swipeAnimationController.forward();
  }

  void resetData({ZoomState? zoomState}) {
    final quranMode = ref.read(quranModeProvider.notifier).state;

    canvasData.orientation = MediaQuery.of(context).orientation;
    canvasData.scaleState = switch (zoomState) {
      ZoomState.zoomedOut => ScaleState.scaleOut,
      ZoomState.zoomedInLevel1 => ScaleState.scaleIn,
      ZoomState.zoomedInLevel2 => ScaleState.scaleIn,
      _ => ScaleState.none
    };
    canvasData.resetCachedText();
    canvasData.resetCachedPage();
    canvasData.zoomState = zoomState ?? ZoomState.defaultZoom;
    canvasData.previousZoomState = zoomState ?? ZoomState.defaultZoom;
    canvasData.rightFrameAlpha = (quranMode is DefaultQuranMode) ? 1 : 0;

    updateZoomState();

    swipeAnimationController.duration = Duration.zero;
    if (canvasData.isSpread()) {
      final spreadNumber = canvasData.currentSpreadNumber ??
          (widget.initialPageNumber / 2).round();
      moveToDesiredPager(
        currentPager: spreadNumber.toDouble(),
        nextPager: spreadNumber.toDouble(),
        isInitial: true,
      );
    } else {
      final pageNumber =
          canvasData.currentPageNumber ?? widget.initialPageNumber;
      moveToDesiredPager(
        currentPager: pageNumber.toDouble(),
        nextPager: pageNumber.toDouble(),
        isInitial: true,
      );
    }
    swipeAnimationController.duration = swipeAnimationDuration;
  }

  void refreshScale() {
    final double scale = switch (canvasData.orientation) {
      Orientation.portrait => switch (canvasData.zoomState) {
          ZoomState.zoomedOut => canvasData.getZoomOutScale(),
          ZoomState.defaultZoom => 1,
          ZoomState.zoomedInLevel1 =>
            canvasData.getZoomInLevel1Scale(SpreadPosition.current),
          ZoomState.zoomedInLevel2 =>
            canvasData.getZoomInLevel2Scale(SpreadPosition.current),
        },
      Orientation.landscape =>
        (canvasData.isMobile) ? canvasData.getPhoneLandscapeScale() : 1,
      _ => 1,
    };

    scaleAnimationController.duration = Duration.zero; // disable the animation
    startZoomAnimation(
        beginRight: scale, endRight: scale, beginLeft: scale, endLeft: 0);
    scaleAnimationController.duration = defaultAnimationDuration;
  }

  void setupInitialScrollY() {
    final zoomedTextHeight = canvasData.getZoomedTextHeight(
      SpreadPosition.current,
      PagePosition.right,
    );
    final pageHeight = canvasData.getPageHeight(zoomedTextHeight);
    final landscapeScrollY = -(pageHeight - canvasData.canvasHeight) / 2;

    canvasData.startScrollY = switch (canvasData.orientation) {
      Orientation.landscape => landscapeScrollY,
      _ => 0,
    };

    scrollAnimationController.duration = Duration.zero;
    startScrollAnimation(
        begin: canvasData.startScrollY, end: canvasData.startScrollY);
  }
}
