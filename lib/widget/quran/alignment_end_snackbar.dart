import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/resource/color_resource.dart';

class AlignmentEndSnackbar extends ConsumerStatefulWidget {
  const AlignmentEndSnackbar({super.key});

  @override
  ConsumerState<AlignmentEndSnackbar> createState() =>
      _AlignmentEndSnackbarState();
}

class _AlignmentEndSnackbarState extends ConsumerState<AlignmentEndSnackbar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    ref.listenManual(showAlignmentEndSnackbarProvider, (_, show) {
      if (show) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _offsetAnimation,
      child: FadeTransition(
        opacity: _opacityAnimation,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(
              Radius.circular(8),
            ),
            color: ColorResource.backgroundGrey(context.isDarkMode),
            boxShadow: [
              const BoxShadow(
                offset: Offset(0, 4),
                blurRadius: 6,
                color: Color(0x1A000000),
              ),
            ],
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Icon(
                Icons.check_circle_outline,
                size: 16,
                color: ColorResource.primary(context.isDarkMode),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  context.tr('quran.reached_end'),
                  style: TextStyle(
                    fontSize: 12,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
              IconButton(
                onPressed: () {
                  ref.read(showAlignmentEndSnackbarProvider.notifier).state =
                      false;
                },
                icon: const Icon(Icons.close),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
