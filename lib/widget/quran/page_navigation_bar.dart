import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/quran/juz_name_script.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/quran/quran_screen.dart';
import 'package:mushafi/resource/color_resource.dart';

class PageNavigationBar extends ConsumerStatefulWidget {
  final double maxWidth;

  const PageNavigationBar(this.maxWidth, {super.key});

  @override
  ConsumerState<PageNavigationBar> createState() => _PageNavigationBarState();
}

class _PageNavigationBarState extends ConsumerState<PageNavigationBar> {
  late final FixedExtentScrollController scrollController;
  late final JuzNameScript juzNameScript;

  Timer? delayTimer;

  @override
  void initState() {
    final pageData = ref.read(quranPageDataProvider).data;
    final pageNumber = pageData.currentRightPage?.pageNumber;
    if (pageNumber != null) {
      scrollController =
          FixedExtentScrollController(initialItem: pageNumber - 1);
    } else {
      scrollController = FixedExtentScrollController();
    }

    juzNameScript = PreferenceStorage.getJuzNameScript();

    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    delayTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quranNavigationHeight =
        ref.watch(quranNavigationHeightNotifierProvider);
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final navigationTextSize = ref.watch(navigationTextSizeProvider);

    final backgroundColor = mushafDesignAsync.value?.design.backgroundColor ??
        MushafDesign.getDefault(context.isDarkMode).backgroundColor;

    return Container(
      color: backgroundColor,
      width: widget.maxWidth,
      height: quranNavigationHeight,
      child: Stack(
        children: [
          Center(
            child: Container(
              width: 50, // Same as itemExtent
              height: 50, // Same as itemExtent
              decoration: BoxDecoration(
                color: ColorResource.defaultHighlightFaded(context.isDarkMode),
                shape: BoxShape.circle,
              ),
            ),
          ),
          RotatedBox(
            quarterTurns: 1,
            child: ListWheelScrollView.useDelegate(
              // Increase this to flatten the view
              diameterRatio: 10,
              // Reduce the perspective effect
              perspective: 0.0001,
              itemExtent: 50,
              physics: const FixedExtentScrollPhysics(),
              controller: scrollController,
              onSelectedItemChanged: (index) {
                delayTimer?.cancel();
                delayTimer = Timer(const Duration(milliseconds: 500), () {
                  ref
                      .read(pageNavigationProvider.notifier)
                      .navigateToPage(index + 1);
                });

                setState(() {});
              },
              childDelegate: ListWheelChildBuilderDelegate(
                childCount: 604,
                builder: (context, index) {
                  final number = index + 1;
                  final value = switch (juzNameScript) {
                    JuzNameScript.latin => number.toString(),
                    JuzNameScript.arabic =>
                      NumberFormat('#', 'ar_EG').format(number),
                  };
                  return GestureDetector(
                    onTap: () {
                      if (scrollController.selectedItem == index) {
                        ref.read(showPageNavigationBarProvider.notifier).state =
                            false;
                        return;
                      }

                      scrollController.animateToItem(
                        index,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    child: Container(
                      alignment: Alignment.center,
                      child: RotatedBox(
                        quarterTurns: -1,
                        child: Text(
                          value,
                          style: TextStyle(
                            fontSize: navigationTextSize,
                            color:
                                ColorResource.textDefault(context.isDarkMode),
                            fontWeight: scrollController.selectedItem == index
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          IgnorePointer(
            child: Center(
              child: Container(
                width: 50, // Same as itemExtent
                height: 50, // Same as itemExtent
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color:
                        ColorResource.defaultHighlightFull(context.isDarkMode),
                    width: 4,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
