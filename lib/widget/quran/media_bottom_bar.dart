import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/quran_widget_state.dart';
import 'package:mushafi/presentation/model/quran/recitation_pending_data.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/settings/reciter_options_bottom_sheet.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/recitation_player.dart';

class MediaBottomBar extends ConsumerStatefulWidget {
  final Function() onTranslationIconClicked;

  const MediaBottomBar({super.key, required this.onTranslationIconClicked});

  @override
  ConsumerState<MediaBottomBar> createState() => _MediaBottomBarState();
}

class _MediaBottomBarState extends ConsumerState<MediaBottomBar>
    with SingleTickerProviderStateMixin {
  late AnimationController reciterOptionAnimationController;
  bool translationSheetShown = false;
  bool recitationPreviouslyPlaying = false;

  @override
  void initState() {
    reciterOptionAnimationController =
        BottomSheet.createAnimationController(this);
    reciterOptionAnimationController.addStatusListener((status) {
      if (status == AnimationStatus.dismissed) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (navigatorKey.currentContext != null) {
            final container =
                ProviderScope.containerOf(navigatorKey.currentContext!);

            String? reciterKey;

            final currentTrack = container.read(selectedTrackProvider);
            if (currentTrack?.type == TrackType.listening) {
              reciterKey = currentTrack?.reciterKey;
            }

            final highlightedVerseKey = ref.read(highlightedVerseKeyProvider);
            if (mounted &&
                recitationPreviouslyPlaying &&
                highlightedVerseKey != null) {
              container.read(recitationPendingProvider.notifier).state =
                  RecitationPendingData(
                highlightedVerseKey.verseKey,
                currentTrack?.type == TrackType.listening,
              );
            }

            container
                .read(audioSegmentDataInitProvider.notifier)
                .fetchAudioSegmentData(reciterKey: reciterKey);
          }
        });
      }
    });

    ref.listenManual(translationSheetHeightProvider, (prevHeight, nextHeight) {
      setState(() {
        translationSheetShown = nextHeight > 0;
      });
    });

    super.initState();
  }

  @override
  void dispose() {
    reciterOptionAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final recitationPlaying = ref.watch(recitationPlayingProvider);
    final audioSegmentDataInitAsync = ref.watch(audioSegmentDataInitProvider);
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final design = mushafDesignAsync.value?.design;
    final drawerColor = design?.drawer1Color ??
        MushafDesign.getDefault(context.isDarkMode).drawer1Color;
    final isConnected = ref.watch(isConnectedProvider);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: translationSheetShown ? Radius.zero : const Radius.circular(16),
        ),
        color: drawerColor,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Visibility(
            visible: !translationSheetShown,
            maintainSize: true,
            maintainAnimation: true,
            maintainState: true,
            child: IconButton(
              onPressed: widget.onTranslationIconClicked,
              icon: SvgPicture.asset(
                Assets.svgsIcBookOpen,
                colorFilter: ColorFilter.mode(
                  ColorResource.textDefault(context.isDarkMode),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          Expanded(
            child: audioSegmentDataInitAsync.when(
              data: (state) => InkWell(
                onTap: () async {
                  recitationPreviouslyPlaying = RecitationPlayer.player.playing;
                  ref.read(stopRecitationProvider).stop();
                  ref.read(reciterSheetShownProvider.notifier).state = true;

                  await showModalBottomSheet(
                    context: context,
                    backgroundColor: drawerColor,
                    transitionAnimationController:
                        reciterOptionAnimationController,
                    shape: const RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(12.0)),
                    ),
                    builder: (context) {
                      return ReciterOptionsBottomSheet(
                          reciterOptionAnimationController);
                    },
                  );

                  if (mounted) {
                    final currentWidgetState =
                        ref.read(quranWidgetStateProvider);
                    ref.read(quranWidgetStateProvider.notifier).state =
                        QuranWidgetState(
                      showTopBar: currentWidgetState.showTopBar,
                      showBottomBar: true,
                    );
                    ref.read(reciterSheetShownProvider.notifier).state = false;
                  }
                },
                child: Text(
                  (state?.data != null)
                      ? (context.isArabic)
                          ? state!.data!.arabicName
                          : state!.data!.name
                      : '',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
              error: (error, stackTrace) {
                String errorMessage = '';

                if (!isConnected) {
                  errorMessage = 'No internet connection';
                } else {
                  errorMessage = error.toString();
                }

                return Center(
                  child: InkWell(
                    onTap: () async {
                      recitationPreviouslyPlaying =
                          RecitationPlayer.player.playing;
                      ref.read(stopRecitationProvider).stop();
                      ref.read(reciterSheetShownProvider.notifier).state = true;

                      await showModalBottomSheet(
                        context: context,
                        backgroundColor: drawerColor,
                        transitionAnimationController:
                            reciterOptionAnimationController,
                        shape: const RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(12.0)),
                        ),
                        builder: (context) {
                          return ReciterOptionsBottomSheet(
                              reciterOptionAnimationController);
                        },
                      );

                      if (mounted) {
                        final currentWidgetState =
                            ref.read(quranWidgetStateProvider);
                        ref.read(quranWidgetStateProvider.notifier).state =
                            QuranWidgetState(
                          showTopBar: currentWidgetState.showTopBar,
                          showBottomBar: true,
                        );
                        ref.read(reciterSheetShownProvider.notifier).state =
                            false;
                      }
                    },
                    child: Text(
                      errorMessage,
                      style: TextStyle(
                          color: ColorResource.textDefault(context.isDarkMode)),
                    ),
                  ),
                );
              },
              loading: () => Center(
                child: Transform.scale(
                  scale: 0.8,
                  child: const CircularProgressIndicator(),
                ),
              ),
            ),
          ),
          IconButton(
            visualDensity: const VisualDensity(
              horizontal: VisualDensity.minimumDensity,
              vertical: VisualDensity.minimumDensity,
            ),
            padding: EdgeInsets.zero,
            onPressed: () {
              if (recitationPlaying) {
                ref.read(stopRecitationProvider).stop();
              } else {
                ref.read(requestRecitationProvider).request();
              }
            },
            icon: Transform(
              alignment: Alignment.center,
              transform: Matrix4.rotationY((context.isArabic) ? pi : 0),
              child: SvgPicture.asset(
                recitationPlaying
                    ? Assets.svgsIcStopCircle
                    : Assets.svgsIcPlayCircle,
                colorFilter: ColorFilter.mode(
                  ColorResource.textDefault(context.isDarkMode),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          IconButton(
            visualDensity: const VisualDensity(
              horizontal: VisualDensity.minimumDensity,
              vertical: VisualDensity.minimumDensity,
            ),
            padding: EdgeInsets.zero,
            onPressed: () {
              ref.read(requestNextRecitationProvider).request();
            },
            icon: Transform(
              alignment: Alignment.center,
              transform: Matrix4.rotationY((context.isArabic) ? pi : 0),
              child: SvgPicture.asset(
                Assets.svgsIcForwardCircle,
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }
}
