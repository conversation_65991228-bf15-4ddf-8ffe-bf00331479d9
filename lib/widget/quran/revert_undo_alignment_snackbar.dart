import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/resource/color_resource.dart';

class RevertUndoAlignmentSnackbar extends ConsumerStatefulWidget {
  const RevertUndoAlignmentSnackbar({super.key});

  @override
  ConsumerState<RevertUndoAlignmentSnackbar> createState() =>
      _RevertUndoAlignmentSnackbarState();
}

class _RevertUndoAlignmentSnackbarState
    extends ConsumerState<RevertUndoAlignmentSnackbar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    ref.listenManual(undoAlignmentSnackbarProvider,
        (prevVerseNumber, nextVerseNumber) {
      if (nextVerseNumber != null) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final undoVerseNumber = ref.watch(undoAlignmentSnackbarProvider);

    return SlideTransition(
      position: _offsetAnimation,
      child: FadeTransition(
        opacity: _opacityAnimation,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(
              Radius.circular(8),
            ),
            color: ColorResource.backgroundGrey(context.isDarkMode),
            boxShadow: [
              const BoxShadow(
                offset: Offset(0, 4),
                blurRadius: 6,
                color: Color(0x1A000000),
              ),
            ],
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              const Icon(Icons.lightbulb_outline, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  context.tr(
                    'alignment.tap_to_start_again',
                    args: [undoVerseNumber?.toString() ?? ''],
                  ),
                  style: TextStyle(
                    fontSize: 12,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  ref.read(revertUndoAlignmentProvider.notifier).revert();
                  _controller.reverse();
                },
                child: Text(
                  context.tr('alignment.undo'),
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
