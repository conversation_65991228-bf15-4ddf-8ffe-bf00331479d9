import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_data.dart';
import 'package:mushafi/presentation/quran/quran_screen.dart';
import 'package:mushafi/utils/recitation_alignment_helper.dart';
import 'package:mushafi/widget/quran/quran_painter.dart';

class QuranCustomPaint extends ConsumerWidget {
  final ValueNotifier<int> notifier;
  final double width;
  final double height;
  final QuranCanvasData canvasData;
  final RecitationAlignmentHelper recitationAlignmentHelper;

  const QuranCustomPaint({
    super.key,
    required this.notifier,
    required this.width,
    required this.height,
    required this.canvasData,
    required this.recitationAlignmentHelper,
  });

  static const quranCustomPaintKey = ValueKey('quranCustomPaint');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final quranMode = ref.watch(quranModeProvider);

    return RepaintBoundary(
      child: ClipRRect(
        child: CustomPaint(
          key: QuranCustomPaint.quranCustomPaintKey,
          size: Size(width, height),
          painter: QuranPainter(
            notifier: notifier,
            data: canvasData,
            recitationAlignmentHelper: recitationAlignmentHelper,
            quranMode: quranMode,
          ),
        ),
      ),
    );
  }
}
