import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/presentation/model/quran/quran_mode.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/quran/quran_screen.dart';
import 'package:mushafi/widget/common/measure_size.dart';
import 'package:mushafi/widget/quran/alignment_end_snackbar.dart';
import 'package:mushafi/widget/quran/media_bottom_bar.dart';
import 'package:mushafi/widget/quran/quran_top_bar.dart';
import 'package:mushafi/widget/quran/recitation_alignment_bottom_bar.dart';
import 'package:mushafi/widget/quran/revert_undo_alignment_snackbar.dart';
import 'package:mushafi/widget/quran/translation_sheet.dart';

class QuranAdditionalWidget extends ConsumerStatefulWidget {
  final double layoutWidth;
  final double layoutHeight;
  final Future<void> Function() onSetupInferenceRequested;

  const QuranAdditionalWidget({
    super.key,
    required this.layoutWidth,
    required this.layoutHeight,
    required this.onSetupInferenceRequested,
  });

  @override
  ConsumerState<QuranAdditionalWidget> createState() =>
      _QuranAdditionalWidgetState();
}

class _QuranAdditionalWidgetState extends ConsumerState<QuranAdditionalWidget>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  Orientation? currentOrientation;

  final quranTopBarKey = GlobalKey();
  final mediaBottomBarKey = GlobalKey();

  late AnimationController draggingAnimationController;
  late Animation<double> draggingCurvedAnimation;
  late Tween<double> draggingTween;
  late Animation<double> draggingTweenAnimation;

  Duration draggingAnimationDuration = const Duration(milliseconds: 200);

  double topBarHeight = 0;
  double bottomBarHeight = 0;

  void dragging({required double begin, required double end}) {
    draggingTween
      ..begin = begin
      ..end = end;
    draggingTweenAnimation = draggingTween.animate(draggingCurvedAnimation);

    draggingAnimationController.reset();
    draggingAnimationController.forward();
  }

  double get maxSheetHeight {
    return widget.layoutHeight - topBarHeight - bottomBarHeight;
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);

    topBarHeight = widget.layoutHeight;
    bottomBarHeight = widget.layoutHeight;

    draggingAnimationController = AnimationController(
      vsync: this,
      duration: draggingAnimationDuration,
    );
    draggingCurvedAnimation = CurvedAnimation(
      parent: draggingAnimationController,
      curve: Curves.easeOut,
    );
    draggingTween = Tween<double>(begin: 0.0, end: 0.0);
    draggingTweenAnimation = draggingTween.animate(draggingCurvedAnimation);

    draggingTweenAnimation.addListener(() {
      if (mounted) {
        ref
            .read(translationSheetHeightProvider.notifier)
            .setHeight(draggingTweenAnimation.value);
      }
    });

    draggingTweenAnimation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (mounted) {
          ref.read(focusHighlightProvider).focus();
        }
      }
    });

    ref.listenManual(quranWidgetStateProvider, (prevState, nextState) {
      if (!nextState.showBottomBar) {
        ref.read(translationSheetHeightProvider.notifier).reset();

        draggingAnimationDuration = Duration.zero;
        dragging(begin: 0, end: 0);
      }
    });

    super.initState();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    final newOrientation = MediaQuery.of(context).orientation;
    if (currentOrientation != newOrientation) {
      currentOrientation = newOrientation;

      draggingAnimationDuration = Duration.zero;
      dragging(begin: 0, end: 0);
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    draggingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quranMode = ref.watch(quranModeProvider);
    final quranWidgetState = ref.watch(quranWidgetStateProvider);

    return Stack(
      children: [
        AnimatedPositioned(
          width: widget.layoutWidth,
          top: (quranWidgetState.showTopBar) ? 0 : -topBarHeight,
          duration: const Duration(milliseconds: 100),
          child: MeasureSize(
            onChange: (size) {
              if (topBarHeight != size.height) {
                setState(() {
                  topBarHeight = size.height;
                });
              }
            },
            child: QuranTopBar(key: quranTopBarKey),
          ),
        ),
        AnimatedPositioned(
          width: widget.layoutWidth,
          bottom: (quranWidgetState.showBottomBar) ? 0 : -bottomBarHeight,
          duration: const Duration(milliseconds: 100),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (quranMode is MemorizationQuranMode ||
                  quranMode is RecitationCheckerQuranMode) ...[
                const Stack(
                  children: [
                    Padding(
                      padding: EdgeInsets.all(8.0),
                      child: RevertUndoAlignmentSnackbar(),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8.0),
                      child: AlignmentEndSnackbar(),
                    ),
                  ],
                ),
                MeasureSize(
                  onChange: (size) {
                    if (bottomBarHeight != size.height) {
                      setState(() {
                        bottomBarHeight = size.height;
                      });
                    }
                  },
                  child: RecitationAlignmentBottomBar(
                      widget.onSetupInferenceRequested),
                ),
              ],
              if (quranMode is DefaultQuranMode) ...[
                TranslationSheet(
                  draggingAnimation: draggingTweenAnimation,
                  onDragging: (delta) {
                    draggingAnimationDuration = Duration.zero;
                    double animationValue = draggingTweenAnimation.value;
                    animationValue -= delta;
                    animationValue = animationValue.clamp(0, maxSheetHeight);

                    dragging(begin: animationValue, end: animationValue);
                  },
                  onCloseIconClicked: () {
                    draggingAnimationDuration =
                        const Duration(milliseconds: 200);

                    draggingTween
                      ..begin = 0
                      ..end = draggingTweenAnimation.value;
                    draggingTweenAnimation =
                        draggingTween.animate(draggingCurvedAnimation);

                    draggingAnimationController.reverse();
                  },
                ),
                MeasureSize(
                  onChange: (size) {
                    if (bottomBarHeight != size.height) {
                      setState(() {
                        bottomBarHeight = size.height;
                      });
                    }
                  },
                  child: MediaBottomBar(
                    key: mediaBottomBarKey,
                    onTranslationIconClicked: () {
                      draggingAnimationDuration =
                          const Duration(milliseconds: 200);
                      dragging(begin: 0, end: maxSheetHeight / 2);
                    },
                  ),
                ),
              ]
            ],
          ),
        ),
      ],
    );
  }
}
