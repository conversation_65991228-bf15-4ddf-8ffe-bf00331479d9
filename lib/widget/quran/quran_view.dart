import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_data.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/quran/quran_layout.dart';

class QuranView extends ConsumerStatefulWidget {
  final QuranCanvasData quranCanvasData;
  final double width;
  final double height;

  const QuranView(this.quranCanvasData, this.width, this.height, {super.key});

  static const errorMessageKey = ValueKey('errorMessage');

  @override
  ConsumerState<QuranView> createState() => _QuranViewState();
}

class _QuranViewState extends ConsumerState<QuranView> {
  @override
  Widget build(BuildContext context) {
    final isMobileAsync = ref.watch(isMobileProvider);
    final mushafDataInitAsync = ref.watch(mushafDataInitProvider);
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final navigationPage = ref.watch(pageNavigationProvider);
    final isConnected = ref.watch(isConnectedProvider);

    final backgroundColor = mushafDesignAsync.value?.design.backgroundColor ??
        MushafDesign.getDefault(context.isDarkMode).backgroundColor;

    return Container(
      color: backgroundColor,
      child: Builder(builder: (context) {
        if (isMobileAsync.isLoading || mushafDesignAsync.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (mushafDataInitAsync.isLoading) {
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 8),
                Text(context.tr('quran.downloading')),
              ],
            ),
          );
        } else if (isMobileAsync.hasError ||
            mushafDataInitAsync.hasError ||
            mushafDesignAsync.hasError) {
          String errorMessage = '';

          if (!isConnected) {
            errorMessage = 'No internet connection';
          } else {
            errorMessage = isMobileAsync.error?.toString() ??
                mushafDataInitAsync.error?.toString() ??
                mushafDesignAsync.error?.toString() ??
                'Failed to load data';
          }

          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  errorMessage,
                  textAlign: TextAlign.center,
                  key: QuranView.errorMessageKey,
                ),
                FilledButton(
                  onPressed: () async {
                    ref.invalidate(mushafDataInitProvider);
                  },
                  style: FilledButton.styleFrom(
                    backgroundColor: ColorResource.primary(context.isDarkMode),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(32),
                    ),
                  ),
                  child: Text(
                    'Retry',
                    style: TextStyle(
                      color: ColorResource.onPrimary(context.isDarkMode),
                      fontSize: 14,
                    ),
                  ),
                )
              ],
            ),
          );
        }

        widget.quranCanvasData.setDarkMode(context.isDarkMode);

        isMobileAsync.whenData((isMobile) {
          widget.quranCanvasData.setIsMobile(isMobile);
        });

        mushafDesignAsync.whenData((asset) {
          final canvasData = widget.quranCanvasData;
          canvasData.updateImageAsset(asset);
          canvasData.setCanvasSize(widget.width, widget.height);
          canvasData.calculateDimension((navigationHeight) {
            Future(() {
              final scale =
                  (canvasData.scaleWholePage()) ? canvasData.rightZoomScale : 1;

              ref
                  .read(quranNavigationHeightNotifierProvider.notifier)
                  .updateValue(canvasData.navigationHeight * scale);
            });
          });
        });

        return QuranLayout(
          widget.quranCanvasData,
          navigationPage ?? 1,
        );
      }),
    );
  }
}
