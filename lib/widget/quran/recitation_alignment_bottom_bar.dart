import 'dart:async';

import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/memorization/alignment_mistake_screen.dart';
import 'package:mushafi/presentation/model/quran/alignment_word_highlight.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/quran_mode.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/presentation/quran/quran_screen.dart';
import 'package:mushafi/presentation/settings/reciter_options_bottom_sheet.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/common_utils.dart';
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:permission_handler/permission_handler.dart';

class RecitationAlignmentBottomBar extends ConsumerStatefulWidget {
  final Future<void> Function() onSetupInferenceRequested;

  const RecitationAlignmentBottomBar(this.onSetupInferenceRequested,
      {super.key});

  @override
  ConsumerState<RecitationAlignmentBottomBar> createState() =>
      _RecitationAlignmentBottomBarState();
}

class _RecitationAlignmentBottomBarState
    extends ConsumerState<RecitationAlignmentBottomBar>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  get inferencePipeline => InferencePipeline.getInstance();

  late AnimationController reciterOptionAnimationController;

  final key = GlobalKey();
  late final AnimationController micAnimationController =
      AnimationController(vsync: this);
  Timer? transcriptionTimer;

  bool isSpeech = false;
  bool isMicLoading = false;

  Future<void> setupInferencePipeline() async {
    await widget.onSetupInferenceRequested();
  }

  Future<void> resetInferencePipeline() async {
    InferencePipeline.getInstance()
      ..stop()
      ..delete();

    await Future.delayed(const Duration(seconds: 1));

    await setupInferencePipeline();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);

    reciterOptionAnimationController =
        BottomSheet.createAnimationController(this);
    reciterOptionAnimationController.addStatusListener((status) {
      if (status == AnimationStatus.dismissed) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ref
              .read(audioSegmentDataInitProvider.notifier)
              .fetchAudioSegmentData();
        });
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final RenderBox? renderBox =
          key.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final height = renderBox.size.height;
        ref.read(recitationAlignmentBarHeightProvider.notifier).state = height;
      }
    });

    ref.listenManual(showAlignmentEndSnackbarProvider, (_, show) {
      if (show) {
        setState(() {
          inferencePipeline.reset();
          inferencePipeline.stop();
        });
      }
    });

    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      setState(() {});
    }

    super.didChangeAppLifecycleState(state);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    key.currentState?.dispose();
    transcriptionTimer?.cancel();
    reciterOptionAnimationController.dispose();
    micAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quranMode = ref.watch(quranModeProvider);
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final design = mushafDesignAsync.value?.design;
    final drawer1Color = design?.drawer1Color ??
        MushafDesign.getDefault(context.isDarkMode).drawer1Color;
    final drawer2Color = design?.drawer2Color ??
        MushafDesign.getDefault(context.isDarkMode).drawer2Color;
    final backgroundColor = design?.backgroundColor ??
        MushafDesign.getDefault(context.isDarkMode).backgroundColor;

    return Container(
      key: key,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(16),
        ),
        color: drawer1Color,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildIconTextButton(
              iconPath: Assets.svgsIcRestart,
              text: context.tr('quran.restart_activity'),
              onPressed: restartActivity,
            ),
            if (quranMode is MemorizationQuranMode)
              _buildIconTextButton(
                iconPath: Assets.svgsIcDoubleChevronLeft,
                text: context.tr('quran.reveal_verse'),
                onPressed: revealVerse,
              ),
            _buildIconTextButton(
              iconPath: Assets.svgsIcChevronLeft16px,
              text: (quranMode is MemorizationQuranMode)
                  ? context.tr('quran.reveal_word')
                  : context.tr('quran.next_word'),
              onPressed: revealWord,
            ),
            _buildStartMicButton(drawer2Color),
            _buildIconTextButton(
              iconPath: Assets.svgsIcArrowUpRight,
              text: context.tr('quran.undo_word'),
              onPressed: undoWord,
            ),
            if (quranMode is MemorizationQuranMode)
              _buildIconTextButton(
                iconPath: Assets.svgsIcHint,
                text: context.tr('quran.show_hint'),
                onPressed: showHint,
              ),
            _buildIconTextButton(
              iconPath: Assets.svgsIcMoreHorizontal,
              text: context.tr('quran.more_options'),
              onPressed: () {
                showMoreOptionsDialog(context, drawer1Color, backgroundColor);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconTextButton({
    required String iconPath,
    required String text,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onPressed,
        child: Column(
          children: [
            SvgPicture.asset(
              iconPath,
              colorFilter: ColorFilter.mode(
                ColorResource.textDefault(context.isDarkMode),
                BlendMode.srcIn,
              ),
            ),
            Text(
              text,
              style: TextStyle(
                color: ColorResource.textDefault(context.isDarkMode),
                fontSize: 8,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStartMicButton(Color micBackgroundColor) {
    return Expanded(
      child: InkWell(
        onTap: () {
          if (inferencePipeline.isRunning) {
            setState(() {
              inferencePipeline.reset();
              inferencePipeline.stop();
            });
          } else {
            setState(() {
              isMicLoading = true;
            });

            WidgetsBinding.instance.addPostFrameCallback((_) async {
              final permissionStatus = await Permission.microphone.request();
              if (permissionStatus.isGranted) {
                bool success = false;

                await setupInferencePipeline();
                success = await runAlignment();

                if (!success) {
                  int retryCount = 1;
                  while (retryCount < 3) {
                    await resetInferencePipeline();
                    success = await runAlignment();

                    if (success) break;
                    retryCount++;
                  }
                }

                if (success) {
                  HapticFeedback.vibrate();
                } else {
                  showAlignmentFailedToast();
                }

                isMicLoading = false;
                if (mounted) {
                  setState(() {});
                }
              } else if (permissionStatus.isDenied ||
                  permissionStatus.isPermanentlyDenied) {
                showNeedMicrophonePermissionToast();
              }
            });
          }
        },
        child: CircleAvatar(
          backgroundColor: micBackgroundColor,
          child: (inferencePipeline.isRunning)
              ? ColorFiltered(
                  colorFilter: ColorFilter.mode(
                    ColorResource.onPrimary(context.isDarkMode),
                    BlendMode.srcATop,
                  ),
                  child: Lottie.asset(
                    Assets.jsonWaveAnimation,
                    controller: micAnimationController,
                    onLoaded: (composition) {
                      micAnimationController.duration = composition.duration;
                    },
                  ),
                )
              : isMicLoading
                  ? Transform.scale(
                      scale: .8,
                      child: CircularProgressIndicator(
                        color: ColorResource.onPrimary(context.isDarkMode),
                      ),
                    )
                  : SvgPicture.asset(
                      Assets.svgsIcMic,
                      colorFilter: ColorFilter.mode(
                        ColorResource.onPrimary(context.isDarkMode),
                        BlendMode.srcIn,
                      ),
                    ),
        ),
      ),
    );
  }

  void onTranscription(String transcription) {
    final transcriptionWords = transcription.split(' ');
    final lastWordSplit = transcriptionWords
        .lastWhereOrNull((element) => element.isNotEmpty)
        ?.split("@");

    var lastWord =
        lastWordSplit?.lastWhereOrNull((element) => element.isNotEmpty) ?? '';
    if (transcription.endsWith("@")) {
      lastWord = "$lastWord.";
    }

    if (mounted) {
      ref.read(transcriptionProvider.notifier).state = lastWord ?? '';
    }
    talker.debug("onTranscription: $transcription");

    transcriptionTimer?.cancel();
    final delayMillis = (transcription.endsWith("@")) ? 1500 : 10000;
    transcriptionTimer = Timer(Duration(milliseconds: delayMillis), () {
      if (mounted) {
        ref.read(transcriptionProvider.notifier).state = '';
      }
    });
  }

  void onAlignmentResult(
      int ayahId, int wordIndex, bool isCorrect, bool isComplete) async {
    if (mounted) {
      final verse =
          await ref.read(quranRepositoryProvider).getQuranVerseById(ayahId);
      if (verse == null) return;

      ref.read(alignmentResultProvider.notifier).state = AlignmentWordHighlight(
        verseId: ayahId,
        wordIndex: wordIndex,
        word: verse.words[wordIndex],
        pageNumber: verse.pageId,
        isCorrect: isCorrect,
        isComplete: isComplete,
      );
    }
    talker.debug(
        "onAlignmentResult: $ayahId, $wordIndex, $isCorrect, $isComplete");
  }

  Future<bool> runAlignment() async {
    final track = ref.read(selectedTrackProvider.notifier).state;
    if (track?.type == TrackType.memorizing ||
        track?.type == TrackType.readingWithAi) {
      final quranRepository = ref.read(quranRepositoryProvider);

      final startVerseKey =
          '${track!.range.startSurahNumber}:${track.range.startVerseNumber}';
      final startVerse =
          await quranRepository.getQuranVerseByVerseKey(startVerseKey);
      if (startVerse == null) return false;

      final endVerseKey =
          '${track.range.endSurahNumber}:${track.range.endVerseNumber}';
      final endVerse =
          await quranRepository.getQuranVerseByVerseKey(endVerseKey);
      if (endVerse == null) return false;

      inferencePipeline.stop();
      final success = inferencePipeline.align(
        startAyahId: startVerse.id,
        endAyahId: endVerse.id,
        onListening: (isListening) {},
        onSpeech: (isSpeech) {
          if (isSpeech != this.isSpeech) {
            this.isSpeech = isSpeech;
            if (mounted) {
              if (isSpeech) {
                micAnimationController.forward();
              } else {
                micAnimationController.reset();
              }
            }
          }
        },
        onTranscription: onTranscription,
        onAlignmentResult: onAlignmentResult,
      );

      if (!success) return false;

      if (mounted) {
        final currentHighlight =
            ref.read(currentAlignmentHighlightProvider.notifier).state;
        if (currentHighlight != null) {
          final setAlignerCursorResult = inferencePipeline.setAlignerCursor(
            ayahId: currentHighlight.verseId,
            wordIndex: currentHighlight.wordIndex,
          );
          talker.debug("setAlignerCursor: $setAlignerCursorResult");
        }
      }

      return true;
    }

    return false;
  }

  void showNeedMicrophonePermissionToast() {
    CommonUtils.showToast(context.tr('quran.microphone_permission_required'));
  }

  void showAlignmentFailedToast() {
    CommonUtils.showToast(context.tr('voice_search.alignment_failed'));
  }

  void revealWord() {
    ref.read(revealWordProvider.notifier).trigger();
  }

  void revealVerse() {
    ref.read(revealVerseProvider.notifier).trigger();
  }

  void restartActivity() {
    ref.read(restartActivityProvider.notifier).trigger();
  }

  void undoWord() {
    ref.read(undoWordProvider.notifier).trigger();
  }

  void showHint() {
    ref.read(showHintProvider.notifier).trigger();
  }

  void showMoreOptionsDialog(
      BuildContext context, Color drawerColor, Color backgroundColor) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      backgroundColor: drawerColor,
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Stack(
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SvgPicture.asset(
                        Assets.svgsIcCloseCircle,
                        width: 24,
                        height: 24,
                        colorFilter: ColorFilter.mode(
                          ColorResource.textDefault(context.isDarkMode),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: Text(
                      context.tr('quran.more_options_title'),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: ColorResource.textDefault(context.isDarkMode),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: backgroundColor,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    InkWell(
                      onTap: () {
                        final track =
                            ref.read(selectedTrackProvider.notifier).state;
                        if (track == null) return;

                        Navigator.pop(context);
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => AlignmentMistakeScreen(
                              trackId: track.id,
                              range: track.range,
                            ),
                          ),
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          textAlign: TextAlign.center,
                          context.tr('quran.show_mistakes'),
                          style: TextStyle(
                              color: ColorResource.textDefault(
                                  context.isDarkMode)),
                        ),
                      ),
                    ),
                    const Divider(height: .2),
                    InkWell(
                      onTap: () {
                        Navigator.pop(context);
                        ref
                            .read(reciterTranslationOptionDialogShownProvider)
                            .trigger();
                        showModalBottomSheet(
                          context: context,
                          backgroundColor: drawerColor,
                          transitionAnimationController:
                              reciterOptionAnimationController,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                                top: Radius.circular(12.0)),
                          ),
                          builder: (context) {
                            return ReciterOptionsBottomSheet(
                                reciterOptionAnimationController);
                          },
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          textAlign: TextAlign.center,
                          context.tr('quran.select_reciter'),
                          style: TextStyle(
                              color: ColorResource.textDefault(
                                  context.isDarkMode)),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
