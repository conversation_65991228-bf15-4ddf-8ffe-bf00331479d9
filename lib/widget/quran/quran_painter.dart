import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mushafi/presentation/model/quran/juz_name_script.dart';
import 'package:mushafi/presentation/model/quran/quran_mode.dart';
import 'package:mushafi/presentation/model/quran/quran_word.dart';
import 'package:mushafi/presentation/model/quran_canvas/page_position.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_page_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/quran_canvas_spread_data.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/utils/quran_text_cache_utils.dart';
import 'package:mushafi/utils/recitation_alignment_helper.dart';

class QuranPainter extends CustomPainter {
  final ValueNotifier<int> notifier;
  final QuranCanvasData data;
  final RecitationAlignmentHelper recitationAlignmentHelper;
  final QuranMode quranMode;

  QuranPainter({
    required this.notifier,
    required this.data,
    required this.recitationAlignmentHelper,
    required this.quranMode,
  }) : super(repaint: notifier);

  void drawBookHinge(Canvas canvas, Size size, QuranCanvasPageData pageData) {
    canvas.save();

    final pageWidth = data.getPageWidth();
    final pageLeft = (size.width - pageWidth) / 2;
    final pageRight = (pageLeft + pageWidth);
    final pageNumber = pageData.quranPage?.pageNumber ?? 1;
    final hingeLeft = (pageNumber % 2 == 0)
        ? pageRight - (data.bookHingeWidth / 2)
        : pageLeft - (data.bookHingeWidth / 2);

    final hingeRect = Rect.fromLTWH(
        hingeLeft, -data.bookHingeWidth, data.bookHingeWidth, size.height);
    final hingePath = Path()..addRect(hingeRect);
    canvas.drawShadow(hingePath, Colors.black, data.bookHingeWidth, true);

    canvas.restore();
  }

  void drawBookEdge(Canvas canvas, Size size, QuranCanvasPageData pageData) {
    canvas.save();

    final pageWidth = data.getPageWidth();
    final pageLeft = (size.width - pageWidth) / 2;
    final pageRight = pageLeft + pageWidth;
    final pageNumber = pageData.quranPage?.pageNumber ?? 1;

    double totalEdgeDrawnWidth = 0;
    while (totalEdgeDrawnWidth < data.bookEdgeWidth) {
      totalEdgeDrawnWidth += data.bookEdgePartWidth;
      final edgeLeft = (pageNumber % 2 == 0)
          ? pageLeft +
              data.bookEdgeWidth -
              totalEdgeDrawnWidth -
              data.bookEdgePartWidth
          : pageRight - totalEdgeDrawnWidth - data.bookEdgePartWidth;

      final edgeRect = Rect.fromLTWH(
        edgeLeft,
        0,
        data.bookEdgePartWidth,
        size.height,
      );
      final edgePaint = Paint()..color = Colors.black.withOpacity(0.25);
      canvas.drawRect(edgeRect, edgePaint);

      totalEdgeDrawnWidth += data.bookEdgePartWidth;
    }

    canvas.restore();
  }

  void drawOutsideBackground(
      Canvas canvas, Size size, QuranCanvasPageData pageData) {
    canvas.save();

    final pageWidth = data.getPageWidth();
    final outsideLeft = (size.width - data.getPageWidth()) / 2;
    final outsideWidth = pageWidth;

    final zoomedTextHeight = pageData.getZoomedTextHeight(data.zoomState);
    final pageHeight = data.getPageHeight(zoomedTextHeight);
    final outsideHeight = pageHeight;

    final paint = Paint()..color = const Color(0xFF2D2D2D);

    final topBackgroundRect = Rect.fromLTWH(
      outsideLeft,
      -outsideHeight,
      outsideWidth,
      outsideHeight,
    );
    canvas.drawRect(topBackgroundRect, paint);

    final bottomBackgroundRect = Rect.fromLTWH(
      outsideLeft,
      pageHeight,
      outsideWidth,
      pageHeight + outsideHeight,
    );
    canvas.drawRect(bottomBackgroundRect, paint);

    canvas.restore();
  }

  void drawSurahJuzNavigation(
    Canvas canvas,
    Size canvasSize,
    QuranCanvasPageData pageData,
    PagePosition position,
  ) {
    final surahTextNavigationPainter = pageData.surahTextNavigationPainter;
    if (surahTextNavigationPainter == null) {
      return;
    }

    final juzTextNavigationPainter = pageData.juzTextNavigationPainter;
    if (juzTextNavigationPainter == null) {
      return;
    }

    canvas.save();

    final navigationCenter = data.navigationHeight / 2;
    final textCenter =
        surahTextNavigationPainter.height * pageData.navigationTextScale / 2;
    final centerVertical = navigationCenter - textCenter;

    canvas.translate(0, centerVertical);
    canvas.scale(pageData.navigationTextScale, pageData.navigationTextScale);

    final scaledFrameWidth = data.frameWidth * data.frameScale;
    final exactScaledFrameWidth =
        scaledFrameWidth / pageData.navigationTextScale;
    final exactCanvasWidth = canvasSize.width / pageData.navigationTextScale;
    final exactNavigationWidth = min(exactScaledFrameWidth, exactCanvasWidth);

    // draw surah navigation
    final startSurahX = (exactCanvasWidth - exactNavigationWidth) / 2;
    final surahOffset = Offset(startSurahX, 0);
    surahTextNavigationPainter.paint(canvas, surahOffset);

    double leftSurahNavRect = startSurahX;
    if (data.isSpread()) {
      leftSurahNavRect = (exactCanvasWidth / 2 - exactNavigationWidth) / 2;

      if (position == PagePosition.right) {
        leftSurahNavRect += data.getPageWidth() / pageData.navigationTextScale;
      }
    }

    pageData.surahNavigationRect = Rect.fromLTWH(
      leftSurahNavRect * pageData.navigationTextScale,
      0,
      surahTextNavigationPainter.width * pageData.navigationTextScale,
      data.navigationHeight,
    );

    // draw juz navigation
    final juzCenterVerticalToSurah = surahTextNavigationPainter.height / 2 -
        juzTextNavigationPainter.height / 2;
    final startJuzX =
        startSurahX + exactNavigationWidth - juzTextNavigationPainter.width;
    final juzOffset = Offset(
      startJuzX,
      juzCenterVerticalToSurah,
    );
    juzTextNavigationPainter.paint(canvas, juzOffset);
    pageData.juzNavigationRect = Rect.fromLTWH(
      (leftSurahNavRect +
              exactNavigationWidth -
              juzTextNavigationPainter.width) *
          pageData.navigationTextScale,
      0,
      juzTextNavigationPainter.width * pageData.navigationTextScale,
      data.navigationHeight,
    );

    canvas.restore();
  }

  void drawPageNumber(
    Canvas canvas,
    Size canvasSize,
    QuranCanvasPageData pageData,
    PagePosition position,
  ) {
    final pageNumber = pageData.quranPage?.pageNumber;
    if (pageNumber == null) {
      return;
    }

    canvas.save();

    final text = switch (data.juzNameScript) {
      JuzNameScript.latin => pageNumber.toString(),
      JuzNameScript.arabic => NumberFormat('#', 'ar_EG').format(pageNumber),
    };
    pageData.latinTextPainter.text = TextSpan(
      text: text,
      style: pageData.latinTextStyle.copyWith(
        decoration: TextDecoration.underline,
        decorationStyle: TextDecorationStyle.dashed,
      ),
    );
    pageData.latinTextPainter.layout();

    final additionalScale = switch (data.juzNameScript) {
      JuzNameScript.latin => .75,
      JuzNameScript.arabic => .5,
    };
    final scale = pageData.navigationTextScale * additionalScale;
    final centerHorizontal =
        canvasSize.width / 2 - pageData.latinTextPainter.width * scale / 2;
    final centerVertical = data.navigationHeight / 2 -
        pageData.latinTextPainter.height * scale / 2;

    canvas.translate(centerHorizontal, centerVertical);
    canvas.scale(scale, scale);

    double middlePagePosition = canvasSize.width / 2;
    if (data.isSpread()) {
      middlePagePosition = canvasSize.width / 4;

      if (position == PagePosition.right) {
        middlePagePosition += data.getPageWidth();
      }
    }

    pageData.pageNumberRect = Rect.fromLTWH(
      middlePagePosition - pageData.latinTextPainter.width / 2,
      0,
      pageData.latinTextPainter.width,
      data.navigationHeight,
    );

    pageData.latinTextPainter.paint(canvas, const Offset(0, 0));

    canvas.restore();
  }

  void drawFrame(Canvas canvas, Size canvasSize) {
    final frameFlutterImage = data.imageAsset.frameImage?.flutterImage;
    if (frameFlutterImage == null) {
      return;
    }

    canvas.save();

    final centerHorizontal =
        canvasSize.width / 2 - frameFlutterImage.width * data.frameScale / 2;
    canvas.translate(centerHorizontal, data.navigationHeight);
    canvas.scale(data.frameScale, data.frameScale);

    double opacity = data.rightFrameAlpha;
    if (data.isDarkMode) {
      opacity *= .5;
    }

    final framePaint = Paint()..color = Color.fromRGBO(0, 0, 0, opacity);
    canvas.drawImage(frameFlutterImage, const Offset(0, 0), framePaint);

    canvas.restore();
  }

  void cacheQuranPage(
      Size canvasSize, QuranCanvasPageData pageData, PagePosition position) {
    if (pageData.pagePicture != null) return;

    ui.PictureRecorder recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    drawQuranPage(canvas, canvasSize, pageData, position);

    pageData.pagePicture = recorder.endRecording();
  }

  void drawCachedQuranText(Canvas canvas, QuranCanvasPageData pageData) {
    canvas.save();
    // scale down the scaled image, so it will not get too big
    canvas.scale(1.0 / data.quranTextRecorderImageScale);

    final paint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true;
    canvas.drawImage(pageData.textImage!, const Offset(0, 0), paint);

    canvas.restore();
  }

  void drawCachedLineBreakQuranText(
      Canvas canvas, QuranCanvasPageData pageData) {
    canvas.save();
    // scale down the scaled image, so it will not get too big
    canvas.scale(1.0 / data.quranTextRecorderImageScale);

    final paint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true;
    canvas.drawImage(pageData.lineBreakTextImage!, const Offset(0, 0), paint);

    canvas.restore();
  }

  void drawQuranPage(
    Canvas canvas,
    Size canvasSize,
    QuranCanvasPageData pageData,
    PagePosition position,
  ) {
    canvas.save();
    if (data.isSpread()) {
      final pageWidth = data.getPageWidth();
      canvas.translate(-pageWidth / 2, 0);
    }

    drawOutsideBackground(canvas, canvasSize, pageData);
    drawBookHinge(canvas, canvasSize, pageData);
    drawBookEdge(canvas, canvasSize, pageData);
    drawSurahJuzNavigation(canvas, canvasSize, pageData, position);
    drawPageNumber(canvas, canvasSize, pageData, position);
    drawFrame(canvas, canvasSize);

    canvas.restore();
  }

  void foregroundCacheQuranText(
    Size canvasSize,
    QuranCanvasPageData pageData,
    PagePosition position,
  ) {
    if (data.zoomState != ZoomState.zoomedInLevel2) {
      if (pageData.textImage == null) {
        final cachedQuranText = QuranTextCacheUtils.cacheQuranText(
          canvasSize: canvasSize,
          data: data,
          pageData: pageData,
          quranMode: quranMode,
          recitationAlignmentHelper: recitationAlignmentHelper,
        );
        pageData.textImage = cachedQuranText.image;

        pageData.wordToHighlightRectMap = Map.fromIterable(
          cachedQuranText.wordHighlightRects,
          key: (item) => item.word,
          value: (item) {
            Rect rect = item.rect.toRect();
            // todo: should be handle in quran_text_cache_utils.dart
            if (data.isSpread()) {
              rect = rect.translate(-(data.getPageWidth() / 2), 0);
              if (position == PagePosition.right) {
                rect = rect.translate(data.getPageWidth(), 0);
              }
            }

            return rect;
          },
        );
      }
    } else {
      if (pageData.lineBreakTextImage == null) {
        final cachedQuranText = QuranTextCacheUtils.cacheLineBreakQuranText(
          canvasSize: canvasSize,
          data: data,
          pageData: pageData,
          quranMode: quranMode,
        );
        pageData.lineBreakTextImage = cachedQuranText.image;
        pageData.wordToHighlightLineBreakRectMap = {
          for (var data in cachedQuranText.wordHighlightRects)
            data.word: data.rect.toRect()
        };
      }
    }
  }

  void startDrawingQuranText(
      Canvas canvas, Size canvasSize, QuranCanvasPageData pageData) {
    canvas.save();

    if (data.scaleTextOnly()) {
      final scaleValue = (data.zoomState == ZoomState.zoomedInLevel1)
          ? pageData.zoomInLevel1Scale
          : data.rightZoomScale;
      if (data.scaleTextCenterPivot()) {
        final centerHorizontal = canvasSize.width / 2;
        final centerVertical = canvasSize.height / 2;

        // scaling with a center pivot
        canvas.translate(centerHorizontal, centerVertical);
        canvas.scale(scaleValue, scaleValue);
        canvas.translate(-centerHorizontal, -centerVertical);
      } else {
        canvas.scale(scaleValue, scaleValue);
      }
    }

    if (data.zoomState != ZoomState.zoomedInLevel2) {
      if (pageData.textImage != null) {
        drawCachedQuranText(canvas, pageData);
      }
    } else {
      if (pageData.lineBreakTextImage != null) {
        drawCachedLineBreakQuranText(canvas, pageData);
      }
    }

    canvas.restore();
  }

  void drawOrCacheQuranPage(
    Canvas canvas,
    Size canvasSize,
    QuranCanvasPageData pageData,
    PagePosition position,
  ) {
    if (pageData.pagePicture == null) {
      cacheQuranPage(canvasSize, pageData, position);
    }

    if (data.isForegroundCacheQuranText(quranMode, pageData)) {
      foregroundCacheQuranText(canvasSize, pageData, position);
    }

    drawHighlight(canvas, canvasSize, pageData, position);

    if (data.isSpread() && position == PagePosition.right) {
      canvas.translate(data.getPageWidth(), 0);
    }

    startDrawingQuranText(canvas, canvasSize, pageData);

    // todo if cache not available yet then show the loading
    if (pageData.useCachedPage && pageData.pagePicture != null) {
      canvas.drawPicture(pageData.pagePicture!);
    } else {
      drawQuranPage(canvas, canvasSize, pageData, position);
    }
  }

  void preparePageDrawing(
      Canvas canvas, Size canvasSize, QuranCanvasPageData pageData) {
    final pageNumber = pageData.quranPage?.pageNumber;
    if (pageNumber == null) {
      return;
    }

    canvas.save();

    final scrollX =
        data.getPageWidth() * (data.currentPositionOffset - pageNumber);
    canvas.translate(scrollX, 0);

    drawOrCacheQuranPage(canvas, canvasSize, pageData, PagePosition.right);

    canvas.restore();
  }

  void drawHighlight(
    Canvas canvas,
    Size canvasSize,
    QuranCanvasPageData pageData,
    PagePosition position,
  ) {
    canvas.save();

    if (data.scaleTextOnly()) {
      final scaleValue = (data.zoomState == ZoomState.zoomedInLevel1)
          ? pageData.zoomInLevel1Scale
          : data.rightZoomScale;
      if (data.scaleTextCenterPivot()) {
        final centerHorizontal = canvasSize.width / 2;
        final centerVertical = canvasSize.height / 2;

        // scaling with a center pivot
        canvas.translate(centerHorizontal, centerVertical);
        canvas.scale(scaleValue, scaleValue);
        canvas.translate(-centerHorizontal, -centerVertical);
      } else {
        canvas.scale(scaleValue, scaleValue);
      }
    }

    Map<QuranWord, Rect> highlightRectMap = {};
    if (data.zoomState == ZoomState.zoomedInLevel2) {
      highlightRectMap = pageData.wordToHighlightLineBreakRectMap;
    } else {
      highlightRectMap = pageData.wordToHighlightRectMap;
    }

    for (var entry in highlightRectMap.entries.toList()) {
      if (entry.key.verseKey == data.highlightedVerseKey) {
        canvas.drawRect(entry.value, data.verseHighlightPaint);
      }

      if (entry.key == data.highlightedWord) {
        canvas.drawRect(entry.value, data.wordHighlightPaint);
      }
    }

    canvas.restore();
  }

  void drawSinglePage(Canvas canvas, Size canvasSize) {
    // previous page
    preparePageDrawing(
        canvas, canvasSize, data.previousSpreadData.currentSinglePageData);

    // current page
    preparePageDrawing(
        canvas, canvasSize, data.currentSpreadData.currentSinglePageData);

    // next page
    preparePageDrawing(
        canvas, canvasSize, data.nextSpreadData.currentSinglePageData);
  }

  void prepareSpreadDrawing(
      Canvas canvas, Size canvasSize, QuranCanvasSpreadData spreadData) {
    final spreadNumber = spreadData.spreadNumber;
    if (spreadNumber == null) {
      return;
    }

    canvas.save();

    final scrollX =
        canvasSize.width * (data.currentPositionOffset - spreadNumber);
    canvas.translate(scrollX, 0);

    drawOrCacheQuranPage(
        canvas, canvasSize, spreadData.leftPageData, PagePosition.left);
    drawOrCacheQuranPage(
        canvas, canvasSize, spreadData.rightPageData, PagePosition.right);

    canvas.restore();
  }

  void drawSpreadPages(Canvas canvas, Size canvasSize) {
    // previous spread
    prepareSpreadDrawing(canvas, canvasSize, data.previousSpreadData);

    // current spread
    prepareSpreadDrawing(canvas, canvasSize, data.currentSpreadData);

    // next spread
    prepareSpreadDrawing(canvas, canvasSize, data.nextSpreadData);
  }

  @override
  void paint(Canvas canvas, Size size) {
    if (!data.renderMushaf) return;

    // for scrolling
    canvas.translate(0, -data.scrollY);

    final centerHorizontal = size.width / 2;
    final centerVertical = size.height / 2;

    if (data.scaleWholePage()) {
      // scaling with a center pivot
      canvas.translate(centerHorizontal, centerVertical);
      canvas.scale(data.rightZoomScale, data.rightZoomScale);
      canvas.translate(-centerHorizontal, -centerVertical);
    }

    if (data.isSpread()) {
      drawSpreadPages(canvas, size);
    } else {
      drawSinglePage(canvas, size);
    }

    data.cacheQuranTextDirectly = false;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
