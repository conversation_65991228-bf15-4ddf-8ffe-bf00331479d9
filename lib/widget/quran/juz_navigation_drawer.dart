import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/navigation/juz_navigation_list.dart';

class JuzNavigationDrawer extends ConsumerWidget {
  const JuzNavigationDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final quranPageData = ref.watch(quranPageDataProvider);
    final pageData = quranPageData.data;
    final juzNumber = pageData.currentRightPage?.juzNumber ?? 1;

    return SafeArea(
      child: Row(
        children: [
          if (!context.isArabic) ...[
            const SizedBox(width: 6),
            Container(
              width: 4,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.0),
                color: ColorResource.backgroundGreyDark(context.isDarkMode),
              ),
            ),
            const SizedBox(width: 6),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.only(left: 16.0, right: 16, top: 16),
                  child: Text(
                    context.tr('quran.juz'),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 20,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16),
                    child: JuzNavigationList(
                      scrollable: true,
                      initialPosition: juzNumber - 1,
                      onNavigationItemSelected: (pageNumber) {
                        if (context.isArabic) {
                          Scaffold.of(context).closeDrawer();
                        } else {
                          Scaffold.of(context).closeEndDrawer();
                        }

                        ref
                            .read(pageNavigationProvider.notifier)
                            .navigateToPage(pageNumber);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (context.isArabic) ...[
            const SizedBox(width: 6),
            Container(
              width: 4,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.0),
                color: ColorResource.backgroundGreyDark(context.isDarkMode),
              ),
            ),
            const SizedBox(width: 6),
          ],
        ],
      ),
    );
  }
}
