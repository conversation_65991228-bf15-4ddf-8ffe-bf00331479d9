import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/int_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/activity/activity_input_screen.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/model/track/track_range_portion.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';

class QuranTopBar extends ConsumerStatefulWidget {
  const QuranTopBar({super.key});

  @override
  ConsumerState<QuranTopBar> createState() => _QuranTopBarState();
}

class _QuranTopBarState extends ConsumerState<QuranTopBar> {
  List<Surah> surahList = [];

  String? getSurahName(int? surahNumber) {
    if (surahNumber == null) return null;

    final surah = surahList.elementAtOrNull(surahNumber - 1);
    return (context.isArabic) ? surah?.nameArabic : surah?.nameComplex;
  }

  String? getTrackDetail(Track? track) {
    if (track == null) return null;

    final range = track.range;
    final startSurahName = getSurahName(track.range.startSurahNumber);
    final endSurahName = getSurahName(track.range.endSurahNumber);

    String detail = '';
    if (track.name != null && track.name!.isNotEmpty) {
      detail = switch (track.type) {
        null => '',
        TrackType.listening => switch (track.rangePortion) {
            TrackRangePortion.surah =>
              context.tr('activity.detail.surah', args: [startSurahName ?? '']),
            TrackRangePortion.juz => context.tr('activity.detail.juz',
                args: [range.startJuzNumber.toLocaleString(context.locale)]),
            TrackRangePortion.range =>
              (range.endSurahNumber > range.startSurahNumber)
                  ? context.tr('activity.detail.range_different_surah', args: [
                      startSurahName ?? '',
                      range.startVerseNumber.toLocaleString(context.locale),
                      endSurahName ?? '',
                      range.endVerseNumber.toLocaleString(context.locale),
                    ])
                  : context.tr('activity.detail.range_same_surah', args: [
                      startSurahName ?? '',
                      range.startVerseNumber.toLocaleString(context.locale),
                      range.endVerseNumber.toLocaleString(context.locale),
                    ]),
            TrackRangePortion.page =>
              (range.endPageNumber > range.startPageNumber)
                  ? context.tr('activity.detail.page_range', args: [
                      range.startPageNumber.toLocaleString(context.locale),
                      range.endPageNumber.toLocaleString(context.locale),
                    ])
                  : context.tr('activity.detail.page_single', args: [
                      range.startPageNumber.toLocaleString(context.locale)
                    ]),
          },
        TrackType.readingHabitual =>
          context.tr('activity.detail.reading_habitual', args: [
            startSurahName ?? '',
            range.startVerseNumber.toLocaleString(context.locale),
          ]),
        TrackType.readingCoverToCover =>
          context.tr('activity.detail.reading_cover_to_cover', args: [
            range.startPageNumber.toLocaleString(context.locale),
          ]),
        TrackType.readingWithAi || TrackType.memorizing => switch (
              track.rangePortion) {
            TrackRangePortion.surah =>
              context.tr('activity.detail.surah', args: [startSurahName ?? '']),
            TrackRangePortion.juz => context.tr('activity.detail.juz',
                args: [range.startJuzNumber.toLocaleString(context.locale)]),
            TrackRangePortion.range =>
              (range.endSurahNumber > range.startSurahNumber)
                  ? context.tr('activity.detail.range_different_surah', args: [
                      startSurahName ?? '',
                      range.startVerseNumber.toLocaleString(context.locale),
                      endSurahName ?? '',
                      range.endVerseNumber.toLocaleString(context.locale),
                    ])
                  : context.tr('activity.detail.range_same_surah', args: [
                      startSurahName ?? '',
                      range.startVerseNumber.toLocaleString(context.locale),
                      range.endVerseNumber.toLocaleString(context.locale),
                    ]),
            TrackRangePortion.page =>
              (range.endPageNumber > range.startPageNumber)
                  ? context.tr('activity.detail.page_range', args: [
                      range.startPageNumber.toLocaleString(context.locale),
                      range.endPageNumber.toLocaleString(context.locale),
                    ])
                  : context.tr('activity.detail.page_single', args: [
                      range.startPageNumber.toLocaleString(context.locale)
                    ]),
          },
      };
    } else {
      detail = switch (track.type) {
        null => '',
        TrackType.listening => switch (track.rangePortion) {
            TrackRangePortion.surah => startSurahName ?? '',
            TrackRangePortion.juz =>
              context.tr('activity_input.juz_number', args: [
                range.startJuzNumber.toLocaleString(context.locale),
              ]),
            TrackRangePortion.range =>
              (range.endSurahNumber > range.startSurahNumber)
                  ? context.tr('activity_range.verse_range_with_surah', args: [
                      startSurahName ?? '',
                      range.startVerseNumber.toLocaleString(context.locale),
                      endSurahName ?? '',
                      range.endVerseNumber.toLocaleString(context.locale),
                    ])
                  : context.tr('activity.range.ayah', args: [
                      range.startVerseNumber.toLocaleString(context.locale),
                      range.endVerseNumber.toLocaleString(context.locale),
                    ]),
            TrackRangePortion.page =>
              (range.endPageNumber > range.startPageNumber)
                  ? context.tr('activity_range.page_range', args: [
                      range.startPageNumber.toLocaleString(context.locale),
                      range.endPageNumber.toLocaleString(context.locale),
                    ])
                  : context.tr('activity_range.page', args: [
                      range.startPageNumber.toLocaleString(context.locale),
                    ]),
          },
        TrackType.readingHabitual => context.tr('activity_input.ayah', args: [
            range.startVerseNumber.toLocaleString(context.locale),
          ]),
        TrackType.readingCoverToCover =>
          context.tr('activity.detail.reading_cover_to_cover', args: [
            range.startPageNumber.toLocaleString(context.locale),
          ]),
        TrackType.readingWithAi || TrackType.memorizing => switch (
              track.rangePortion) {
            TrackRangePortion.surah => startSurahName ?? '',
            TrackRangePortion.juz =>
              context.tr('activity_input.juz_number', args: [
                range.startJuzNumber.toLocaleString(context.locale),
              ]),
            TrackRangePortion.range =>
              (range.endSurahNumber > range.startSurahNumber)
                  ? context.tr('activity_range.verse_range_with_surah', args: [
                      startSurahName ?? '',
                      range.startVerseNumber.toLocaleString(context.locale),
                      endSurahName ?? '',
                      range.endVerseNumber.toLocaleString(context.locale),
                    ])
                  : context.tr('activity.range.ayah', args: [
                      range.startVerseNumber.toLocaleString(context.locale),
                      range.endVerseNumber.toLocaleString(context.locale),
                    ]),
            TrackRangePortion.page =>
              (range.endPageNumber > range.startPageNumber)
                  ? context.tr('activity_range.page_range', args: [
                      range.startPageNumber.toLocaleString(context.locale),
                      range.endPageNumber.toLocaleString(context.locale),
                    ])
                  : context.tr('activity_range.page', args: [
                      range.startPageNumber.toLocaleString(context.locale),
                    ]),
          },
      };
    }

    return detail;
  }

  String? getTrackIconAsset(Track? track) {
    final icon = switch (track?.type) {
      null => null,
      TrackType.listening => Assets.svgsIcHeadphone,
      TrackType.readingHabitual => Assets.svgsIcHabitual,
      TrackType.readingCoverToCover => Assets.svgsIcReading,
      TrackType.readingWithAi => Assets.svgsIcAi,
      TrackType.memorizing => Assets.svgsIcColumn,
    };

    return icon;
  }

  @override
  void initState() {
    ref.read(jsonDataRepositoryProvider).getSurahList().then((surahList) {
      setState(() {
        this.surahList = surahList;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final quranPageDataState = ref.watch(quranPageDataProvider);
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final design = mushafDesignAsync.value?.design;
    final drawerColor = design?.drawer1Color ??
        MushafDesign.getDefault(context.isDarkMode).drawer1Color;
    final currentTrack = ref.watch(selectedTrackProvider);

    String? trackName =
        (currentTrack?.name != null && currentTrack!.name!.isNotEmpty)
            ? currentTrack.name
            : null;
    String? trackStartSurahName =
        getSurahName(currentTrack?.range.startSurahNumber);
    final trackDetail = getTrackDetail(currentTrack);
    final trackIconAsset = getTrackIconAsset(currentTrack);

    final pageData = quranPageDataState.data.currentRightPage;
    final surahId = pageData?.surahNumber ?? 1;
    final surahName = getSurahName(surahId) ?? '';

    return Container(
      color: drawerColor,
      child: (surahList.isEmpty || pageData == null)
          ? const SizedBox()
          : Stack(
              alignment: Alignment.center,
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: SvgPicture.asset(
                      Assets.svgsIcChevronLeft24px,
                      colorFilter: ColorFilter.mode(
                        ColorResource.textDefault(context.isDarkMode),
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      trackName ?? trackStartSurahName ?? surahName,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: ColorResource.textDefault(context.isDarkMode),
                      ),
                    ),
                    Text(
                      trackDetail ??
                          context.tr('quran.page_and_juz', args: [
                            pageData.pageNumber.toLocaleString(context.locale),
                            pageData.juzNumber.toLocaleString(context.locale),
                          ]),
                      style: TextStyle(
                        fontSize: 10,
                        color: ColorResource.textDefault(context.isDarkMode),
                      ),
                    ),
                  ],
                ),
                if (currentTrack == null)
                  Align(
                    alignment: Alignment.centerRight,
                    child: IconButton(
                      onPressed: () async {
                        ref.read(stopRecitationProvider).stop();

                        final track = await Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const ActivityInputScreen(),
                          ),
                        ) as Track?;

                        if (context.mounted && track != null) {
                          Navigator.pop(context, track);
                        }
                      },
                      icon: SvgPicture.asset(
                        Assets.svgsIcPlusCircle,
                        colorFilter: ColorFilter.mode(
                            ColorResource.textDefault(context.isDarkMode),
                            BlendMode.srcIn),
                      ),
                    ),
                  )
                else if (trackIconAsset != null)
                  Align(
                    alignment: Alignment.centerRight,
                    child: IconButton(
                      onPressed: () async {
                        ref.read(stopRecitationProvider).stop();

                        final track = await Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) =>
                                ActivityInputScreen(track: currentTrack),
                          ),
                        ) as Track?;

                        if (context.mounted && track != null) {
                          Navigator.pop(context, track);
                        }
                      },
                      icon: SvgPicture.asset(
                        trackIconAsset,
                        width: 24,
                        height: 24,
                        colorFilter: ColorFilter.mode(
                          ColorResource.textDefault(context.isDarkMode),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  )
              ],
            ),
    );
  }
}
