import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/resource/color_resource.dart';

class VerseTooltip extends StatelessWidget {
  final VoidCallback onTap;

  const VerseTooltip({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final Color bgColor =
        ColorResource.backgroundBlackAlpha90(context.isDarkMode);
    return GestureDetector(
      onTap: onTap,
      child: Material(
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Arrow at the top center
            CustomPaint(
              size: const Size(20, 10),
              painter: _TooltipArrowPainter(bgColor),
            ),
            Container(
              width: 120,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: bgColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                context.tr('add_bookmark'),
                style: TextStyle(
                  color: (context.isDarkMode) ? Colors.black : Colors.white,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Custom painter for the tooltip arrow
class _TooltipArrowPainter extends CustomPainter {
  final Color color;

  _TooltipArrowPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()..color = color;
    final Path path = Path()
      ..moveTo(0, size.height)
      ..lineTo(size.width / 2, 0)
      ..lineTo(size.width, size.height)
      ..close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
