import 'dart:io';

import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/int_extension.dart';
import 'package:mushafi/extension/verse_key_parser.dart';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/home/<USER>';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/common_utils.dart';

class BookmarkSection extends ConsumerWidget {
  final Function(int pageNumber, String verseKey) onBookmarkTap;

  const BookmarkSection({super.key, required this.onBookmarkTap});

  Future<void> showDeleteConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    String verseKey,
    String surahName,
    String verseNumber,
  ) async {
    if (Platform.isIOS || Platform.isMacOS) {
      return showCupertinoDialog(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(
              context.tr('delete_bookmark'),
              style: TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
            ),
            content: Text(
              context.tr('delete_bookmark_confirmation', args: [surahName, verseNumber]),
              style: TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  context.tr('common.cancel'),
                  style: TextStyle(color: ColorResource.textBlue(context.isDarkMode)),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  final bookmarkRepository = ref.read(bookmarkRepositoryProvider);
                  bookmarkRepository.deleteBookmark(verseKey);
                  CommonUtils.showToast(context.tr('bookmark_deleted'));
                },
                child: Text(
                  context.tr('common.delete'),
                  style: TextStyle(color: ColorResource.danger(context.isDarkMode)),
                ),
              ),
            ],
          );
        },
      );
    }

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(
          context.tr('delete_bookmark'),
          style: TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
        content: Text(
          context.tr('delete_bookmark_confirmation', args: [surahName, verseNumber]),
          style: TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              context.tr('common.cancel'),
              style: TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final bookmarkRepository = ref.read(bookmarkRepositoryProvider);
              bookmarkRepository.deleteBookmark(verseKey);
              CommonUtils.showToast(context.tr('bookmark_deleted'));
            },
            child: Text(
              context.tr('common.delete'),
              style: TextStyle(color: ColorResource.textBlue(context.isDarkMode)),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bookmarkListAsync = ref.watch(bookmarkListProvider);

    return bookmarkListAsync.when(
      data: (bookmarkList) {
        if (bookmarkList.isEmpty) {
          return buildEmptyRecentPageState(context);
        }

        return buildBookmarkList(context, ref, bookmarkList);
      },
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
          style: TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget buildEmptyRecentPageState(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            context.tr('empty_bookmarks_title'),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          Text(
            context.tr('empty_bookmarks_description'),
            textAlign: TextAlign.center,
            style: TextStyle(color: ColorResource.textGrey(context.isDarkMode), fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget buildBookmarkList(BuildContext context, WidgetRef ref, List<Bookmark> bookmarkList) {
    final surahListAsync = ref.watch(surahListProvider);

    return surahListAsync.when(
      data: (surahList) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: bookmarkList.length,
          itemBuilder: (context, index) {
            final bookmark = bookmarkList[index];
            final surah = surahList.firstWhereOrNull((surah) {
              final maxPage = surah.pages.max;
              final minPage = surah.pages.min;
              return bookmark.pageNumber >= minPage && bookmark.pageNumber <= maxPage;
            });
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: buildBookmarkItem(context, ref, bookmark, surah),
            );
          },
        );
      },
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
          style: TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget buildBookmarkItem(BuildContext context, WidgetRef ref, Bookmark bookmark, Surah? surah) {
    final surahName = ((context.isArabic) ? surah?.nameArabic : surah?.nameComplex);
    if (surahName == null) return const SizedBox.shrink();

    final verseNumber = bookmark.verseKey.parseVerseNumber().toLocaleString(context.locale);

    return InkWell(
      onTap: () {
        onBookmarkTap(bookmark.pageNumber, bookmark.verseKey);
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: ColorResource.border(context.isDarkMode), width: 1.0),
          borderRadius: BorderRadius.circular(12.0),
          color: ColorResource.backgroundWhite(context.isDarkMode),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: context.isArabic ? 12 : 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  "$surahName: $verseNumber",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontFamily: 'ProductSans',
                    fontSize: 14,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  showDeleteConfirmationDialog(
                    context,
                    ref,
                    bookmark.verseKey,
                    surahName,
                    verseNumber,
                  );
                },
                child: Icon(
                  Icons.delete_outline,
                  size: 20,
                  color: ColorResource.textDefault(context.isDarkMode),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
