import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/int_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/home/<USER>';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/home/<USER>';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/resource/color_resource.dart';

class RecentPageSection extends ConsumerWidget {
  final Function(int pageNumber) onNavigationItemSelected;

  const RecentPageSection({super.key, required this.onNavigationItemSelected});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final recentPageListAsync = ref.watch(recentPageListProvider);

    return recentPageListAsync.when(
      data: (recentPageList) {
        if (recentPageList.isEmpty) {
          return buildEmptyRecentPageState(context);
        }

        return buildRecentPageList(context, ref, recentPageList);
      },
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
          style:
              TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget buildEmptyRecentPageState(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            context.tr('home.empty_recent_pages_title'),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          Text(
            context.tr('home.empty_recent_pages_description'),
            textAlign: TextAlign.center,
            style: TextStyle(
                color: ColorResource.textGrey(context.isDarkMode),
                fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget buildRecentPageList(
      BuildContext context, WidgetRef ref, List<RecentPage> recentPageList) {
    final surahListAsync = ref.watch(surahListProvider);

    return surahListAsync.when(
      data: (surahList) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: recentPageList.length,
          itemBuilder: (context, index) {
            final recentPage = recentPageList[index];
            final surah = surahList.firstWhereOrNull((surah) {
              final maxPage = surah.pages.max;
              final minPage = surah.pages.min;
              return recentPage.pageNumber >= minPage &&
                  recentPage.pageNumber <= maxPage;
            });
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: buildRecentPageItem(context, recentPage, surah),
            );
          },
        );
      },
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
          style:
              TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget buildRecentPageItem(
      BuildContext context, RecentPage recentPage, Surah? surah) {
    final nowTime = DateTime.now().millisecondsSinceEpoch;
    final diff = nowTime - recentPage.time;

    final minutes = diff ~/ (1000 * 60) % 60;
    final hours = diff ~/ (1000 * 60 * 60) % 24;
    final days = diff ~/ (1000 * 60 * 60 * 24);

    String when = '';
    if (days > 0) {
      when = context
          .tr('time.days_ago', args: [days.toLocaleString(context.locale)]);
    } else if (hours > 0) {
      when = context
          .tr('time.hours_ago', args: [hours.toLocaleString(context.locale)]);
    } else if (minutes > 0) {
      when = context.tr('time.minutes_ago',
          args: [minutes.toLocaleString(context.locale)]);
    } else {
      when = context.tr('time.few_seconds_ago');
    }

    final formattedSurahNumber = surah?.id.toString().padLeft(3, '0') ?? '';
    final surahName =
        ((context.isArabic) ? surah?.nameArabic : surah?.nameComplex) ?? '';

    return InkWell(
      onTap: () {
        onNavigationItemSelected(recentPage.pageNumber);
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
              color: ColorResource.border(context.isDarkMode), width: 1.0),
          borderRadius: BorderRadius.circular(12.0),
          color: ColorResource.backgroundWhite(context.isDarkMode),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: 16, vertical: context.isArabic ? 12 : 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: RichText(
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  text: TextSpan(
                    children: [
                      if (context.isArabic)
                        TextSpan(
                          text: formattedSurahNumber,
                          style: TextStyle(
                            fontFamily: 'SurahNames',
                            fontSize: 24,
                            color:
                                ColorResource.textDefault(context.isDarkMode),
                          ),
                        )
                      else ...[
                        TextSpan(
                          text: surahName,
                          style: TextStyle(
                            fontFamily: 'ProductSans',
                            fontSize: 14,
                            color:
                                ColorResource.textDefault(context.isDarkMode),
                          ),
                        ),
                        const WidgetSpan(
                          child: SizedBox(width: 4),
                        ),
                        TextSpan(
                          text: formattedSurahNumber,
                          style: TextStyle(
                            fontFamily: 'SurahNames',
                            fontSize: 14,
                            color:
                                ColorResource.textDefault(context.isDarkMode),
                          ),
                        ),
                      ],
                      const WidgetSpan(
                        child: SizedBox(width: 4),
                      ),
                      TextSpan(
                        text: ' ${context.tr('activity.bullet')} ',
                        style: TextStyle(
                          fontFamily: 'ProductSans',
                          color: ColorResource.textDefault(context.isDarkMode),
                        ),
                      ),
                      const WidgetSpan(
                        child: SizedBox(width: 4),
                      ),
                      TextSpan(
                        text: context.tr(
                          'home.page_short',
                          args: [
                            recentPage.pageNumber.toLocaleString(context.locale)
                          ],
                        ),
                        style: TextStyle(
                          fontFamily: 'ProductSans',
                          fontSize: 12,
                          color: ColorResource.textGrey(context.isDarkMode),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              SvgPicture.asset(Assets.svgsIcClock),
              const SizedBox(width: 8),
              Text(
                when,
                style: TextStyle(
                  fontSize: 12,
                  color: ColorResource.textGrey(context.isDarkMode),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
