import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/activity/activity_input_screen.dart';
import 'package:mushafi/presentation/main/main_screen.dart';
import 'package:mushafi/presentation/model/nullable.dart';
import 'package:mushafi/presentation/model/quran/surah.dart';
import 'package:mushafi/presentation/model/track/track.dart';
import 'package:mushafi/presentation/model/track/track_type.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/activity/activity_item.dart';
import 'package:mushafi/widget/activity/nameless_activity_item.dart';

final trackListProvider = StreamProvider<List<Track>>((ref) {
  final trackRepository = ref.watch(trackRepositoryProvider);
  final minioDataRepository = ref.watch(minioDataRepositoryProvider);
  return trackRepository.watchTrackList().asyncMap((trackList) async {
    final List<Track> newList = [];

    final modelCompatible = PreferenceStorage.getModelCompatible();
    for (final track in trackList) {
      if (!modelCompatible) {
        if (track.type == TrackType.memorizing ||
            track.type == TrackType.readingWithAi) {
          continue;
        }
      }

      if (track.reciterKey != null) {
        final reciter = await minioDataRepository.getReciter(track.reciterKey!);
        newList.add(track.copyWith(reciter: Nullable(reciter)));
      } else {
        newList.add(track);
      }
    }

    newList.sort((a, b) {
      if (b.openingTimestamp == null) return 0;
      if (b.openingTimestamp != null && a.openingTimestamp == null) return 1;

      return b.openingTimestamp!.compareTo(a.openingTimestamp!);
    });

    return newList;
  });
});

class Activities extends ConsumerStatefulWidget {
  final Function(Track track) onActivitySelected;

  const Activities(this.onActivitySelected, {super.key});

  @override
  ConsumerState<Activities> createState() => _ActivitiesState();
}

class _ActivitiesState extends ConsumerState<Activities> {
  List<String> activityTabLabelList = [];

  List<_ActivityTemplate> templateList = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    activityTabLabelList = [
      context.tr('activity.all'),
      context.tr('activity.reading'),
      context.tr('activity.listening'),
      context.tr('activity.memorizing'),
    ];

    templateList = [
      _ActivityTemplate(
        title: context.tr('activity.memorization_template_title'),
        subtitle: context.tr('activity.memorization_template_subtitle'),
        iconAssetName: Assets.svgsIcColumn,
        imageAssetName: Assets.svgsImgReadingQuran,
        buttonText: context.tr('activity.try_now'),
      ),
      _ActivityTemplate(
        title: context.tr('activity.ai_recitation_template_title'),
        subtitle: context.tr('activity.ai_recitation_template_subtitle'),
        iconAssetName: Assets.svgsIcStars,
        imageAssetName: Assets.svgsImgHoldTheQuran,
        buttonText: context.tr('activity.try_now'),
      ),
      _ActivityTemplate(
        title: context.tr('activity.al_kahf_reading_template_title'),
        subtitle: context.tr('activity.al_kahf_reading_template_subtitle'),
        iconAssetName: Assets.svgsIcCalendar,
        imageAssetName: Assets.svgsImgQuranLantern,
        buttonText: context.tr('activity.read_now'),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(builder: (context, orientation) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (orientation == Orientation.portrait) ...[
            _buildTemplateSection(),
            const SizedBox(height: 8),
          ],
          _buildActivitiesSection(),
        ],
      );
    });
  }

  Widget _buildActivitiesSection() {
    final surahListAsync = ref.watch(surahListProvider);

    return Expanded(
      child: DefaultTabController(
        length: 4,
        child: Scaffold(
          backgroundColor: ColorResource.backgroundDefault(context.isDarkMode),
          appBar: TabBar(
            indicatorColor: ColorResource.textDefault(context.isDarkMode),
            labelColor: ColorResource.textDefault(context.isDarkMode),
            unselectedLabelColor:
                ColorResource.textDefault(context.isDarkMode).withOpacity(.5),
            tabAlignment: TabAlignment.start,
            isScrollable: true,
            tabs: activityTabLabelList
                .mapIndexed((index, label) => Tab(text: label))
                .toList(),
          ),
          body: surahListAsync.when(
            data: (surahList) {
              return TabBarView(
                children: [
                  buildAllActivityList(surahList),
                  buildReadingActivityList(surahList),
                  buildListeningActivityList(surahList),
                  buildMemorizingActivityList(surahList),
                ],
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stack) => Center(
              child: Text(
                error.toString(),
                style: TextStyle(
                    color: ColorResource.textDefault(context.isDarkMode)),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildAllActivityList(List<Surah> surahList) {
    final trackListAsync = ref.watch(trackListProvider);

    return trackListAsync.when(
      data: (trackList) {
        return buildActivityList(surahList, trackList);
      },
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
          style:
              TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget buildReadingActivityList(List<Surah> surahList) {
    final trackListAsync = ref.watch(trackListProvider);

    return trackListAsync.when(
      data: (trackList) {
        final filteredTrackList = trackList
            .where((track) => track.type?.activityType == 'reading')
            .toList();
        return buildActivityList(surahList, filteredTrackList);
      },
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
          style:
              TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget buildListeningActivityList(List<Surah> surahList) {
    final trackListAsync = ref.watch(trackListProvider);

    return trackListAsync.when(
      data: (trackList) {
        final filteredTrackList = trackList
            .where((track) => track.type == TrackType.listening)
            .toList();
        return buildActivityList(surahList, filteredTrackList);
      },
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
          style:
              TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget buildMemorizingActivityList(List<Surah> surahList) {
    final trackListAsync = ref.watch(trackListProvider);

    return trackListAsync.when(
      data: (trackList) {
        final filteredTrackList = trackList
            .where((track) => track.type == TrackType.memorizing)
            .toList();
        return buildActivityList(surahList, filteredTrackList);
      },
      error: (error, stack) => Center(
        child: Text(
          error.toString(),
          style:
              TextStyle(color: ColorResource.textDefault(context.isDarkMode)),
        ),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget buildActivityList(List<Surah> surahList, List<Track> trackList) {
    return (trackList.isNotEmpty)
        ? ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: trackList.length,
            itemBuilder: (context, index) {
              final track = trackList[index];
              final name = track.name;
              return (name == null || name.isEmpty)
                  ? _buildNamelessActivity(track, surahList)
                  : _buildActivity(track, surahList);
            },
          )
        : buildEmptyActivityState();
  }

  Future<void> showMoreOptionsBottomSheet(Track track) {
    return showModalBottomSheet<void>(
      context: context,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.center,
                child: Text(
                  context.tr('activity.more_actions'),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: ColorResource.textDefault(context.isDarkMode),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              const Divider(thickness: .2),
              InkWell(
                onTap: () async {
                  Navigator.pop(context);

                  final newTrack = await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => ActivityInputScreen(track: track),
                    ),
                  ) as Track?;

                  if (mounted && newTrack != null) {
                    widget.onActivitySelected(newTrack);
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        Assets.svgsIcEdit,
                        colorFilter: ColorFilter.mode(
                          ColorResource.textDefault(context.isDarkMode),
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('activity.edit_activity'),
                        style: TextStyle(
                          fontSize: 16,
                          color: ColorResource.textDefault(context.isDarkMode),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const Divider(thickness: .2),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNamelessActivity(Track track, List<Surah> surahList) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: NamelessActivityItem(
        track: track,
        surahList: surahList,
        onTap: widget.onActivitySelected,
        onMorePressed: () {
          showMoreOptionsBottomSheet(track);
        },
        onEditPressed: () async {
          final newTrack = await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ActivityInputScreen(track: track),
            ),
          ) as Track?;

          if (mounted && newTrack != null) {
            widget.onActivitySelected(newTrack);
          }
        },
      ),
    );
  }

  Widget _buildActivity(Track track, List<Surah> surahList) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: ActivityItem(
        track: track,
        surahList: surahList,
        onTap: widget.onActivitySelected,
        onMorePressed: () {
          showMoreOptionsBottomSheet(track);
        },
        onEditPressed: () async {
          final newTrack = await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ActivityInputScreen(track: track),
            ),
          ) as Track?;

          if (mounted && newTrack != null) {
            widget.onActivitySelected(newTrack);
          }
        },
      ),
    );
  }

  Widget buildEmptyActivityState() {
    return Builder(builder: (context) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              context.tr('activity.empty_activity_title'),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 20,
                color: ColorResource.textDefault(context.isDarkMode),
              ),
            ),
            Text(
              context.tr('activity.empty_activity_description'),
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: ColorResource.textGrey(context.isDarkMode),
                  fontSize: 14),
            ),
            const SizedBox(height: 32),
            FilledButton(
              style: FilledButton.styleFrom(
                backgroundColor: ColorResource.primary(context.isDarkMode),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () async {
                final trackType =
                    switch (DefaultTabController.of(context).index) {
                  1 => TrackType.readingCoverToCover,
                  2 => TrackType.listening,
                  3 => TrackType.memorizing,
                  _ => null,
                };

                final newTrack = await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) =>
                        ActivityInputScreen(trackType: trackType),
                  ),
                ) as Track?;

                if (mounted && newTrack != null) {
                  widget.onActivitySelected(newTrack);
                }
              },
              child: Text(
                context.tr('activity.create_activity_button'),
                style: TextStyle(
                  fontSize: 16,
                  color: ColorResource.onPrimary(context.isDarkMode),
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildTemplateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            context.tr('activity.suggested_for_you'),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: templateList.mapIndexed((index, template) {
                final rightPadding = (context.isArabic)
                    ? 0.0
                    : index != templateList.length - 1
                        ? 8.0
                        : 0.0;
                final leftPadding = (context.isArabic)
                    ? index != templateList.length - 1
                        ? 8.0
                        : 0.0
                    : 0.0;
                return Padding(
                  padding:
                      EdgeInsets.only(right: rightPadding, left: leftPadding),
                  child: _buildTemplateCard(index, template),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTemplateCard(int index, _ActivityTemplate data) {
    return InkWell(
      onTap: () async {
        final track = switch (index) {
          0 => Track.createMemorizationTemplate(
              context.tr('activity.my_first_memorization'), context.isDarkMode),
          1 => Track.createReciteWithAITemplate(
              context.tr('activity.my_first_recite'), context.isDarkMode),
          2 => Track.createAlKahfHabitualTemplate(
              context.tr('activity.al_kahf_friday'), context.isDarkMode),
          _ => null,
        };

        final newTrack = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ActivityInputScreen(track: track),
          ),
        );

        if (mounted && newTrack != null) {
          widget.onActivitySelected(newTrack);
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          color: ColorResource.backgroundWhite(context.isDarkMode),
          width: 250,
          child: AspectRatio(
            aspectRatio: 5 / 3,
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(data.iconAssetName,
                              width: 16, height: 16),
                          const SizedBox(width: 4),
                          Text(
                            data.subtitle,
                            style: TextStyle(
                                fontSize: 12, color: Colors.grey[600]),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        data.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: ColorResource.textDefault(context.isDarkMode),
                        ),
                      ),
                      // const SizedBox(height: 16),
                    ],
                  ),
                ),
                Align(
                  alignment: (context.isArabic)
                      ? Alignment.bottomLeft
                      : Alignment.bottomRight,
                  child: SvgPicture.asset(data.imageAssetName),
                ),
                Align(
                  alignment: (context.isArabic)
                      ? Alignment.bottomRight
                      : Alignment.bottomLeft,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      data.buttonText,
                      style: TextStyle(
                        color: ColorResource.primary(context.isDarkMode),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _ActivityTemplate {
  final String title;
  final String subtitle;
  final String iconAssetName;
  final String imageAssetName;
  final String buttonText;

  _ActivityTemplate({
    required this.title,
    required this.subtitle,
    required this.iconAssetName,
    required this.imageAssetName,
    required this.buttonText,
  });
}
