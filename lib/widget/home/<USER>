import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/home/<USER>';
import 'package:mushafi/widget/home/<USER>';
import 'package:mushafi/widget/navigation/juz_navigation_list.dart';
import 'package:mushafi/widget/navigation/surah_navigation_list.dart';

class BrowseMushaf extends ConsumerStatefulWidget {
  final Function(int pageNumber, {String? verseKey}) onNavigationItemSelected;
  final VoidCallback onVoiceSearchRequested;

  const BrowseMushaf(this.onNavigationItemSelected, this.onVoiceSearchRequested,
      {super.key});

  @override
  ConsumerState<BrowseMushaf> createState() => _BrowseMushafState();
}

class _BrowseMushafState extends ConsumerState<BrowseMushaf> {
  bool modelCompatible = true;

  @override
  void initState() {
    modelCompatible = PreferenceStorage.getModelCompatible();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            context.tr('home.browse_mushaf'),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
        ),
        if (modelCompatible) ...[
          const SizedBox(height: 8),
          buildVerseRecognitionCard(),
        ],
        Expanded(
          child: DefaultTabController(
            length: 4,
            child: Scaffold(
              backgroundColor:
                  ColorResource.backgroundDefault(context.isDarkMode),
              appBar: TabBar(
                indicatorColor: ColorResource.textDefault(context.isDarkMode),
                labelColor: ColorResource.textDefault(context.isDarkMode),
                unselectedLabelColor:
                    ColorResource.textDefault(context.isDarkMode)
                        .withOpacity(.5),
                tabAlignment: TabAlignment.start,
                isScrollable: true,
                tabs: [
                  Tab(text: context.tr('home.recent_pages')),
                  Tab(text: context.tr('home.surah')),
                  Tab(text: context.tr('home.juz')),
                  Tab(text: context.tr('bookmarks')),
                ],
              ),
              body: TabBarView(
                children: [
                  RecentPageSection(
                    onNavigationItemSelected: widget.onNavigationItemSelected,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: SurahNavigationList(
                      scrollable: true,
                      onNavigationItemSelected: widget.onNavigationItemSelected,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: JuzNavigationList(
                      scrollable: true,
                      onNavigationItemSelected: widget.onNavigationItemSelected,
                    ),
                  ),
                  BookmarkSection(
                    onBookmarkTap: (pageNumber, verseKey) =>
                        widget.onNavigationItemSelected(pageNumber,
                            verseKey: verseKey),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildVerseRecognitionCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Container(
          color: ColorResource.backgroundBlue(context.isDarkMode),
          child: Stack(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    flex: 70,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Transform(
                                alignment: Alignment.center,
                                transform: Matrix4.rotationY(
                                    (context.isArabic) ? pi : 0),
                                child: SvgPicture.asset(
                                    Assets.svgsIcVoiceSelection),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                context.tr('home.verse_recognition.title'),
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            context.tr('home.verse_recognition.description'),
                            textAlign: TextAlign.start,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 16),
                          FilledButton.icon(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(8), // Rounded corners
                              ),
                            ),
                            onPressed: widget.onVoiceSearchRequested,
                            icon: SvgPicture.asset(
                              Assets.svgsIcMic,
                              colorFilter: const ColorFilter.mode(
                                Colors.black,
                                BlendMode.srcIn,
                              ),
                            ),
                            label: Text(
                              context.tr('home.verse_recognition.action'),
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const Spacer(flex: 30),
                ],
              ),
              Positioned.fill(
                child: Align(
                  alignment: (context.isArabic)
                      ? Alignment.bottomLeft
                      : Alignment.bottomRight,
                  child: Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY((context.isArabic) ? pi : 0),
                    child: SvgPicture.asset(Assets.svgsImgVerseRecognition),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
