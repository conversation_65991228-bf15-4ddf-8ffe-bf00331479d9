import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/settings/settings_screen.dart';
import 'package:mushafi/resource/color.dart';
import 'package:mushafi/utils/constants.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:easy_localization/easy_localization.dart';

class HomeDrawer extends StatelessWidget {
  const HomeDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.zero,
      children: [
        _buildHeader(context),
        const Divider(thickness: .2),
        _buildHomeSection(context),
        const Divider(thickness: .2),
        _buildAboutUsSection(context),
        const Divider(thickness: .2),
        _buildSupportUsSection(context),
        const Divider(thickness: .2),
        _buildFollowUsSection(context)
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return SafeArea(
      child: ListTile(
        leading: SvgPicture.asset(Assets.svgsImgMushafiHorizontalDetailed),
        trailing: GestureDetector(
          child: const Icon(Icons.close),
          onTap: () {
            Navigator.pop(context);
          },
        ),
      ),
    );
  }

  Widget _buildHomeSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(context.tr('drawer.home'),
              style: const TextStyle(fontWeight: FontWeight.w700)),
        ),
        ListTile(
          leading: SvgPicture.asset(Assets.svgsIcHome),
          title: Text(
            context.tr('drawer.my_mushaf'),
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: ColorResource.primary,
            ),
          ),
          onTap: () {
            Navigator.pop(context);
          },
        ),
        ListTile(
          leading: SvgPicture.asset(Assets.svgsIcSettings),
          title: Text(
            context.tr('drawer.settings'),
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: ColorResource.textGrey,
            ),
          ),
          onTap: () {
            Navigator.pop(context);

            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const SettingsScreen(),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAboutUsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(context.tr('drawer.about_us'),
              style: const TextStyle(fontWeight: FontWeight.w700)),
        ),
        ListTile(
          leading: SvgPicture.asset(Assets.svgsIcWeb),
          title: Text(
            context.tr('drawer.website'),
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: ColorResource.textGrey,
            ),
          ),
          onTap: () async {
            await launchUrl(Uri.parse(mushafiWebUrl));
          },
        ),
        ListTile(
          leading: SvgPicture.asset(Assets.svgsIcCredit),
          title: Text(
            context.tr('drawer.app_credits'),
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: ColorResource.textGrey,
            ),
          ),
          onTap: () {
            Navigator.pop(context);
          },
        ),
      ],
    );
  }

  Widget _buildSupportUsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(context.tr('drawer.ways_to_support'),
              style: const TextStyle(fontWeight: FontWeight.w700)),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _buildShareButton(context),
              const SizedBox(width: 8),
              _buildReviewButton(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShareButton(BuildContext context) {
    return ElevatedButton.icon(
      style: ButtonStyle(
        backgroundColor:
            const WidgetStatePropertyAll(ColorResource.backgroundGreyLight),
        elevation: const WidgetStatePropertyAll(0),
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
      icon: SvgPicture.asset(Assets.svgsIcForward),
      label: Text(
        context.tr('drawer.share'),
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          color: ColorResource.textDefault,
        ),
      ),
      onPressed: () {
        const text =
            "Take a look at this amazing Quran app!\n\n$mushafiPlayStoreUrl";
        Share.share(text);
      },
    );
  }

  Widget _buildReviewButton(BuildContext context) {
    return Row(
      children: [
        ElevatedButton.icon(
          style: ButtonStyle(
            backgroundColor:
                const WidgetStatePropertyAll(ColorResource.backgroundGreyLight),
            elevation: const WidgetStatePropertyAll(0),
            shape: WidgetStatePropertyAll(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          icon: SvgPicture.asset(Assets.svgsIcStar),
          label: Text(
            context.tr('drawer.review'),
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: ColorResource.textDefault,
            ),
          ),
          onPressed: () async {
            // todo: conditional url for android and ios
            await launchUrl(Uri.parse(mushafiPlayStoreUrl));
          },
        ),
      ],
    );
  }

  Widget _buildFollowUsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(context.tr('drawer.follow_us'),
              style: const TextStyle(fontWeight: FontWeight.w700)),
        ),
        Row(
          children: [
            _buildSocialMediaButton(
              context,
              Assets.svgsIcInstagram,
              mushafiInstagramUrl,
            ),
            _buildSocialMediaButton(
              context,
              Assets.svgsIcFacebook,
              mushafiFacebookUrl,
            ),
            _buildSocialMediaButton(
              context,
              Assets.svgsIcYoutube,
              mushafiYoutubeUrl,
            ),
            _buildSocialMediaButton(
              context,
              Assets.svgsIcTiktok,
              mushafiTiktokUrl,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialMediaButton(
      BuildContext context, String asset, String url) {
    return IconButton(
      icon: SvgPicture.asset(
        asset,
        width: 16,
        height: 16,
      ),
      onPressed: () async {
        await launchUrl(Uri.parse(url));
      },
    );
  }
}
