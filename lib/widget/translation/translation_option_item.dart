import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/string_extension.dart';
import 'package:mushafi/generated/assets.dart';
import 'package:mushafi/presentation/model/translation/lang_code.dart';
import 'package:mushafi/presentation/model/translation/translation.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/utils/common_utils.dart';

class TranslationOptionItem extends ConsumerStatefulWidget {
  final Translation translation;
  final bool isDownloaded;
  final bool isSelected;
  final VoidCallback onSelected;

  const TranslationOptionItem({
    super.key,
    required this.translation,
    required this.isDownloaded,
    required this.isSelected,
    required this.onSelected,
  });

  @override
  ConsumerState<TranslationOptionItem> createState() =>
      _TranslationOptionItemState();
}

class _TranslationOptionItemState extends ConsumerState<TranslationOptionItem> {
  bool isDownloading = false;
  double downloadingProgress = 0;

  @override
  Widget build(BuildContext context) {
    final translation = widget.translation;
    final langCode = LangCode.fromName(translation.language);
    return InkWell(
      onTap: () async {
        if (!widget.isDownloaded && !isDownloading) {
          setState(() {
            isDownloading = true;
            downloadingProgress = 0;
          });

          await ref.read(minioDataRepositoryProvider).getTranslationTafsir(
                fileName: translation.fileName,
                onDownloading: (percentage) {
                  if (mounted) {
                    setState(() {
                      downloadingProgress = percentage;
                    });
                  }
                },
              );

          isDownloading = false;
          downloadingProgress = 0;
          widget.onSelected();
        } else {
          widget.onSelected();
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            if (langCode != null) ...[
              SvgPicture.asset(langCode.flagAsset),
              const SizedBox(width: 16),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    (translation.nativeTranslatedName.isNotEmpty)
                        ? translation.nativeTranslatedName
                        : translation.name,
                    style: TextStyle(
                      fontFamily: '',
                      fontWeight: FontWeight.w500,
                      color: ColorResource.textDefault(context.isDarkMode),
                    ),
                  ),
                  Text(
                    translation.language.toTitleCase,
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorResource.textGrey(context.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
            Directionality(
              textDirection: TextDirection.ltr,
              child: Text(
                CommonUtils.humanReadableKBCountBin(translation.size),
                style: TextStyle(
                    color: ColorResource.textGrey(context.isDarkMode)),
              ),
            ),
            const SizedBox(width: 16),
            if (!widget.isDownloaded)
              SizedBox(
                width: 24,
                height: 24,
                child: (isDownloading)
                    ? Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color:
                              ColorResource.backgroundGrey(context.isDarkMode),
                        ),
                        child: Stack(
                          children: [
                            CircularProgressIndicator(
                                value: downloadingProgress),
                            Center(
                              child: Text(
                                '${(downloadingProgress * 100).toInt()}%',
                                style: TextStyle(
                                  fontSize: 8,
                                  color: ColorResource.textDefault(
                                      context.isDarkMode),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Center(
                        child: SvgPicture.asset(
                          Assets.svgsIcDownload,
                          width: 24,
                          height: 24,
                        ),
                      ),
              )
            else if (widget.isSelected)
              const Icon(
                Icons.check,
                size: 24,
              )
            else
              const SizedBox(width: 24)
          ],
        ),
      ),
    );
  }
}
