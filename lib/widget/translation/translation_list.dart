import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mushafi/data/source/preference_storage.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/extension/string_extension.dart';
import 'package:mushafi/presentation/model/quran/mushaf_design.dart';
import 'package:mushafi/presentation/model/translation/translation.dart';
import 'package:mushafi/presentation/quran/quran_provider.dart';
import 'package:mushafi/provider/repository_provider.dart';
import 'package:mushafi/resource/color_resource.dart';
import 'package:mushafi/widget/translation/translation_option_item.dart';

class TranslationList extends ConsumerStatefulWidget {
  final bool applyMushafTheme;

  const TranslationList({super.key, this.applyMushafTheme = true});

  @override
  ConsumerState<TranslationList> createState() => _TranslationListState();
}

class _TranslationListState extends ConsumerState<TranslationList> {
  String selectedTranslationKey = '';

  List<Translation> allTranslationList = [];
  List<Translation> downloadedTranslationList = [];
  List<Translation> otherTranslationList = [];

  void updateSelectedTranslation() {
    selectedTranslationKey = PreferenceStorage.getDefaultTranslationKey();
  }

  Future<void> filterTranslationList() async {
    if (!mounted) return;

    final List<String> downloadedTranslationFileNameList = await ref
        .read(minioDataRepositoryProvider)
        .getCachedTranslationTafsirNameList();
    final List<String> downloadedTranslationKeyList =
        downloadedTranslationFileNameList
            .map((fileName) => fileName.replaceAll('.fb', ''))
            .toList();

    downloadedTranslationList = [];
    otherTranslationList = [];

    for (final translation in allTranslationList) {
      if (downloadedTranslationKeyList.contains(translation.key)) {
        downloadedTranslationList.add(translation);
      } else {
        otherTranslationList.add(translation);
      }
    }

    downloadedTranslationList.sort(compareTranslations);
    otherTranslationList.sort(compareTranslations);
  }

  int compareTranslations(Translation a, Translation b) {
    final languageComparison =
        a.language.toTitleCase.compareTo(b.language.toTitleCase);
    if (languageComparison != 0) {
      return languageComparison;
    }

    final aTitle =
        (a.nativeTranslatedName.isNotEmpty) ? a.nativeTranslatedName : a.name;
    final bTitle =
        (b.nativeTranslatedName.isNotEmpty) ? b.nativeTranslatedName : b.name;
    return aTitle.compareTo(bTitle);
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      allTranslationList =
          await ref.read(minioDataRepositoryProvider).getTranslationList();

      updateSelectedTranslation();
      await filterTranslationList();

      setState(() {});
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (downloadedTranslationList.isNotEmpty) ...[
          buildDownloadedTranslationList(),
          const SizedBox(height: 8),
        ],
        buildOtherTranslationList(),
      ],
    );
  }

  Widget buildDownloadedTranslationList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          context.tr('translation.downloaded'),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: ColorResource.textDefault(context.isDarkMode),
          ),
        ),
        const SizedBox(height: 8),
        buildTranslationList(downloadedTranslationList, true),
      ],
    );
  }

  Widget buildOtherTranslationList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (downloadedTranslationList.isNotEmpty) ...[
          Text(
            context.tr('translation.other_translations'),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: ColorResource.textDefault(context.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
        ],
        buildTranslationList(otherTranslationList, false),
      ],
    );
  }

  Widget buildTranslationList(
      List<Translation> translationList, bool isDownloaded) {
    final mushafDesignAsync = ref.watch(quranImageAssetProvider);
    final design = mushafDesignAsync.value?.design;
    final backgroundColor = (widget.applyMushafTheme)
        ? design?.backgroundColor ??
            MushafDesign.getDefault(context.isDarkMode).backgroundColor
        : ColorResource.backgroundWhite(context.isDarkMode);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        color: backgroundColor,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.all(0),
        itemCount: translationList.length,
        itemBuilder: (context, index) {
          final translation = translationList[index];
          final selected = translation.key == selectedTranslationKey;
          return TranslationOptionItem(
            translation: translation,
            isDownloaded: isDownloaded,
            isSelected: selected,
            onSelected: () async {
              await PreferenceStorage.saveDefaultTranslationKey(
                  translation.key);

              if (mounted) {
                updateSelectedTranslation();
                await filterTranslationList();
                if (mounted) {
                  setState(() {});
                }
              }
            },
          );
        },
        separatorBuilder: (context, index) {
          return const Divider(thickness: .2, height: 1);
        },
      ),
    );
  }
}
