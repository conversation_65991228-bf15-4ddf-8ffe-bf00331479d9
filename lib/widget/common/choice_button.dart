import 'package:flutter/material.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/resource/color_resource.dart';

class ChoiceButton<T> extends StatelessWidget {
  final Map<String, String> keyLabelMap;
  final String selected;
  final Function(String) onSelectionChanged;

  const ChoiceButton({
    super.key,
    required this.keyLabelMap,
    required this.selected,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: SegmentedButton<String>(
        style: SegmentedButton.styleFrom(
          backgroundColor:
              ColorResource.buttonSegmentBackground(context.isDarkMode),
          foregroundColor: ColorResource.buttonSegmentText(context.isDarkMode),
          selectedBackgroundColor:
              ColorResource.buttonSegmentBackgroundSelected(context.isDarkMode),
          selectedForegroundColor:
              ColorResource.buttonSegmentTextSelected(context.isDarkMode),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          side: BorderSide(color: ColorResource.border(context.isDarkMode)),
        ),
        showSelectedIcon: false,
        segments: keyLabelMap.entries
            .map(
              (entry) => ButtonSegment(
                value: entry.key,
                label: Text(entry.value, textAlign: TextAlign.center),
              ),
            )
            .toList(),
        selected: {selected},
        onSelectionChanged: (Set<String> newSelection) {
          onSelectionChanged(newSelection.first);
        },
      ),
    );
  }
}
