import 'package:flutter/material.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/resource/color_resource.dart';

class VerseKeyCard extends StatelessWidget {
  final String verseKey;
  final VerseKeySize size;

  const VerseKeyCard(
      {super.key, required this.verseKey, this.size = VerseKeySize.normal});

  @override
  Widget build(BuildContext context) {
    final padding = (size == VerseKeySize.normal)
        ? const EdgeInsets.symmetric(vertical: 4.0, horizontal: 6.0)
        : const EdgeInsets.symmetric(vertical: 2.0, horizontal: 4.0);
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        border: Border.all(
            color: ColorResource.border(context.isDarkMode), width: 1.0),
        borderRadius: BorderRadius.circular(8.0),
        color: ColorResource.backgroundDefault(context.isDarkMode),
      ),
      child: Text(
        verseKey,
        style: TextStyle(
          fontSize: size == VerseKeySize.normal ? 14 : 12,
          color: ColorResource.textDefault(context.isDarkMode),
        ),
      ),
    );
  }
}

enum VerseKeySize { normal, small }
