import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class MeasureSize extends SingleChildRenderObjectWidget {
  final ValueChanged<Size> onChange;

  const MeasureSize({super.key, required super.child, required this.onChange});

  @override
  RenderObject createRenderObject(BuildContext context) =>
      _MeasureSizeRenderObject(onChange);
}

class _MeasureSizeRenderObject extends RenderProxyBox {
  final ValueChanged<Size> onChange;
  Size? _lastSize;

  _MeasureSizeRenderObject(this.onChange);

  @override
  void performLayout() {
    super.performLayout();
    if (child != null) {
      final newSize = child!.size;
      if (_lastSize != newSize) {
        _lastSize = newSize;
        WidgetsBinding.instance.addPostFrameCallback((_) => onChange(newSize));
      }
    }
  }
}
