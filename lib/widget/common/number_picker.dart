import 'package:flutter/material.dart';
import 'package:mushafi/extension/context_extension.dart';
import 'package:mushafi/resource/color_resource.dart';

class NumberPicker extends StatefulWidget {
  final int minValue;
  final int maxValue;
  final int value;
  final List<String> displayedValues;
  final FixedExtentScrollController? scrollController;
  final ValueChanged<int> onValueChanged;

  const NumberPicker({
    super.key,
    required this.minValue,
    required this.maxValue,
    required this.value,
    required this.displayedValues,
    this.scrollController,
    required this.onValueChanged,
  });

  @override
  State<NumberPicker> createState() => _NumberPickerState();
}

class _NumberPickerState extends State<NumberPicker> {
  late final FixedExtentScrollController scrollController;

  @override
  void initState() {
    super.initState();
    scrollController = widget.scrollController ??
        FixedExtentScrollController(initialItem: widget.value);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorResource.backgroundGrey(context.isDarkMode),
      height: 150, // Adjust height for better visibility
      child: ShaderMask(
        shaderCallback: (Rect rect) {
          return const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black,
              Colors.black,
              Colors.transparent,
            ],
            stops: [0.0, 0.4, 0.6, 1.0],
          ).createShader(rect);
        },
        blendMode: BlendMode.dstIn,
        child: Stack(
          children: [
            Center(
              child: Container(
                height: 50, // Same as itemExtent
                color: (context.isDarkMode)
                    ? ColorResource.backgroundDefault(context.isDarkMode)
                    : ColorResource.backgroundGreyDark(context.isDarkMode),
              ),
            ),
            ListWheelScrollView.useDelegate(
              diameterRatio: 10,
              // Increase this to flatten the view
              perspective: 0.0001,
              // Reduce the perspective effect
              controller: scrollController,
              itemExtent: 50,
              physics: const FixedExtentScrollPhysics(),
              onSelectedItemChanged: (index) {
                if (index < widget.minValue) {
                  scrollController.animateToItem(
                    widget.minValue,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                  return;
                }
                setState(() {
                  final value = index;
                  widget.onValueChanged(value);
                });
              },
              childDelegate: ListWheelChildBuilderDelegate(
                builder: (context, index) {
                  final value = index;
                  if (value < widget.minValue) {
                    return const SizedBox.shrink();
                  }
                  return GestureDetector(
                    onTap: () {
                      if (value < widget.minValue) return;
                      setState(() {
                        scrollController.animateToItem(
                          index,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                        widget.onValueChanged(value);
                      });
                    },
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        widget.displayedValues[value],
                        style: TextStyle(
                          fontSize: 16,
                          color: ColorResource.textDefault(context.isDarkMode),
                          fontWeight: widget.value == value
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                },
                childCount: widget.displayedValues.length,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      scrollController.dispose();
    }

    super.dispose();
  }
}
