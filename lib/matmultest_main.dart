import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart' as intl;
import 'package:mushafi/utils/inference_pipeline.dart';
import 'package:permission_handler/permission_handler.dart';

// run with this command:
// for Android:
// flutter run --flavor matmultestApp lib/matmultest_main.dart
// for other OS:
// flutter run lib/matmultest_main.dart
void main() {
  runApp(const MatMulTestApp());
}

class MatMulTestApp extends StatefulWidget {
  const MatMulTestApp({super.key});

  @override
  State<MatMulTestApp> createState() => _MatMulTestAppState();
}

class _MatMulTestAppState extends State<MatMulTestApp> {
  late _InferencePipeline inferencePipeline;
  String _result = '';
  String _transcription = '';
  String _compatibilityResult = '';

  @override
  void initState() {
    inferencePipeline = _InferencePipeline();

    Permission.microphone.request().then((status) async {
      if (status.isDenied || status.isPermanentlyDenied) {
        await Fluttertoast.cancel();
        Fluttertoast.showToast(
          msg: 'Microphone permission is required',
          gravity: ToastGravity.BOTTOM,
        );
      }
    });

    super.initState();
  }

  void checkPermission(VoidCallback onGranted) async {
    final permissionStatus = await Permission.microphone.request();
    if (permissionStatus.isGranted) {
      onGranted();
    } else if (permissionStatus.isDenied ||
        permissionStatus.isPermanentlyDenied) {
      await Fluttertoast.cancel();
      Fluttertoast.showToast(
        msg: 'Microphone permission is required',
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  Future<void> _runTest() async {
    final result = inferencePipeline.runMatMulTest();
    setState(() {
      _result = 'Inference Benchmark Results (over ${result.numRuns} runs):\n'
          'Average time: ${result.averageTime.toStringAsFixed(3)} us\n'
          'Min time: ${result.minTime.toStringAsFixed(3)} us\n'
          'Max time: ${result.maxTime.toStringAsFixed(3)} us';
    });
  }

  void _runRecognizeVerse() {
    setState(() {
      _result = 'Listening...';
    });

    inferencePipeline.recognizeVerse(
      onTranscription: (transcription) {
        setState(() {
          _result = transcription;
        });
      },
    );
  }

  void _runCompatibilityTest() {
    final result = inferencePipeline.testCompatibility();
    setState(() {
      _result = 'Compatibility Test Result: $result';
    });
  }

  @override
  Widget build(BuildContext context) {
    final isRtl = intl.Bidi.detectRtlDirectionality(_result);
    return MaterialApp(
      title: 'MatMulTestApp',
      home: Scaffold(
        appBar: AppBar(
          title: const Text('MatMulTestApp'),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Directionality(
                  textDirection:
                      (isRtl) ? TextDirection.rtl : TextDirection.ltr,
                  child: Text(_result, textAlign: TextAlign.center),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    if (Platform.isMacOS || Platform.isLinux) {
                      _runTest();
                    } else {
                      checkPermission(_runTest);
                    }
                  },
                  child: const Text('Run MatMulTest'),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: _runCompatibilityTest,
                  child: const Text('Test Compatibility'),
                ),
                const SizedBox(height: 20),
                GestureDetector(
                  onTapDown: (details) async {
                    setState(() {
                      _result = 'Loading...';
                    });

                    await Future.delayed(Duration(milliseconds: 1));
                    if (Platform.isMacOS || Platform.isLinux) {
                      _runRecognizeVerse();
                    } else {
                      checkPermission(_runRecognizeVerse);
                    }
                  },
                  onTapUp: (details) {
                    inferencePipeline.stopRecognition();
                    setState(() {
                      _result = '';
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.blue,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: const Text(
                      'Recognize Verse (Hold to start)',
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MatMulTestScreen extends StatelessWidget {
  const MatMulTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}

final class MatMulResult extends Struct {
  @Int()
  external int numRuns;

  @Double()
  external double averageTime;

  @Uint64()
  external int minTime;

  @Uint64()
  external int maxTime;
}

typedef NewSelfNativeFunction = Long Function(
  Pointer<Utf8>,
  Pointer<Utf8>,
  Pointer<Utf8>,
  Int,
  Pointer<Utf8>,
);
typedef NewSelfFunction = int Function(
  Pointer<Utf8>,
  Pointer<Utf8>,
  Pointer<Utf8>,
  int,
  Pointer<Utf8>,
);

typedef MatMulTestNativeFunction = MatMulResult Function();
typedef MatMulTestFunction = MatMulResult Function();

// push the models to android temp directory with this command:
// adb push /path/to/models /data/local/tmp
class _InferencePipeline {
  final dylib = Platform.isAndroid
      ? DynamicLibrary.open('libnativelib.so')
      : DynamicLibrary.process();

  final InferencePipeline _pipeline = InferencePipeline.getInstance();

  MatMulResult runMatMulTest() {
    final matMulTest =
        dylib.lookupFunction<MatMulTestNativeFunction, MatMulTestFunction>(
            'benchmarkTest');
    return matMulTest();
  }

  void recognizeVerse({
    required Function(String) onTranscription,
  }) {
    if (!_pipeline.isInitialized) {
      _pipeline.initialize();
    }

    _pipeline.recognizeVerse(
      onListening: (_) {},
      onSpeech: (_) {},
      onTranscription: onTranscription,
      onMatchedVerses: (_) {},
    );
  }

  double testCompatibility() {
    try {
      if (!_pipeline.isInitialized) {
        _pipeline.initialize();
      }

      final testResult = _pipeline.testCompatibility();
      return testResult;
    } catch (e) {
      return 0.0;
    }
  }

  void stopRecognition() {
    _pipeline.reset();
    _pipeline.stop();
  }
}
