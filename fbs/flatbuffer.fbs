//to generate py files run this command:
//flatc --python ~/utils/data/flat_asset/flatbuffer.fbs

namespace flatbuffer;
table Word {
    word_id:int;
    line_number:int;
    text_uthmani:string;
    text_imlaei:string;
    code_v1:string;
    split:bool;
}
table Verse {
    verse_id:int;
    surah_id:int;
    page_id:int;
    //juz_id:int;
    verse_key:string;
    //hizb_number:int;
    //rub_el_hizb_number:int;
    words:[Word];
    audio_segments:[AudioSegment];
    translation_or_tafseer:string;
}
table AudioSegment {
    index:uint32;
    start_time:uint32;
    end_time:uint32;
}
//table Reciter{
  //  reciter_id:int;
  //  name:string;
  //  audio:string;
//}
//table Translation{
  //  translation_id:int
  //  page_Id:int
  //  verse_id:int
  //  translation_data:string
//}
table Page {
    first_verse_id:int;
    last_verse_id:int;
    juz_id:[int];
    surah_id:[int];
    page_id:int;
    font:[ubyte];
}
table Surah {
    surah_id:int;
    starting_page:int;
    name:string; // name for the current language
    surah_type:string;
}
table Juz{
    juz_id:int;
    starting_page:int;
    first_verse:Verse;
    rub_el_hizb:int;
}

table FontFile {
   name:string;
   data:[ubyte];                 // Font data as a byte array
}

table Quran{
   verses:[Verse];
   pages:[Page];
   surahs:[Surah];
   juzs:[Juz];
   other_fonts:[FontFile];          // Repetitive field for other fonts in the Quran
   global_font:[ubyte];
}
root_type Quran;