name: mushafi
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.56+346

environment:
  sdk: '>=3.3.4 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.10+1
  image: ^4.1.7
  collection: ^1.18.0
  easy_localization: ^3.0.5

  flutter_localizations:
    sdk: flutter
  intl: any
  ffi: ^2.1.3
  talker_flutter: ^4.4.1
  url_launcher: ^6.3.0
  share_plus: ^10.0.2
  flat_buffers: ^23.5.26
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  path_provider: ^2.1.4
  just_audio: ^0.9.43
  just_audio_background: ^0.0.1-beta.14
  just_audio_media_kit: ^2.0.6
  media_kit_libs_linux: any
  async: ^2.11.0
  permission_handler: ^11.3.1
  drift: ^2.21.0
  drift_flutter: ^0.1.0
  path: ^1.9.0
  sqlite3: ^2.4.7
  sqlite3_flutter_libs: ^0.5.26
  equatable: ^2.0.5
  json_annotation: ^4.9.0
  package_info_plus: ^8.1.0
  shared_preferences: ^2.3.2
  lottie: ^3.1.3
  toastification: ^2.3.0
  uuid: ^4.5.1
  minio: ^3.5.6
  fast_csv: ^0.2.11
  http: ^1.1.0
  disk_space_2: ^1.0.1
  language_code: ^0.5.5
  wakelock_plus: ^1.2.8
  get_storage: ^2.1.1
  scrollview_observer: ^1.24.0
  flutter_isolate: ^2.1.0
  adaptive_theme: ^3.6.0
  firebase_crashlytics: ^4.3.3
  firebase_analytics: ^11.4.3
  cloud_firestore: ^5.6.4
  firebase_core: ^3.12.0
  upgrader: ^11.3.1
  device_info_plus: ^11.2.2
  firebase_auth: ^5.5.0
  yaml: ^3.1.3
  scroll_to_index: ^3.0.1
  facebook_app_events: ^0.20.1
  asset_delivery: ^1.0.0
  dnsolve: ^1.0.2
  connectivity_plus: ^6.1.3

  flutter_stripe: ^10.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_svg_test: ^1.0.1
  integration_test:
    sdk: flutter

  golden_toolkit: ^0.15.0
  mockito: ^5.4.4
  build_runner: ^2.4.13
  riverpod_generator: ^2.4.3
  custom_lint: ^0.7.0
  riverpod_lint: ^2.3.13
  drift_dev: ^2.21.0
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  fonts:
    - family: SurahNames
      fonts:
        - asset: assets/fonts/sura_names.ttf

    - family: QuranCalligraphy
      fonts:
        - asset: assets/fonts/QCF_BSML.TTF

    - family: ProductSans
      fonts:
        - asset: assets/fonts/product_sans_regular.ttf
        - asset: assets/fonts/product_sans_medium.ttf
          weight: 500
        - asset: assets/fonts/product_sans_bold.ttf
          weight: 700

    - family: NotoNaskhArabic
      fonts:
        - asset: assets/fonts/noto_naskh_arabic_regular.ttf

  # use FlutterAssetsGenerator plugin to generate assets.dart code in lib/generated
  assets:
    - assets/svgs/
    - assets/json/
    - assets/translations/
    - assets/images/
    - assets/yamls/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
