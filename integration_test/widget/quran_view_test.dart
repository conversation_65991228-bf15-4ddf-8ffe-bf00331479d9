import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:mushafi/exception/byte_data_conversion_exception.dart';
import 'package:mushafi/exception/decode_exception.dart';
import 'package:mushafi/presentation/model/quran_canvas/zoom_state.dart';
import 'package:mushafi/utils/method_channel_helper.dart';
import 'package:mushafi/utils/quran_image_helper.dart';
import 'package:mushafi/widget/quran/quran_custom_paint.dart';
import 'package:mushafi/widget/quran/quran_layout.dart';
import 'package:mushafi/widget/quran/quran_view.dart';

import 'quran_view_test.mocks.dart';

// run below command to generate mocks:
// flutter pub run build_runner build
@GenerateMocks([QuranImageHelper])
void main() {
  late MockQuranImageHelper mockQuranImageHelper;

  setUp(() {
    mockQuranImageHelper = MockQuranImageHelper();
  });

  Widget buildApp(MockQuranImageHelper mockQuranImageHelper) {
    return ProviderScope(
      child: MaterialApp(
        home: QuranView(
          mockQuranImageHelper,
        ),
      ),
    );
  }

  testWidgets('when the data is loading, the loading indicator should be shown',
      (tester) async {
    when(mockQuranImageHelper.getQuranThemeAsset())
        .thenAnswer((realInvocation) async {
      await Future.delayed(const Duration(seconds: 5));
      return QuranImageHelper().getQuranThemeAsset();
    });

    final app = buildApp(mockQuranImageHelper);
    await tester.pumpWidget(app);

    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });

  testWidgets('when the data is loaded, the quran canvas should be shown',
      (tester) async {
    when(mockQuranImageHelper.getQuranThemeAsset())
        .thenAnswer((realInvocation) async {
      return QuranImageHelper().getQuranThemeAsset();
    });

    final app = buildApp(mockQuranImageHelper);
    await tester.pumpWidget(app);
    await tester.pumpAndSettle();

    expect(find.byKey(QuranCustomPaint.quranCustomPaintKey), findsOneWidget);
  });

  testWidgets(
      'when the data is loaded and the screen is pinched in on mobile phone, the quran canvas zooming out function should work',
      (tester) async {
    final isMobile = await MethodChannelHelper.isMobile();
    if (isMobile == false) {
      return;
    }

    when(mockQuranImageHelper.getQuranThemeAsset())
        .thenAnswer((realInvocation) async {
      return QuranImageHelper().getQuranThemeAsset();
    });

    final app = buildApp(mockQuranImageHelper);
    await tester.pumpWidget(app);
    await tester.pumpAndSettle();

    expect(find.byKey(QuranCustomPaint.quranCustomPaintKey), findsOneWidget);

    final QuranLayoutState state = tester.state(find.byType(QuranLayout));
    final gestureDetectorWidget =
        find.byKey(QuranLayout.quranGestureDetectorKey);
    final center = tester.getCenter(gestureDetectorWidget);

    // pinch in to zoom out
    TestGesture gesture1 = await tester.startGesture(center.translate(-100, 0));
    TestGesture gesture2 = await tester.startGesture(center.translate(100, 0));
    await gesture1.moveTo(center);
    await gesture2.moveTo(center);
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, lessThan(1));
    expect(state.canvasData.zoomState, ZoomState.zoomedOut);

    // pinch out to zoom back to default zoom
    gesture1 = await tester.startGesture(center.translate(-1, 0));
    gesture2 = await tester.startGesture(center.translate(1, 0));
    await gesture1.moveTo(center.translate(-100, 0));
    await gesture2.moveTo(center.translate(100, 0));
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, 1);
    expect(state.canvasData.zoomState, ZoomState.defaultZoom);
  });

  testWidgets(
      'when the data is loaded and the screen is pinched in on screen other than mobile phone, the quran canvas zooming out function should be ignored',
      (tester) async {
    final isMobile = await MethodChannelHelper.isMobile();
    if (isMobile == true) {
      return;
    }

    when(mockQuranImageHelper.getQuranThemeAsset())
        .thenAnswer((realInvocation) async {
      return QuranImageHelper().getQuranThemeAsset();
    });

    final app = buildApp(mockQuranImageHelper);
    await tester.pumpWidget(app);
    await tester.pumpAndSettle();

    expect(find.byKey(QuranCustomPaint.quranCustomPaintKey), findsOneWidget);

    final QuranLayoutState state = tester.state(find.byType(QuranLayout));
    final gestureDetectorWidget =
        find.byKey(QuranLayout.quranGestureDetectorKey);
    final center = tester.getCenter(gestureDetectorWidget);

    // pinch in to zoom out
    TestGesture gesture1 = await tester.startGesture(center.translate(-100, 0));
    TestGesture gesture2 = await tester.startGesture(center.translate(100, 0));
    await gesture1.moveTo(center);
    await gesture2.moveTo(center);
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, 1);
    expect(state.canvasData.zoomState, ZoomState.defaultZoom);
  });

  testWidgets(
      'when the data is loaded and the screen is pinched out one time, the quran canvas should zoom in to level 1',
      (tester) async {
    when(mockQuranImageHelper.getQuranThemeAsset())
        .thenAnswer((realInvocation) async {
      return QuranImageHelper().getQuranThemeAsset();
    });

    final app = buildApp(mockQuranImageHelper);
    await tester.pumpWidget(app);
    await tester.pumpAndSettle();

    expect(find.byKey(QuranCustomPaint.quranCustomPaintKey), findsOneWidget);

    final QuranLayoutState state = tester.state(find.byType(QuranLayout));
    final gestureDetectorWidget =
        find.byKey(QuranLayout.quranGestureDetectorKey);
    final center = tester.getCenter(gestureDetectorWidget);

    // pinch out to zoom in
    TestGesture gesture1 = await tester.startGesture(center.translate(-1, 0));
    TestGesture gesture2 = await tester.startGesture(center.translate(1, 0));
    await gesture1.moveTo(center.translate(-100, 0));
    await gesture2.moveTo(center.translate(100, 0));
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, greaterThanOrEqualTo(1));
    expect(state.canvasData.rightFrameAlpha, 0);
    expect(state.canvasData.zoomState, ZoomState.zoomedInLevel1);

    // pinch in to zoom back to default zoom
    gesture1 = await tester.startGesture(center.translate(-100, 0));
    gesture2 = await tester.startGesture(center.translate(100, 0));
    await gesture1.moveTo(center);
    await gesture2.moveTo(center);
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, 1);
    expect(state.canvasData.rightFrameAlpha, 1);
    expect(state.canvasData.zoomState, ZoomState.defaultZoom);
  });

  testWidgets(
      'when the data is loaded and the screen is pinched out two times on mobile phone, the quran canvas should zoom in to level 2',
      (tester) async {
    final isMobile = await MethodChannelHelper.isMobile();
    if (isMobile == false) {
      return;
    }

    when(mockQuranImageHelper.getQuranThemeAsset())
        .thenAnswer((realInvocation) async {
      return QuranImageHelper().getQuranThemeAsset();
    });

    final app = buildApp(mockQuranImageHelper);
    await tester.pumpWidget(app);
    await tester.pumpAndSettle();

    expect(find.byKey(QuranCustomPaint.quranCustomPaintKey), findsOneWidget);

    final QuranLayoutState state = tester.state(find.byType(QuranLayout));
    final gestureDetectorWidget =
        find.byKey(QuranLayout.quranGestureDetectorKey);
    final center = tester.getCenter(gestureDetectorWidget);

    // pinch out to zoom in level 1
    TestGesture gesture1 = await tester.startGesture(center.translate(-1, 0));
    TestGesture gesture2 = await tester.startGesture(center.translate(1, 0));
    await gesture1.moveTo(center.translate(-100, 0));
    await gesture2.moveTo(center.translate(100, 0));
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, greaterThanOrEqualTo(1));
    expect(state.canvasData.rightFrameAlpha, 0);
    expect(state.canvasData.zoomState, ZoomState.zoomedInLevel1);

    // pinch out one more time to zoom in level 2
    gesture1 = await tester.startGesture(center.translate(-1, 0));
    gesture2 = await tester.startGesture(center.translate(1, 0));
    await gesture1.moveTo(center.translate(-100, 0));
    await gesture2.moveTo(center.translate(100, 0));
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, greaterThan(1));
    expect(state.canvasData.rightFrameAlpha, 0);
    expect(state.canvasData.zoomState, ZoomState.zoomedInLevel2);

    // pinch in to zoom back to zoom in level 1
    gesture1 = await tester.startGesture(center.translate(-100, 0));
    gesture2 = await tester.startGesture(center.translate(100, 0));
    await gesture1.moveTo(center);
    await gesture2.moveTo(center);
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, greaterThanOrEqualTo(1));
    expect(state.canvasData.rightFrameAlpha, 0);
    expect(state.canvasData.zoomState, ZoomState.zoomedInLevel1);

    // pinch in one more time to zoom back to default zoom
    gesture1 = await tester.startGesture(center.translate(-100, 0));
    gesture2 = await tester.startGesture(center.translate(100, 0));
    await gesture1.moveTo(center);
    await gesture2.moveTo(center);
    await gesture1.up();
    await gesture2.up();
    await tester.pumpAndSettle();

    expect(state.canvasData.rightZoomScale, 1);
    expect(state.canvasData.rightFrameAlpha, 1);
    expect(state.canvasData.zoomState, ZoomState.defaultZoom);
  });

  testWidgets('when the data loading fails, the error text should be shown',
      (tester) async {
    when(mockQuranImageHelper.getQuranThemeAsset())
        .thenAnswer((realInvocation) async {
      throw ByteDataConversionException();
    });

    await expectLater(mockQuranImageHelper.getQuranThemeAsset(),
        throwsA(isA<ByteDataConversionException>()));

    final app = buildApp(mockQuranImageHelper);
    await tester.pumpWidget(app);
    await tester.pumpAndSettle();

    const errorMessageKey = QuranView.errorMessageKey;
    expect(find.byKey(errorMessageKey), findsOneWidget);
  });

  testWidgets('when decode image fails, the error text should be shown',
      (tester) async {
    when(mockQuranImageHelper.getQuranThemeAsset())
        .thenAnswer((realInvocation) async {
      throw DecodeException();
    });

    await expectLater(mockQuranImageHelper.getQuranThemeAsset(),
        throwsA(isA<DecodeException>()));

    final app = buildApp(mockQuranImageHelper);
    await tester.pumpWidget(app);
    await tester.pumpAndSettle();

    const errorMessageKey = QuranView.errorMessageKey;
    expect(find.byKey(errorMessageKey), findsOneWidget);
  });
}
