import 'package:flutter/material.dart';
import 'package:flutter_svg_test/flutter_svg_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mushafi/main.dart';
import 'package:mushafi/presentation/model/introduction_step.dart';
import 'package:mushafi/presentation/onboarding/onboarding_data.dart';

void main() {
  group('onboarding test', () {
    testWidgets(
        'tap on the continue button until the last introduction section, verify each section shows correctly',
        (tester) async {
      await tester.pumpWidget(const MyApp());
      final BuildContext context =
          tester.element(find.byKey(const ValueKey('onboardingPage')));
      var data = getIntroductionStepData(context, IntroductionStep.first);

      expect(find.text(data.title), findsOneWidget);
      expect(find.text(data.description), findsOneWidget);
      expect(find.svgAssetWithPath(data.imageAsset), findsOneWidget);

      final button = find.byKey(const ValueKey('continueButton'));
      await tester.tap(button);
      await tester.pumpAndSettle();

      data = getIntroductionStepData(context, IntroductionStep.second);
      expect(find.text(data.title), findsOneWidget);
      expect(find.text(data.description), findsOneWidget);
      expect(find.svgAssetWithPath(data.imageAsset), findsOneWidget);

      await tester.tap(button);
      await tester.pumpAndSettle();

      data = getIntroductionStepData(context, IntroductionStep.third);
      expect(find.text(data.title), findsOneWidget);
      expect(find.text(data.description), findsOneWidget);
      expect(find.svgAssetWithPath(data.imageAsset), findsOneWidget);
    });
  });
}
