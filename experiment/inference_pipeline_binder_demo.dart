import 'dart:isolate';
import 'dart:math';

import 'package:mushafi/main.dart';
import 'package:mushafi/utils/inference_pipeline.dart';

final receivePort = ReceivePort();

// run with this command:
// flutter run --flavor development experiment/inference_pipeline_binder_demo.dart
void main() {
  // run on another thread/isolate to prevent UI freeze
  Isolate.spawn(_runPipeline, receivePort.sendPort);
}

void _runPipeline(SendPort sendPort) {
  final pipeline = InferencePipeline.getInstance();
  pipeline.initialize();

  const runAlignDemo = false;

  final testCompatibility = pipeline.testCompatibility();
  talker.debug("testCompatibility: $testCompatibility");

  if (runAlignDemo) {
    talker.debug("running align demo...");
    _runAlignDemo(pipeline);
  } else {
    talker.debug("running recognize verse demo...");
    _runRecognizeVerseDemo(pipeline);
  }

  // end after 3 seconds
  Future.delayed(const Duration(seconds: 3)).then((value) {
    final reset = pipeline.reset();
    talker.debug("reset: $reset");
    final stop = pipeline.stop();
    talker.debug("stop: $stop");

    pipeline.delete();
    talker.debug("deleted");
  });
}

void _runAlignDemo(InferencePipeline pipeline) {
  final alignResult = pipeline.align(
    startAyahId: 1,
    endAyahId: 7,
    onListening: (isListening) {
      talker.debug("onListening: $isListening");
    },
    onSpeech: (isSpeech) {
      talker.debug("onSpeech: $isSpeech");
    },
    onTranscription: (transcription) {
      talker.debug("onTranscription: $transcription");
    },
    onAlignmentResult: (ayahId, wordIndex, isCorrect, isComplete) {
      talker.debug(
          "onAlignmentResult: $ayahId, $wordIndex, $isCorrect, $isComplete");
    },
  );
  talker.debug("align: $alignResult");

  final setAlignerCursorResult =
      pipeline.setAlignerCursor(ayahId: 3, wordIndex: 0);
  talker.debug("setAlignerCursor: $setAlignerCursorResult");
}

void _runRecognizeVerseDemo(InferencePipeline pipeline) {
  final recognizeVerseResult = pipeline.recognizeVerse(
    onListening: (isListening) {
      talker.debug("onListening: $isListening");
    },
    onSpeech: (isSpeech) {
      talker.debug("onSpeech: $isSpeech");
    },
    onTranscription: (transcription) {
      talker.debug("onTranscription: $transcription");
    },
    onMatchedVerses: (verses) {
      talker.debug("onMatchedVerses: $verses");
    },
  );
  talker.debug("recognizeVerse: $recognizeVerseResult");
}
