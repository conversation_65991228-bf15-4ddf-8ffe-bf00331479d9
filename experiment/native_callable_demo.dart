import 'dart:ffi';
import 'dart:io';

import 'package:flutter/foundation.dart';

// Define the function signature
typedef NativeSum = Void Function(Int32, Int32);

// Create the Dart function that will be callable from C
void sum(int a, int b) {
  debugPrint('Summing $a and $b in Dart');
}

// Declare the C function we'll call
typedef SetCallbackNativeFunction = Void Function(
    Pointer<NativeFunction<NativeSum>>);
typedef SetCallbackFunction = void Function(Pointer<NativeFunction<NativeSum>>);

// run with this command:
// flutter run experiment/native_callable_demo.dart
void main() {
  runNativeCallableDemo();
}

void runNativeCallableDemo() {
  assert(kDebugMode, 'This code only runs in debug mode');

  // Load the C library
  final dylib = Platform.isAndroid
      ? DynamicLibrary.open('libnative_lib.so')
      : DynamicLibrary.process();

  // Look up the C function
  final setCallback =
      dylib.lookupFunction<SetCallbackNativeFunction, SetCallbackFunction>(
          'setCallback');

  // Create a NativeCallable instance
  final nativeSum = NativeCallable<NativeSum>.listener(sum);

  // Pass the Dart function to C
  setCallback(nativeSum.nativeFunction);

  // Call the C function that will invoke our callback
  final invokeCallback =
      dylib.lookupFunction<Void Function(), void Function()>('invokeCallback');
  invokeCallback();

  // Don't forget to close the NativeCallable when done
  // Closing after 1 second to wait for the callback to finish
  Future.delayed(const Duration(seconds: 1)).then((value) {
    nativeSum.close();
  });
}
