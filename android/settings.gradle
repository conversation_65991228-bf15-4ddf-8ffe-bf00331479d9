pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }
    settings.ext.flutterSdkPath = flutterSdkPath()

    includeBuild("${settings.ext.flutterSdkPath}/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.11.0" apply false
    // START: FlutterFire Configuration
    id "com.google.gms.google-services" version "4.3.15" apply false
    id "com.google.firebase.crashlytics" version "2.8.1" apply false
    // END: FlutterFire Configuration
    id "org.jetbrains.kotlin.android" version "1.9.25" apply false
}

include ":app"
include ':inference_asset'
include ':mushaf_asset'
include ':font_asset:initial_font'
include ':font_asset:all_font'
include ':proto_asset:base_proto'
include ':proto_asset:audio_segment_proto'
include ':tafsir_asset:ar_al_baghawi'
include ':tafsir_asset:ar_al_qurthubi'
include ':tafsir_asset:ar_al_wasit'
include ':tafsir_asset:it_piccardo'
include ':tafsir_asset:ku_asan'
include ':tafsir_asset:nl_keyzer'
include ':tafsir_asset:pt_elhayek'
include ':tafsir_asset:sq_nahi'
include ':tafsir_asset:th_thai'
include ':tafsir_asset:ug_saleh'
include ':tafsir_asset:uz_sodik'
include ":tafsir_asset:ar_jalalayn"
include ":tafsir_asset:ar_muyassar"
include ":tafsir_asset:az_mammadaliyev"
include ":tafsir_asset:az_musayev"
include ":tafsir_asset:bn_hoque"
include ":tafsir_asset:en_saheeh"
include ":tafsir_asset:fr_hamidullah"
include ":tafsir_asset:id_kfqr"
include ":tafsir_asset:ms_basmeih"
include ":tafsir_asset:en_daryabadi"
include ":tafsir_asset:hi_hindi"
include ":tafsir_asset:so_abduh"
include ":tafsir_asset:tr_yildirim"
include ":tafsir_asset:tr_ates"
include ":tafsir_asset:id_jalalayn"
include ':inference_pipeline'
include ":tafsir_asset:tafsir_index"
include ':model_asset:model_stride_2'
include ':model_asset:model_stride_3'
include ':model_asset:model_stride_4'
include ':model_asset:model_stride_2_unquantized'
include ':model_asset:cond_model'
include ':model_asset:cond_model_unquantized'
