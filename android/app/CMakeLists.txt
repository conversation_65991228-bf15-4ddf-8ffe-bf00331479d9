cmake_minimum_required(VERSION 3.18.1)
project(nativelib)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

add_library(onnxruntime SHARED IMPORTED)
set_target_properties(
  onnxruntime
  PROPERTIES
  IMPORTED_LOCATION
  ${PROJECT_SOURCE_DIR}/../../libquranasr/onnxruntime/Android/${ANDROID_ABI}/MinSizeRel/lib/libonnxruntime.so)
include_directories(
  ${PROJECT_SOURCE_DIR}/../../libquranasr/onnxruntime/Android/${ANDROID_ABI}/MinSizeRel/include)

add_library(quranasr SHARED IMPORTED)
set_target_properties(
  quranasr
  PROPERTIES
  IMPORTED_LOCATION
  ${PROJECT_SOURCE_DIR}/../../libquranasr/prebuilt/Android/${ANDROID_ABI}/Release/libquranasr.so)
include_directories(
  ${PROJECT_SOURCE_DIR}/../../libquranasr/prebuilt/Android/${ANDROID_ABI}/Release/includes)

add_library(benchmark SHARED IMPORTED)
set_target_properties(
  benchmark
  PROPERTIES
  IMPORTED_LOCATION
  ${PROJECT_SOURCE_DIR}/../../libbenchmark/prebuilt/Android/${ANDROID_ABI}/Release/libbenchmark.so)
include_directories(
  ${PROJECT_SOURCE_DIR}/../../libbenchmark/prebuilt/Android/${ANDROID_ABI}/Release/includes)

add_library(miniaudio SHARED IMPORTED)
set_target_properties(
  miniaudio
  PROPERTIES
  IMPORTED_LOCATION
  ${PROJECT_SOURCE_DIR}/../../libminiaudio/prebuilt/Android/${ANDROID_ABI}/Release/libminiaudio.so)
include_directories(
  ${PROJECT_SOURCE_DIR}/../../libminiaudio/prebuilt/Android/${ANDROID_ABI}/Release/includes)

add_library(
  nativelib

  SHARED

  # Provides a relative path to your source file(s).
  ../../nativelib/inference_pipeline_binder.cpp
)

find_library(
  log-lib
  log
)

target_link_libraries(
  nativelib

  miniaudio
  benchmark
  quranasr
  onnxruntime

  # Links the target library to the log library
  # included in the NDK.
  ${log-lib}
)
