plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystorePropertiesFile.withReader('UTF-8') { reader ->
        keystoreProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def getGitHash = { ->
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-parse', '--short', 'HEAD'
        standardOutput = stdout
    }
    return stdout.toString().trim()
}

android {
    namespace "my.huda.quranapp"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        matmultestApp.manifest.srcFile 'src/matmultest/AndroidManifest.xml'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "my.huda.quranapp"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 29
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName + "-" + getGitHash()

        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64'
        }

        externalNativeBuild {
            cmake {
                cppFlags "-std=c++14", "-Os"
                arguments "-DANDROID_STL=c++_shared"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release

            firebaseCrashlytics {
                // Enable processing and uploading of native symbols to Firebase servers.
                // By default, this is disabled to improve build speeds.
                // This flag must be enabled to see properly-symbolicated native
                // stack traces in the Crashlytics dashboard.
                nativeSymbolUploadEnabled true
            }
        }
    }

    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }

    flavorDimensions "default"

    productFlavors {
        development {
            applicationIdSuffix ".dev"
        }

        production {
        }

        matmultestApp {
            applicationId "my.huda.matmultestapp"
        }
    }

    assetPacks = [":font_asset:initial_font", ":font_asset:all_font", ":proto_asset:base_proto", ":proto_asset:audio_segment_proto", ":tafsir_asset:ar_al_baghawi", ":tafsir_asset:ar_al_qurthubi", ":tafsir_asset:ar_al_wasit", ":tafsir_asset:it_piccardo", ":tafsir_asset:ku_asan", ":tafsir_asset:nl_keyzer", ":tafsir_asset:pt_elhayek", ":tafsir_asset:sq_nahi", ":tafsir_asset:th_thai", ":tafsir_asset:ug_saleh", ":tafsir_asset:uz_sodik", ":tafsir_asset:ar_jalalayn", ":tafsir_asset:ar_muyassar", ":tafsir_asset:az_mammadaliyev", ":tafsir_asset:az_musayev", ":tafsir_asset:bn_hoque", ":tafsir_asset:en_saheeh", ":tafsir_asset:fr_hamidullah", ":tafsir_asset:id_kfqr", ":tafsir_asset:ms_basmeih", ":tafsir_asset:en_daryabadi", ":tafsir_asset:hi_hindi", ":tafsir_asset:so_abduh", ":tafsir_asset:tr_yildirim", ":tafsir_asset:tr_ates", ":tafsir_asset:id_jalalayn", ":inference_pipeline", ":tafsir_asset:tafsir_index", ":model_asset:model_stride_2", ":model_asset:model_stride_3", ":model_asset:model_stride_4", ":model_asset:model_stride_2_unquantized", ":model_asset:cond_model", ":model_asset:cond_model_unquantized", ":mushaf_asset", ":inference_asset"]
}

flutter {
    source '../..'
}

dependencies {
    // Play Asset Delivery
    implementation "com.google.android.play:asset-delivery-ktx:2.3.0"
    implementation 'com.google.android.material:material:1.11.0'
}
